
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 0;
const board = [0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,1],],'p':0.0602,'wl':-0.3011,'ssM':-0.50,'wlRad':0.0420,'sRad':1.18,'v':51,'av':51,},{'xy':[[1,0],],'p':0.7137,'wl':0.1654,'ssM':0.91,'wlRad':0.0553,'sRad':1.44,'v':109,'av':109,},{'xy':[[0,1],],'p':0.4899,'wl':0.2875,'ssM':0.38,'wlRad':0.0416,'sRad':0.72,'v':49,'av':49,},{'xy':[[1,1],],'p':0.2735,'wl':0.2545,'ssM':0.84,'wlRad':0.0508,'sRad':0.79,'v':34,'av':34,},{'xy':[[3,2],],'p':0.3163,'wl':0.6418,'ssM':0.41,'wlRad':0.1118,'sRad':1.54,'v':51,'av':51,},{'xy':[[3,3],],'p':0.3866,'wl':0.6964,'ssM':0.78,'wlRad':0.0200,'sRad':0.85,'v':166,'av':166,},{'xy':[[0,0],],'p':0.0948,'wl':0.7718,'ssM':0.31,'wlRad':0.0626,'sRad':1.26,'v':71,'av':71,},{'move':'other','p':0.2610,'wl':0.9556,'ssM':-0.28,'wlRad':0.0069,'sRad':0.72,'v':14,'av':14,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
