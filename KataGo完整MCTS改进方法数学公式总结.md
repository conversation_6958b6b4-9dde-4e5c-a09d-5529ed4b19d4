# KataGo完整MCTS改进方法数学公式总结（基于最新文档）

## 1. AlphaZero基础公式

### 1.1 原始PUCT选择公式
$$\text{PUCT}(c) = V(c) + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$$

### 1.2 原始价值更新
$$V(c) = \frac{1}{N(c)} \sum_{i=1}^{N(c)} v_i$$

### 1.3 原始Dirichlet噪声
$$P'(a) = (1-\epsilon)P(a) + \epsilon \cdot \text{Dir}(\alpha)$$
其中 $\epsilon = 0.25$，$\alpha = 0.03$ 对所有合法移动均匀分布

## 2. KataGo论文中的MCTS改进

### 2.1 首次游戏紧迫性(FPU) - 解决未探索节点价值估计问题
**核心思想**：为未访问的子节点提供保守的价值估计，避免过度乐观
**解决问题**：防止未探索节点因价值未定义而影响搜索质量

当 $N(c) = 0$ 时：
$$V(c) = V(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}}$$
其中：
- $P_{\text{explored}} = \sum_{c': N(c')>0} P(c')$
- $c_{\text{FPU}} = 0.2$（非根节点），$c_{\text{FPU}} = 0$（根节点启用噪声时）

### 2.2 强制模拟次数 - 确保探索覆盖度
**核心思想**：强制探索有一定策略概率的移动，防止好移动被过早放弃
**解决问题**：Dirichlet噪声移动可能因初始评估差而得不到充分探索

$$n_{\text{forced}}(c) = \left(kP(c) \sum_{c'} N(c')\right)^{1/2}$$
其中 $k = 2$

### 2.3 策略目标剪枝 - 解耦MCTS动态与策略学习
**核心思想**：从策略目标中移除强制探索产生的"噪音"，只保留真正有价值的探索
**解决问题**：避免策略网络学习到强制探索产生的低质量移动分布

对于非最优子节点 $c \neq c^*$，减去强制模拟次数，条件是不导致：
$$\text{PUCT}(c) \geq \text{PUCT}(c^*)$$

### 2.4 模拟上限随机化 - 平衡策略与价值学习
**核心思想**：用少量高质量搜索训练策略，用大量低质量搜索训练价值
**解决问题**：策略学习需要深度搜索，价值学习需要更多游戏样本的矛盾

- 比例 $p = 0.25$ 的回合使用完整搜索（上限 $N$ 个节点）
- 其余回合使用快速搜索（上限 $n$ 个节点）
- $(N, n)$ 从 $(600, 100)$ 退火至 $(1000, 200)$

### 2.5 综合效用函数 - 多目标优化
**核心思想**：同时优化胜负和得分差，提高分析质量和让子棋表现
**解决问题**：纯胜负优化在优势/劣势局面下缺乏精细指导

$$U_{\text{total}} = u_{\text{win}}(x) + u_{\text{score}}(x)$$

其中：
$$u_{\text{win}}(x) = \text{sign}(x) \in \{-1, 1\}$$

$$u_{\text{score}}(x) = c_{\text{score}} f\left(\frac{x - x_0}{b}\right)$$

$$f(x) = \frac{2}{\pi} \arctan(x)$$

得分效用期望：
$$E[u_{\text{score}}] = \int_{-\infty}^{\infty} u_{\text{score}}(x) \mathcal{N}(x; \hat{\mu}_s, \hat{\sigma}_s^2) dx$$

参数：$c_{\text{score}} = 0.4$，$x_0 = \hat{\mu}_s$（根节点预测得分均值）

## 3. KataGo文档中的高级MCTS改进

### 3.1 形状化Dirichlet噪声 - 智能探索引导
**核心思想**：将噪声集中在策略概率相对较高的移动上，而非完全随机
**解决问题**：均匀噪声大多浪费在明显的坏移动上，真正的盲点移动探索不足

$$\alpha_a = \alpha_{\text{uniform}} + \alpha_{\text{shaped}} \cdot \max(0, \log P(a) - \log P_{\text{threshold}})$$

其中：
- $\alpha_{\text{uniform}} = \frac{\alpha_{\text{total}}}{2 \cdot |\text{legal moves}|}$
- $\alpha_{\text{shaped}} = \frac{\alpha_{\text{total}}}{2 \cdot \sum_b \max(0, \log P(b) - \log P_{\text{threshold}})}$
- $\alpha_{\text{total}} = 10.83$

### 3.2 根节点策略Softmax温度 - 对抗策略过早收敛
**核心思想**：在根节点软化策略分布，为MCTS提供反向压力
**解决问题**：PUCT公式天然倾向于让最优移动变得更优，即使没有证据支持

$$P_{\text{temp}}(a) = \frac{\exp(\text{logit}(a)/T)}{\sum_b \exp(\text{logit}(b)/T)}$$

温度衰减：
$$T = T_{\text{early}} \cdot \exp\left(-\frac{\text{turn}}{\text{halflife}}\right) + T_{\text{late}}$$

参数：$T_{\text{early}} = 1.25$，$T_{\text{late}} = 1.1$，$\text{halflife} = \text{board\_size}$

### 3.3 策略惊喜加权 - 加速盲点学习
**核心思想**：增加"惊喜"位置的训练频率，加速对网络盲点的学习
**解决问题**：网络盲点在训练数据中占比极小，学习缓慢

训练样本频率权重重分配：
- 基础权重：$0.5$
- 惊喜权重：$0.5 \times \frac{\text{KL}(P_{\text{prior}} \| P_{\text{target}})}{\sum_{\text{all}} \text{KL}(P_{\text{prior}} \| P_{\text{target}})}$

### 3.4 子树价值偏差纠正 - 在线偏差学习
**核心思想**：学习并纠正神经网络在特定局部模式下的系统性偏差
**解决问题**：神经网络对某些战术的初始判断可能持续偏差，影响搜索效率

节点效用递归定义：
$$\text{MCTSUtility}(n) = \frac{\text{NodeUtility}(n) + \sum_c \text{MCTSUtility}(c) \cdot \text{Visits}(c)}{1 + \sum_c \text{Visits}(c)}$$

偏差纠正的节点效用：
$$\text{NodeUtility}(n) = \text{NNUtility}(n) - \lambda \cdot \text{ObsBias}(B(n))$$

观测误差：
$$\text{ObsError}(n) = \text{NNUtility}(n) - \frac{\sum_c \text{MCTSUtility}(c) \cdot \text{Visits}(c)}{\sum_c \text{Visits}(c)}$$

桶偏差：
$$\text{ObsBias}(B) = \frac{\sum_{n \in B} \text{ObsError}(n) \cdot \text{ChildVisits}(n)^\alpha}{\sum_{n \in B} \text{ChildVisits}(n)^\alpha}$$

参数：$\lambda = 0.35$，$\alpha = 0.8$

### 3.5 动态方差缩放cPUCT - 自适应探索强度
**核心思想**：根据位置的不确定性动态调整探索强度
**解决问题**：固定cPUCT无法适应不同位置的探索需求差异

$$c_{\text{PUCT}}(n) = c_{\text{base}} \cdot \sqrt{\text{Var}_{\text{utility}}(n) + \epsilon_{\text{var}}}$$

效用方差：
$$\text{Var}_{\text{utility}}(n) = \frac{\sum_{i=1}^{N(n)} w_i (V_i^{\text{corrected}} - Q_{\text{weighted}}(n))^2}{\sum_{i=1}^{N(n)} w_i}$$

### 3.6 短期价值和得分目标 - 降低方差的辅助学习
**核心思想**：用短期目标提供低方差的学习信号，加速收敛
**解决问题**：最终游戏结果信号方差大、反馈延迟长

短期目标（指数加权平均）：
$$\text{ShortTermTarget} = (1-\lambda) \sum_{t' \geq t} \text{MCTSValue}(t') \lambda^{(t'-t)}$$

其中 $\lambda \approx 5/6$（19×19棋盘），较小棋盘按面积比例调整

### 3.7 不确定性加权MCTS模拟 - 置信度感知搜索
**核心思想**：根据神经网络的置信度加权不同模拟的贡献
**解决问题**：所有模拟等权重平均忽略了评估质量的差异

加权价值计算：
$$Q_{\text{weighted}}(a) = \frac{\sum_{i=1}^{N(a)} w_i V_i^{\text{corrected}}}{\sum_{i=1}^{N(a)} w_i}$$

不确定性权重：
$$w_i = \frac{1}{\max(\epsilon, \text{UncertaintyPred}_i)}$$

不确定性预测：
$$\text{UncertaintyPred} = \sqrt{\text{UncertaintyPred}_{\text{value}} + \text{UncertaintyPred}_{\text{score}}}$$

其中：
$$\text{UncertaintyPred}_{\text{value}} = \mathbb{E}[(\text{shortterm\_value\_pred} - \text{shortterm\_value\_outcome})^2]$$

加权访问次数：
$$N_{\text{weighted}}(a) = \sum_{i=1}^{N(a)} w_i$$

### 3.8 乐观策略 - 战术发现优化
**核心思想**：训练专门用于发现意外好结果的乐观策略
**解决问题**：标准策略可能错过低概率但高价值的战术机会

乐观策略训练权重：
$$w_{\text{opt}} = \text{clamp}(0, 1, \text{sigmoid}((z_{\text{value}} - 1.5) \times 3) + \text{sigmoid}((z_{\text{score}} - 1.5) \times 3))$$

标准化惊喜度：
$$z_{\text{value}} = \frac{\text{shortterm\_value\_outcome} - \text{shortterm\_value\_pred}}{\sqrt{\text{shortterm\_value\_error\_pred} + \epsilon}}$$

$$z_{\text{score}} = \frac{\text{shortterm\_score\_outcome} - \text{shortterm\_score\_pred}}{\sqrt{\text{shortterm\_score\_error\_pred} + \epsilon}}$$

## 4. KataGo最终完整MCTS选择公式

### 4.1 核心选择公式
$$\boxed{
\text{Next action} = \arg\max_a \left[ Q_{\text{weighted}}(a) + c_{\text{dynamic}}(n) \cdot P_{\text{final}}(a) \frac{\sqrt{\sum_b N_{\text{weighted}}(b)}}{1 + N_{\text{weighted}}(a)} \right]
}$$

### 4.2 加权价值函数 $Q_{\text{weighted}}(a)$
$$Q_{\text{weighted}}(a) = \frac{\sum_{i=1}^{N(a)} w_i \cdot V_i^{\text{total}}}{\sum_{i=1}^{N(a)} w_i}$$

其中总价值 $V_i^{\text{total}}$ 包含：
$$V_i^{\text{total}} = V_i^{\text{corrected}} + E[u_{\text{score}}^{(i)}]$$

#### 4.2.1 偏差纠正价值 $V_i^{\text{corrected}}$
$$V_i^{\text{corrected}} = V_i^{\text{raw}} - \lambda \cdot \text{ObsBias}(B(n_i))$$

其中：
- $V_i^{\text{raw}}$：第$i$次模拟的原始神经网络价值评估
- $\lambda = 0.35$：偏差纠正强度
- $\text{ObsBias}(B(n_i))$：节点$n_i$所属桶的观测偏差

#### 4.2.2 得分效用期望 $E[u_{\text{score}}^{(i)}]$
$$E[u_{\text{score}}^{(i)}] = c_{\text{score}} \int_{-\infty}^{\infty} \frac{2}{\pi} \arctan\left(\frac{x - \hat{\mu}_s^{(i)}}{b}\right) \mathcal{N}(x; \hat{\mu}_s^{(i)}, (\hat{\sigma}_s^{(i)})^2) dx$$

其中：
- $c_{\text{score}} = 0.4$：得分重要性权重
- $\hat{\mu}_s^{(i)}$：第$i$次模拟的预测得分均值
- $\hat{\sigma}_s^{(i)}$：第$i$次模拟的预测得分标准差
- $b$：棋盘尺寸

#### 4.2.3 不确定性权重 $w_i$
$$w_i = \frac{1}{\max(\epsilon_{\text{unc}}, \sqrt{\text{UncertaintyPred}_{\text{value}}^{(i)} + \text{UncertaintyPred}_{\text{score}}^{(i)}})}$$

其中：
- $\epsilon_{\text{unc}} = 0.01$：数值稳定性参数
- $\text{UncertaintyPred}_{\text{value}}^{(i)}$：第$i$次模拟的价值不确定性预测
- $\text{UncertaintyPred}_{\text{score}}^{(i)}$：第$i$次模拟的得分不确定性预测

### 4.3 动态探索系数 $c_{\text{dynamic}}(n)$
$$c_{\text{dynamic}}(n) = c_{\text{base}} \cdot \sqrt{\text{Var}_{\text{utility}}(n) + \epsilon_{\text{var}}}$$

其中：
- $c_{\text{base}} = 1.1$：基础PUCT常数
- $\epsilon_{\text{var}} = 0.25$：方差下界

效用方差：
$$\text{Var}_{\text{utility}}(n) = \frac{\sum_{i=1}^{N(n)} w_i (V_i^{\text{total}} - Q_{\text{weighted}}(n))^2}{\sum_{i=1}^{N(n)} w_i}$$

### 4.4 最终策略概率 $P_{\text{final}}(a)$
$$P_{\text{final}}(a) = (1-\epsilon_{\text{noise}}) \cdot P_{\text{temp}}^{\text{opt}}(a) + \epsilon_{\text{noise}} \cdot \text{Dir}(\boldsymbol{\alpha}_{\text{shaped}})$$

其中：
- $\epsilon_{\text{noise}} = 0.25$：噪声混合比例

#### 4.4.1 温度缩放的乐观策略 $P_{\text{temp}}^{\text{opt}}(a)$
$$P_{\text{temp}}^{\text{opt}}(a) = \frac{\exp(\text{logit}_{\text{opt}}(a)/T)}{\sum_b \exp(\text{logit}_{\text{opt}}(b)/T)}$$

温度衰减：
$$T = T_{\text{early}} \cdot \exp\left(-\frac{\text{turn}}{\text{board\_size}}\right) + T_{\text{late}}$$

参数：$T_{\text{early}} = 1.25$，$T_{\text{late}} = 1.1$

#### 4.4.2 形状化Dirichlet噪声 $\text{Dir}(\boldsymbol{\alpha}_{\text{shaped}})$
$$\alpha_a^{\text{shaped}} = \alpha_{\text{uniform}} + \alpha_{\text{shaped}} \cdot \max(0, \log P_{\text{temp}}^{\text{opt}}(a) - \log P_{\text{threshold}})$$

其中：
- $\alpha_{\text{uniform}} = \frac{\alpha_{\text{total}}}{2 \cdot |\text{legal moves}|}$
- $\alpha_{\text{shaped}} = \frac{\alpha_{\text{total}}}{2 \cdot \sum_b \max(0, \log P_{\text{temp}}^{\text{opt}}(b) - \log P_{\text{threshold}})}$
- $\alpha_{\text{total}} = 10.83$
- $P_{\text{threshold}} = 0.02$

### 4.5 加权访问次数 $N_{\text{weighted}}(a)$
$$N_{\text{weighted}}(a) = \sum_{i=1}^{N(a)} w_i$$

### 4.6 强制探索约束（来自论文）
当满足以下条件时，设置 $\text{PUCT}(a) = +\infty$：
$$N_{\text{weighted}}(a) < \left(k \cdot P_{\text{final}}(a) \sum_{b} N_{\text{weighted}}(b)\right)^{1/2}$$

其中 $k = 2$

### 4.7 首次游戏紧迫性（来自论文）
对于未访问的子节点（$N(a) = 0$），价值估计为：
$$Q_{\text{weighted}}(a) = Q_{\text{weighted}}(\text{parent}) - c_{\text{FPU}} \sqrt{P_{\text{explored}}}$$

其中：
- $c_{\text{FPU}} = 0.2$（非根节点），$c_{\text{FPU}} = 0$（根节点启用噪声时）
- $P_{\text{explored}} = \sum_{b: N(b)>0} P_{\text{final}}(b)$

## 5. 改进效果总结

| 改进方法              | 核心解决问题       | 预估效果                   |
| --------------------- | ------------------ | -------------------------- |
| **FPU**               | 未探索节点价值估计 | 搜索稳定性提升             |
| **强制模拟+策略剪枝** | 探索-学习解耦      | 盲点发现能力+30-50 Elo     |
| **模拟上限随机化**    | 策略-价值学习平衡  | 训练效率提升50倍           |
| **综合效用函数**      | 多目标优化         | 分析质量显著提升           |
| **形状化噪声**        | 智能探索引导       | 盲点发现效率提升           |
| **策略温度**          | 过早收敛防止       | 探索多样性提升             |
| **策略惊喜加权**      | 盲点学习加速       | 学习速度提升               |
| **子树偏差纠正**      | 系统性偏差消除     | +30-60 Elo                 |
| **动态cPUCT**         | 自适应探索         | +25 Elo                    |
| **不确定性加权**      | 置信度感知         | +50 Elo（与动态cPUCT结合） |
| **乐观策略**          | 战术发现优化       | +40-90 Elo                 |

**总体效果**：相比AlphaZero，KataGo在相同计算资源下实现了约10-50倍的学习效率提升，并在实际对弈中展现出更强的战术发现能力和分析精度。