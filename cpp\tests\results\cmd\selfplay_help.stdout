
DESCRIPTION: 

   Generate training data via self play.

USAGE: 

   selfplay  -config <FILE> ...  [-override-config <KEYVALUEPAIRS>] ... 
             -models-dir <DIR> -output-dir <DIR> [-max-games-total
             <NGAMES>]


Where: 

   -config <FILE>  (accepted multiple times)
     (required)  Config file(s) to use, can be one or multiple files.

   -override-config <KEYVALUEPAIRS>  (accepted multiple times)
     Override config parameters. Format: "key=value, key=value,..."

   -models-dir <DIR>
     (required)  Dir to poll and load models from

   -output-dir <DIR>
     (required)  Dir to output files

   -max-games-total <NGAMES>
     Terminate after this many games

   --,  -ignore_rest
     Ignores the rest of the labeled arguments following this flag.

   -version
     Displays version information and exits.

   -h,  -help
     Displays usage information and exits.



