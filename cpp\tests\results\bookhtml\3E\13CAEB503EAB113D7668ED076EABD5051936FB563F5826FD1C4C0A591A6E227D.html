
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../31/F17EA86A31F03431F3D61EC3DFDB2CEDB673834A312D577285070E44F6822F41.html';
const pSym = 0;
const board = [0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,1],],'p':0.6912,'wl':-0.4888,'ssM':-0.13,'wlRad':0.0535,'sRad':0.40,'v':73,'av':73,},{'xy':[[0,2],],'p':0.4485,'wl':0.2961,'ssM':0.57,'wlRad':0.0146,'sRad':0.34,'v':53,'av':53,},{'xy':[[1,3],],'p':0.6461,'wl':0.4285,'ssM':-0.32,'wlRad':0.1519,'sRad':1.25,'v':130,'av':130,},{'xy':[[0,0],],'p':0.3968,'wl':0.3576,'ssM':1.04,'wlRad':0.0049,'sRad':0.42,'v':17,'av':17,},{'xy':[[1,1],],'p':0.4320,'wl':0.4560,'ssM':0.43,'wlRad':0.1149,'sRad':0.91,'v':16,'av':16,},{'xy':[[1,2],],'p':0.1590,'wl':0.5217,'ssM':-0.09,'wlRad':0.4798,'sRad':1.10,'v':31,'av':31,},{'xy':[[1,0],],'p':0.6335,'wl':0.8996,'ssM':-0.16,'wlRad':0.1044,'sRad':0.80,'v':95,'av':95,},{'move':'other','p':0.1343,'wl':-0.5576,'ssM':-0.01,'wlRad':0.1111,'sRad':1.20,'v':19,'av':19,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
