
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../2B/135745CA2B281C44800BA382F7B53922A1C4DFD2636DB69FFF0F3CF6A3B04901.html';
const pSym = 0;
const board = [0,0,0,0,0,2,0,0,0,0,1,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,2],],'p':0.4833,'wl':-0.3658,'ssM':0.42,'wlRad':0.0235,'sRad':1.25,'v':11,'av':11,},{'xy':[[2,0],],'p':0.8291,'wl':-0.2978,'ssM':0.27,'wlRad':0.1018,'sRad':0.75,'v':16,'av':16,},{'xy':[[0,0],],'p':0.2511,'wl':-0.1229,'ssM':-0.04,'wlRad':0.2538,'sRad':0.44,'v':15,'av':15,},{'xy':[[1,3],],'p':0.2379,'wl':0.2604,'ssM':-0.18,'wlRad':0.0178,'sRad':1.30,'v':13,'av':13,},{'xy':[[3,3],],'p':0.9112,'wl':0.3529,'ssM':0.30,'wlRad':0.1084,'sRad':0.92,'v':126,'av':126,},{'xy':[[3,2],],'p':0.9197,'wl':0.5223,'ssM':-1.16,'wlRad':0.1582,'sRad':0.49,'v':15,'av':15,},{'xy':[[2,3],],'p':0.7016,'wl':0.8055,'ssM':1.41,'wlRad':0.1386,'sRad':0.79,'v':83,'av':83,},{'move':'pass','p':0.6145,'wl':0.8239,'ssM':1.30,'wlRad':0.0368,'sRad':0.40,'v':127,'av':127,},{'move':'other','p':0.0162,'wl':-0.3689,'ssM':0.23,'wlRad':0.0142,'sRad':1.79,'v':20,'av':20,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
