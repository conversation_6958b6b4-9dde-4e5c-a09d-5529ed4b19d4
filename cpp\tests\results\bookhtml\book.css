


body {
  font-family: BlinkMacSystemFont,-apple-system,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Fira Sans","Droid Sans","Helvetica Neue",Helvetica,Arial,sans-serif;
  font-weight:500;
}

a:link { text-decoration: none; color:#485fc7 }
a:visited { text-decoration: none; color:rgb(85, 26, 139) }
a:hover { text-decoration: none; color:#000000 }
a:active { text-decoration: none; color:#ff0055 }

svg {
  font-family:sans-serif;
  font-weight:400;
}

h1 {
  margin-top:10px;
  margin-bottom:10px;
}

.backLink {
  margin-top:6px;
  margin-bottom:6px;
}

.moveTable {
  display: table;
  border-style: solid;
  border-width: 1px;
  border-collapse: collapse;
}

.moveTableHeader {
  display: table-row;
  font-weight: bold;
  /*
  border-style: solid;
  border-width: 1px;
  border-color: black;
  */
}

.moveTableRow {
  display: table-row;
  /*
  border-style: solid;
  border-width: 1px;
  border-color: black;
  */
}

.moveTableRowLinked:link { text-decoration: none; color: rgb(0, 0, 238) }
.moveTableRowLinked:visited { text-decoration: none; color: rgb(85, 26, 139) }
.moveTableRowLinked:hover { text-decoration: none; }
.moveTableRowLinked:active { text-decoration: none; color:#ff0000 }

.moveTableCell {
  display: table-cell;
  padding: 10px;
  text-decoration:none;
}

.moveTableRow:hover {
  outline-style: solid;
  outline-width: 1px;
  outline-color: black;
}
.moveTableRow.moveHovered {
  outline-style: solid;
  outline-width: 1px;
  outline-color: black;
}

#whoToPlay {
  padding:10px;
}

.stoneShadow {
  display: block;
  opacity: 0.001;
}
.stoneShadow.tableHovered {
  display: block;
  opacity: 0.3;
}
.stoneShadowUnhoverable {
  display: block;
  opacity: 0.001;
}
.stoneShadowUnhoverable.tableHovered {
  display: block;
  opacity: 0.3;
}
.stoneShadow:hover {
  display: block;
  opacity: 0.3;
}

.legend {
  padding-top:10px;
}
.legend ul {
  margin-top:0.5em;
}

