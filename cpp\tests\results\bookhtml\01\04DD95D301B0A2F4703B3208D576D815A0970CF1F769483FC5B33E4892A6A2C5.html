
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../2B/135745CA2B281C44800BA382F7B53922A1C4DFD2636DB69FFF0F3CF6A3B04901.html';
const pSym = 5;
const board = [0,0,0,0,0,0,0,0,2,1,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,3],],'p':0.0164,'wl':-0.7090,'ssM':1.22,'wlRad':0.2279,'sRad':0.75,'v':58,'av':58,},{'xy':[[2,1],],'p':0.2988,'wl':-0.6342,'ssM':0.20,'wlRad':0.1123,'sRad':0.84,'v':19,'av':19,},{'xy':[[3,2],],'p':0.7125,'wl':-0.4077,'ssM':-0.25,'wlRad':0.0769,'sRad':0.68,'v':19,'av':19,},{'xy':[[3,3],],'p':0.9087,'wl':-0.3886,'ssM':-0.02,'wlRad':0.0066,'sRad':1.06,'v':18,'av':18,},{'xy':[[0,1],],'p':0.8466,'wl':0.0342,'ssM':0.15,'wlRad':0.0065,'sRad':0.73,'v':18,'av':18,},{'xy':[[0,0],],'p':0.0535,'wl':0.0015,'ssM':0.16,'wlRad':0.0783,'sRad':0.95,'v':36,'av':36,},{'xy':[[1,1],],'p':0.0816,'wl':0.6248,'ssM':0.33,'wlRad':0.1365,'sRad':0.57,'v':18,'av':18,},{'xy':[[2,3],],'p':0.2551,'wl':0.8557,'ssM':0.12,'wlRad':0.0061,'sRad':1.72,'v':16,'av':16,},{'xy':[[2,0],],'p':0.4102,'wl':0.8305,'ssM':1.18,'wlRad':0.0534,'sRad':0.31,'v':15,'av':15,},{'move':'pass','p':0.4995,'wl':0.8930,'ssM':0.76,'wlRad':0.0061,'sRad':1.36,'v':65,'av':65,},{'xy':[[2,2],],'p':0.7523,'wl':0.8737,'ssM':1.45,'wlRad':0.1550,'sRad':1.08,'v':88,'av':88,},{'move':'other','p':0.1048,'wl':0.5897,'ssM':0.73,'wlRad':0.1767,'sRad':0.52,'v':16,'av':16,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
