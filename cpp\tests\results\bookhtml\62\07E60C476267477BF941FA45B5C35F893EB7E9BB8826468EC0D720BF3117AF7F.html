
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 2;
const board = [0,0,0,0,0,0,0,0,0,2,0,1,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,2],],'p':0.4339,'wl':0.0274,'ssM':0.27,'wlRad':0.0733,'sRad':0.37,'v':102,'av':102,},{'xy':[[0,0],],'p':0.7623,'wl':-0.0459,'ssM':1.04,'wlRad':0.0021,'sRad':1.10,'v':113,'av':113,},{'move':'pass','p':0.3858,'wl':-0.0314,'ssM':1.03,'wlRad':0.0280,'sRad':0.39,'v':33,'av':33,},{'xy':[[0,2],],'p':0.0149,'wl':0.1768,'ssM':0.30,'wlRad':0.2379,'sRad':0.62,'v':75,'av':75,},{'xy':[[1,1],],'p':0.5341,'wl':0.4305,'ssM':-0.21,'wlRad':0.0494,'sRad':1.32,'v':94,'av':94,},{'xy':[[2,3],],'p':0.9831,'wl':0.9345,'ssM':0.36,'wlRad':0.0267,'sRad':1.69,'v':104,'av':104,},{'move':'other','p':0.2177,'wl':-0.8826,'ssM':-0.62,'wlRad':0.1863,'sRad':0.20,'v':14,'av':14,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
