This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: cpp/search
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)


================================================================
Directory Structure
================================================================
cpp/search/analysisdata.cpp
cpp/search/analysisdata.h
cpp/search/asyncbot.cpp
cpp/search/asyncbot.h
cpp/search/distributiontable.cpp
cpp/search/distributiontable.h
cpp/search/localpattern.cpp
cpp/search/localpattern.h
cpp/search/mutexpool.cpp
cpp/search/mutexpool.h
cpp/search/patternbonustable.cpp
cpp/search/patternbonustable.h
cpp/search/reportedsearchvalues.cpp
cpp/search/reportedsearchvalues.h
cpp/search/search.cpp
cpp/search/search.h
cpp/search/searchexplorehelpers.cpp
cpp/search/searchhelpers.cpp
cpp/search/searchmirror.cpp
cpp/search/searchmultithreadhelpers.cpp
cpp/search/searchnnhelpers.cpp
cpp/search/searchnode.cpp
cpp/search/searchnode.h
cpp/search/searchnodetable.cpp
cpp/search/searchnodetable.h
cpp/search/searchparams.cpp
cpp/search/searchparams.h
cpp/search/searchprint.cpp
cpp/search/searchprint.h
cpp/search/searchpuct.cpp
cpp/search/searchresults.cpp
cpp/search/searchtimehelpers.cpp
cpp/search/searchupdatehelpers.cpp
cpp/search/subtreevaluebiastable.cpp
cpp/search/subtreevaluebiastable.h
cpp/search/timecontrols.cpp
cpp/search/timecontrols.h

================================================================
Files
================================================================

================
File: cpp/search/analysisdata.cpp
================
#include "../search/analysisdata.h"

AnalysisData::AnalysisData()
  :move(Board::NULL_LOC),
   numVisits(0),
   playSelectionValue(0.0),
   lcb(0.0),
   radius(0.0),
   utility(0.0),
   resultUtility(0.0),
   scoreUtility(0.0),
   winLossValue(0.0),
   policyPrior(0.0),
   scoreMean(0.0),
   scoreStdev(0.0),
   lead(0.0),
   ess(0.0),
   weightFactor(0.0),
   weightSum(0.0),
   weightSqSum(0.0),
   utilitySqAvg(0.0),
   scoreMeanSqAvg(0.0),
   childVisits(0),
   childWeightSum(0.0),
   order(0),
   isSymmetryOf(Board::NULL_LOC),
   symmetry(0),
   pv(),
   pvVisits(),
   pvEdgeVisits(),
   node(NULL)
{}

AnalysisData::AnalysisData(const AnalysisData& other)
  :move(other.move),
   numVisits(other.numVisits),
   playSelectionValue(other.playSelectionValue),
   lcb(other.lcb),
   radius(other.radius),
   utility(other.utility),
   resultUtility(other.resultUtility),
   scoreUtility(other.scoreUtility),
   winLossValue(other.winLossValue),
   policyPrior(other.policyPrior),
   scoreMean(other.scoreMean),
   scoreStdev(other.scoreStdev),
   lead(other.lead),
   ess(other.ess),
   weightFactor(other.weightFactor),
   weightSum(other.weightSum),
   weightSqSum(other.weightSqSum),
   utilitySqAvg(other.utilitySqAvg),
   scoreMeanSqAvg(other.scoreMeanSqAvg),
   childVisits(other.childVisits),
   childWeightSum(other.childWeightSum),
   order(other.order),
   isSymmetryOf(other.isSymmetryOf),
   symmetry(other.symmetry),
   pv(other.pv),
   pvVisits(other.pvVisits),
   pvEdgeVisits(other.pvEdgeVisits),
   node(other.node)
{}

AnalysisData::AnalysisData(AnalysisData&& other) noexcept
  :move(other.move),
   numVisits(other.numVisits),
   playSelectionValue(other.playSelectionValue),
   lcb(other.lcb),
   radius(other.radius),
   utility(other.utility),
   resultUtility(other.resultUtility),
   scoreUtility(other.scoreUtility),
   winLossValue(other.winLossValue),
   policyPrior(other.policyPrior),
   scoreMean(other.scoreMean),
   scoreStdev(other.scoreStdev),
   lead(other.lead),
   ess(other.ess),
   weightFactor(other.weightFactor),
   weightSum(other.weightSum),
   weightSqSum(other.weightSqSum),
   utilitySqAvg(other.utilitySqAvg),
   scoreMeanSqAvg(other.scoreMeanSqAvg),
   childVisits(other.childVisits),
   childWeightSum(other.childWeightSum),
   order(other.order),
   isSymmetryOf(other.isSymmetryOf),
   symmetry(other.symmetry),
   pv(std::move(other.pv)),
   pvVisits(std::move(other.pvVisits)),
   pvEdgeVisits(std::move(other.pvEdgeVisits)),
   node(other.node)
{}

AnalysisData::~AnalysisData()
{}

AnalysisData& AnalysisData::operator=(const AnalysisData& other) {
  if(this == &other)
    return *this;
  move = other.move;
  numVisits = other.numVisits;
  playSelectionValue = other.playSelectionValue;
  lcb = other.lcb;
  radius = other.radius;
  utility = other.utility;
  resultUtility = other.resultUtility;
  scoreUtility = other.scoreUtility;
  winLossValue = other.winLossValue;
  policyPrior = other.policyPrior;
  scoreMean = other.scoreMean;
  scoreStdev = other.scoreStdev;
  lead = other.lead;
  ess = other.ess;
  weightFactor = other.weightFactor;
  weightSum = other.weightSum;
  weightSqSum = other.weightSqSum;
  utilitySqAvg = other.utilitySqAvg;
  scoreMeanSqAvg = other.scoreMeanSqAvg;
  childVisits = other.childVisits;
  childWeightSum = other.childWeightSum;
  order = other.order;
  isSymmetryOf = other.isSymmetryOf;
  symmetry = other.symmetry;
  pv = other.pv;
  pvVisits = other.pvVisits;
  pvEdgeVisits = other.pvEdgeVisits;
  node = other.node;
  return *this;
}

AnalysisData& AnalysisData::operator=(AnalysisData&& other) noexcept {
  if(this == &other)
    return *this;
  move = other.move;
  numVisits = other.numVisits;
  playSelectionValue = other.playSelectionValue;
  lcb = other.lcb;
  radius = other.radius;
  utility = other.utility;
  resultUtility = other.resultUtility;
  scoreUtility = other.scoreUtility;
  winLossValue = other.winLossValue;
  policyPrior = other.policyPrior;
  scoreMean = other.scoreMean;
  scoreStdev = other.scoreStdev;
  lead = other.lead;
  ess = other.ess;
  weightFactor = other.weightFactor;
  weightSum = other.weightSum;
  weightSqSum = other.weightSqSum;
  utilitySqAvg = other.utilitySqAvg;
  scoreMeanSqAvg = other.scoreMeanSqAvg;
  childVisits = other.childVisits;
  childWeightSum = other.childWeightSum;
  order = other.order;
  isSymmetryOf = other.isSymmetryOf;
  symmetry = other.symmetry;
  pv = std::move(other.pv);
  pvVisits = std::move(other.pvVisits);
  pvEdgeVisits = std::move(other.pvEdgeVisits);
  node = other.node;
  return *this;
}

bool operator<(const AnalysisData& a0, const AnalysisData& a1) {
  // Sort all 0-visit moves to the end.
  if(a0.numVisits > 0 && a1.numVisits == 0)
    return true;
  if(a1.numVisits > 0 && a0.numVisits == 0)
    return false;
  // Then sort by play selection value, the normal value for chosing moves to play.
  if(a0.playSelectionValue > a1.playSelectionValue)
    return true;
  if(a0.playSelectionValue < a1.playSelectionValue)
    return false;
  // Then by visits
  if(a0.numVisits > a1.numVisits)
    return true;
  if(a0.numVisits < a1.numVisits)
    return false;
  // Then just by raw policy
  else
    return a0.policyPrior > a1.policyPrior;
}

bool AnalysisData::pvContainsPass() const {
  for(int i = 0; i<pv.size(); i++)
    if(pv[i] == Board::PASS_LOC)
      return true;
  return false;
}

void AnalysisData::writePV(std::ostream& out, const Board& board) const {
  for(int j = 0; j<pv.size(); j++) {
    if(j > 0)
      out << " ";
    out << Location::toString(pv[j],board);
  }
}

void AnalysisData::writePVVisits(std::ostream& out) const {
  for(int j = 0; j<pvVisits.size(); j++) {
    if(j > 0)
      out << " ";
    out << pvVisits[j];
  }
}
void AnalysisData::writePVEdgeVisits(std::ostream& out) const {
  for(int j = 0; j<pvEdgeVisits.size(); j++) {
    if(j > 0)
      out << " ";
    out << pvEdgeVisits[j];
  }
}

int AnalysisData::getPVLenUpToPhaseEnd(const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const {
  Board board(initialBoard);
  BoardHistory hist(initialHist);
  Player nextPla = initialPla;
  int j;
  for(j = 0; j<pv.size(); j++) {
    hist.makeBoardMoveAssumeLegal(board,pv[j],nextPla,NULL);
    nextPla = getOpp(nextPla);
    if(hist.encorePhase != initialHist.encorePhase)
      break;
  }
  return j;
}

void AnalysisData::writePVUpToPhaseEnd(std::ostream& out, const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const {
  Board board(initialBoard);
  BoardHistory hist(initialHist);
  Player nextPla = initialPla;
  for(int j = 0; j<pv.size(); j++) {
    if(j > 0)
      out << " ";
    out << Location::toString(pv[j],board);

    hist.makeBoardMoveAssumeLegal(board,pv[j],nextPla,NULL);
    nextPla = getOpp(nextPla);
    if(hist.encorePhase != initialHist.encorePhase)
      break;
  }
}

void AnalysisData::writePVVisitsUpToPhaseEnd(std::ostream& out, const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const {
  Board board(initialBoard);
  BoardHistory hist(initialHist);
  Player nextPla = initialPla;
  assert(pv.size() == pvVisits.size());
  for(int j = 0; j<pv.size(); j++) {
    if(j > 0)
      out << " ";
    out << pvVisits[j];

    hist.makeBoardMoveAssumeLegal(board,pv[j],nextPla,NULL);
    nextPla = getOpp(nextPla);
    if(hist.encorePhase != initialHist.encorePhase)
      break;
  }
}

void AnalysisData::writePVEdgeVisitsUpToPhaseEnd(std::ostream& out, const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const {
  Board board(initialBoard);
  BoardHistory hist(initialHist);
  Player nextPla = initialPla;
  assert(pv.size() == pvEdgeVisits.size());
  for(int j = 0; j<pv.size(); j++) {
    if(j > 0)
      out << " ";
    out << pvEdgeVisits[j];

    hist.makeBoardMoveAssumeLegal(board,pv[j],nextPla,NULL);
    nextPla = getOpp(nextPla);
    if(hist.encorePhase != initialHist.encorePhase)
      break;
  }
}

================
File: cpp/search/analysisdata.h
================
#ifndef SEARCH_ANALYSISDATA_H_
#define SEARCH_ANALYSISDATA_H_

#include "../game/board.h"
#include "../game/boardhistory.h"

struct SearchNode;

struct AnalysisData {
  //Utilities and scores should all be from white's perspective
  Loc move;
  int64_t numVisits;
  double playSelectionValue; //Similar units to visits, but might have LCB adjustments
  double lcb; //In units of utility
  double radius; //In units of utility
  double utility; //From -1 to 1 or -1.25 to -1.25 or other similar bounds, depending on score utility
  double resultUtility; //Utility from winloss result
  double scoreUtility; //Utility from score. Summing with resultUtility gives utility.
  double winLossValue; //From -1 to 1
  double policyPrior; //From 0 to 1
  double scoreMean; //In units of points
  double scoreStdev; //In units of points
  double lead; //In units of points
  double ess; //Effective sample size taking into account weighting, could be somewhat smaller than visits
  double weightFactor; //Due to child value weighting
  double weightSum; //Internal value that is used instead of visits for everything
  double weightSqSum; //Sum of squares
  double utilitySqAvg;
  double scoreMeanSqAvg;
  int64_t childVisits; //Visits of the underlying child node
  double childWeightSum; //Weight sum of the underlying child node
  int order; //Preference order of the moves, 0 is best
  Loc isSymmetryOf; //If not Board::NULL_LOC, this move is a duplicate analysis data reflected from isSymmetryOf
  int symmetry; //The symmetry applied to isSymmetryOf to get move, or 0.
  std::vector<Loc> pv;
  std::vector<int64_t> pvVisits;
  std::vector<int64_t> pvEdgeVisits;

  const SearchNode* node; //ONLY valid so long as search is not cleared

  AnalysisData();
  AnalysisData(const AnalysisData& other);
  AnalysisData(AnalysisData&& other) noexcept;
  ~AnalysisData();

  AnalysisData& operator=(const AnalysisData& other);
  AnalysisData& operator=(AnalysisData&& other) noexcept;

  bool pvContainsPass() const;
  void writePV(std::ostream& out, const Board& board) const;
  void writePVVisits(std::ostream& out) const;
  void writePVEdgeVisits(std::ostream& out) const;
  void writePVUpToPhaseEnd(std::ostream& out, const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const;
  void writePVVisitsUpToPhaseEnd(std::ostream& out, const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const;
  void writePVEdgeVisitsUpToPhaseEnd(std::ostream& out, const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const;
  int getPVLenUpToPhaseEnd(const Board& initialBoard, const BoardHistory& initialHist, Player initialPla) const;
};

bool operator<(const AnalysisData& a0, const AnalysisData& a1);


#endif  // SEARCH_ANALYSISDATA_H_

================
File: cpp/search/asyncbot.h
================
#ifndef SEARCH_ASYNCBOT_H_
#define SEARCH_ASYNCBOT_H_

#include "../search/search.h"

class AsyncBot {
 public:
  AsyncBot(
    SearchParams params,
    NNEvaluator* nnEval,
    Logger* logger,
    const std::string& randSeed
  );
  AsyncBot(
    SearchParams params,
    NNEvaluator* nnEval,
    NNEvaluator* humanEval,
    Logger* logger,
    const std::string& randSeed
  );
  ~AsyncBot();

  AsyncBot(const AsyncBot& other) = delete;
  AsyncBot& operator=(const AsyncBot& other) = delete;

  //Unless otherwise specified, functions in this class are NOT threadsafe, although they may spawn off asynchronous events.
  //Usage of this API should be single-threaded!

  const Board& getRootBoard() const;
  const BoardHistory& getRootHist() const;
  Player getRootPla() const;
  Player getPlayoutDoublingAdvantagePla() const;
  const SearchParams& getParams() const;

  //Get the search directly. If the asyncbot is doing anything asynchronous, the search MAY STILL BE RUNNING!
  const Search* getSearch() const;
  //Get the search, after stopping and waiting to terminate any existing search
  //Note that one still should NOT mind any threading issues using this search object and other asyncBot calls at the same time.
  Search* getSearchStopAndWait();

  //Setup, same as in search.h
  //Calling any of these will stop any ongoing search, waiting for a full stop.
  void setPosition(Player pla, const Board& board, const BoardHistory& history);
  void setPlayerAndClearHistory(Player pla);
  void setPlayerIfNew(Player pla);
  void setKomiIfNew(float newKomi);
  void setRootHintLoc(Loc loc);
  void setAvoidMoveUntilByLoc(const std::vector<int>& bVec, const std::vector<int>& wVec);
  void setAvoidMoveUntilRescaleRoot(bool b);
  void setAlwaysIncludeOwnerMap(bool b);
  void setParams(SearchParams params);
  void setParamsNoClearing(SearchParams params);
  void setExternalPatternBonusTable(std::unique_ptr<PatternBonusTable>&& table);
  void setCopyOfExternalPatternBonusTable(const std::unique_ptr<PatternBonusTable>& table);
  void clearSearch();

  //Updates position and preserves the relevant subtree of search
  //Will stop any ongoing search, waiting for a full stop.
  //If the move is not legal for the current player, returns false and does nothing, else returns true
  bool makeMove(Loc moveLoc, Player movePla);
  bool makeMove(Loc moveLoc, Player movePla, bool preventEncore);
  bool isLegalTolerant(Loc moveLoc, Player movePla) const;
  bool isLegalStrict(Loc moveLoc, Player movePla) const;

  //Begin searching and produce a move.
  //Will stop any ongoing search, waiting for a full stop.
  //Asynchronously calls the provided function upon success, passing back the move and provided searchId.
  //The provided callback is expected to terminate quickly and should NOT call back into this API.
  //onSearchBegun is called when the search has initialized its tree, after which many asynchronous search query functions become safe
  void genMoveAsync(Player movePla, int searchId, const TimeControls& tc, const std::function<void(Loc,int,Search*)>& onMove);
  void genMoveAsync(Player movePla, int searchId, const TimeControls& tc, double searchFactor, const std::function<void(Loc,int,Search*)>& onMove);
  void genMoveAsync(Player movePla, int searchId, const TimeControls& tc, double searchFactor, const std::function<void(Loc,int,Search*)>& onMove, const std::function<void()>& onSearchBegun);

  //Same as genMove, but waits directly for the move and returns it here.
  Loc genMoveSynchronous(Player movePla, const TimeControls& tc);
  Loc genMoveSynchronous(Player movePla, const TimeControls& tc, double searchFactor);
  Loc genMoveSynchronous(Player movePla, const TimeControls& tc, double searchFactor, const std::function<void()>& onSearchBegun);

  //Begin pondering, returning immediately. Future genMoves may be faster if this is called.
  //Will not stop any ongoing searches.
  void ponder();
  void ponder(double searchFactor);

  //Terminate any existing searches, and then begin pondering while periodically calling the specified callback
  void analyzeAsync(
    Player movePla,
    double searchFactor,
    double callbackPeriod,
    double firstCallbackAfter,
    const std::function<void(const Search* search)>& callback
  );
  //Same as genMove but with periodic analyze callbacks
  void genMoveAsyncAnalyze(
    Player movePla,
    int searchId,
    const TimeControls& tc,
    double searchFactor,
    const std::function<void(Loc,int,Search*)>& onMove,
    double callbackPeriod,
    double firstCallbackAfter,
    const std::function<void(const Search* search)>& callback
  );
  void genMoveAsyncAnalyze(
    Player movePla,
    int searchId,
    const TimeControls& tc,
    double searchFactor,
    const std::function<void(Loc,int,Search*)>& onMove,
    double callbackPeriod,
    double firstCallbackAfter,
    const std::function<void(const Search* search)>& callback,
    const std::function<void()>& onSearchBegun
  );
  Loc genMoveSynchronousAnalyze(
    Player movePla,
    const TimeControls& tc,
    double searchFactor,
    double callbackPeriod,
    double firstCallbackAfter,
    const std::function<void(const Search* search)>& callback
  );
  Loc genMoveSynchronousAnalyze(
    Player movePla,
    const TimeControls& tc,
    double searchFactor,
    double callbackPeriod,
    double firstCallbackAfter,
    const std::function<void(const Search* search)>& callback,
    const std::function<void()>& onSearchBegun
  );

  //Wait for the ongoing search to end, if any search is running.
  void waitForSearchToEnd();

  //Signal an ongoing genMove or ponder to stop as soon as possible, and wait for the stop to happen.
  //Safe to call even if nothing is running.
  void stopAndWait();
  //Same, but does NOT wait for the stop. Also safe to call even if nothing is running.
  //Does not lock anything, so even safe to call from inside callbacks from this API.
  void stopWithoutWait();
  //Call this to permanently kill this bot and prevent future search.
  void setKilled();


 private:
  Search* search;

  std::mutex controlMutex;
  std::condition_variable threadWaitingToSearch;
  std::condition_variable userWaitingForStop;
  std::thread searchThread;

  bool isRunning;
  bool isPondering;
  bool isKilled;
  std::atomic<bool> shouldStopNow;
  int queuedSearchId;
  std::function<void(Loc,int,Search*)> queuedOnMove;
  TimeControls timeControls;
  double searchFactor;
  double analyzeCallbackPeriod;
  double analyzeFirstCallbackAfter;
  std::function<void(const Search* search)> analyzeCallback;
  std::function<void()> searchBegunCallback;

  void stopAndWaitAlreadyLocked(std::unique_lock<std::mutex>& lock);
  void waitForSearchToEndAlreadyLocked(std::unique_lock<std::mutex>& lock);

 public:
  //Only for internal use
  void internalSearchThreadLoop();
};


#endif  // SEARCH_ASYNCBOT_H_

================
File: cpp/search/distributiontable.cpp
================
#include "../search/distributiontable.h"

using namespace std;

DistributionTable::DistributionTable(function<double(double)> pdf, function<double(double)> cdf, double minz, double maxz, int sz) {
  size = sz;
  minZ = minz;
  maxZ = maxz;
  pdfTable = new double[size];
  cdfTable = new double[size];

  for(int i = 0; i<size; i++) {
    if(i == 0) {
      pdfTable[i] = 0.0;
      cdfTable[i] = 0.0;
    }
    else if(i == size-1) {
      pdfTable[i] = 0.0;
      cdfTable[i] = 1.0;
    }
    else {
      double z = minZ + i * (maxZ-minZ) / (double)(size-1);
      pdfTable[i] = pdf(z);
      cdfTable[i] = cdf(z);
    }
  }
}

DistributionTable::~DistributionTable() {
  delete[] pdfTable;
  delete[] cdfTable;
}

================
File: cpp/search/distributiontable.h
================
#ifndef SEARCH_DISTRIBUTIONTABLE_H
#define SEARCH_DISTRIBUTIONTABLE_H

#include "../core/global.h"

struct DistributionTable {
  double* pdfTable;
  double* cdfTable;
  int size;
  double minZ;
  double maxZ;

  DistributionTable(std::function<double(double)> pdf, std::function<double(double)> cdf, double minZ, double maxZ, int size);
  ~DistributionTable();

  DistributionTable(const DistributionTable& other) = delete;
  DistributionTable& operator=(const DistributionTable& other) = delete;

  inline void getPdfCdf(double z, double& pdf, double& cdf) const {
    double d = (size-1) * (z-minZ) / (maxZ-minZ);
    if(d <= 0) {
      pdf = 0.0;
      cdf = 0.0;
      return;
    }
    int idx = (int)d;
    if(idx >= size-1) {
      pdf = 0.0;
      cdf = 1.0;
      return;
    }
    double lambda = d - idx;
    double yp0 = pdfTable[idx];
    double yp1 = pdfTable[idx+1];
    double yc0 = cdfTable[idx];
    double yc1 = cdfTable[idx+1];
    pdf = yp0 + lambda * (yp1 - yp0);
    cdf = yc0 + lambda * (yc1 - yc0);
  };

  inline double getPdf(double z) const {
    double d = (size-1) * (z-minZ) / (maxZ-minZ);
    if(d <= 0)
      return 0.0;
    int idx = (int)d;
    if(idx >= size-1)
      return 0.0;
    double lambda = d - idx;
    double y0 = pdfTable[idx];
    double y1 = pdfTable[idx+1];
    return y0 + lambda * (y1 - y0);
  };

  inline double getCdf(double z) const {
    double d = (size-1) * (z-minZ) / (maxZ-minZ);
    if(d <= 0)
      return 0.0;
    int idx = (int)d;
    if(idx >= size-1)
      return 1.0;
    double lambda = d - idx;
    double y0 = cdfTable[idx];
    double y1 = cdfTable[idx+1];
    return y0 + lambda * (y1 - y0);
  };
};


#endif  // SEARCH_DISTRIBUTION_TABLE_H_

================
File: cpp/search/localpattern.cpp
================
#include "../search/localpattern.h"

#include "../neuralnet/nninputs.h"

using namespace std;

LocalPatternHasher::LocalPatternHasher()
  : xSize(),
    ySize(),
    zobristLocalPattern(),
    zobristPla(),
    zobristAtari()
{}


void LocalPatternHasher::init(int x, int y, Rand& rand) {
  xSize = x;
  ySize = y;
  assert(xSize > 0 && xSize % 2 == 1);
  assert(ySize > 0 && ySize % 2 == 1);
  zobristLocalPattern.resize(NUM_BOARD_COLORS * xSize * ySize);
  zobristPla.resize(NUM_BOARD_COLORS);
  zobristAtari.resize(xSize * ySize);

  for(int i = 0; i<NUM_BOARD_COLORS; i++) {
    for(int dy = 0; dy<ySize; dy++) {
      for(int dx = 0; dx<xSize; dx++) {
        uint64_t h0 = rand.nextUInt64();
        uint64_t h1 = rand.nextUInt64();
        zobristLocalPattern[i * ySize*xSize + dy*xSize + dx] = Hash128(h0,h1);
      }
    }
  }
  for(int i = 0; i<NUM_BOARD_COLORS; i++) {
    uint64_t h0 = rand.nextUInt64();
    uint64_t h1 = rand.nextUInt64();
    zobristPla[i] = Hash128(h0,h1);
  }
  for(int dy = 0; dy<ySize; dy++) {
    for(int dx = 0; dx<xSize; dx++) {
      uint64_t h0 = rand.nextUInt64();
      uint64_t h1 = rand.nextUInt64();
      zobristAtari[dy*xSize + dx] = Hash128(h0,h1);
    }
  }
}

LocalPatternHasher::~LocalPatternHasher() {
}


Hash128 LocalPatternHasher::getHash(const Board& board, Loc loc, Player pla) const {
  Hash128 hash = zobristPla[pla];

  if(loc != Board::PASS_LOC && loc != Board::NULL_LOC) {
    const int dxi = board.adj_offsets[2];
    const int dyi = board.adj_offsets[3];
    assert(dxi == 1);
    assert(dyi == board.x_size+1);

    int xRadius = xSize/2;
    int yRadius = ySize/2;
    int xCenter = xSize/2;
    int yCenter = ySize/2;

    int x = Location::getX(loc,board.x_size);
    int y = Location::getY(loc,board.x_size);
    int dxMin = -xRadius, dxMax = xRadius, dyMin = -yRadius, dyMax = yRadius;
    if(x < xRadius) { dxMin = -x; } else if(x >= board.x_size-xRadius) { dxMax = board.x_size-1-x; }
    if(y < yRadius) { dyMin = -y; } else if(y >= board.y_size-yRadius) { dyMax = board.y_size-1-y; }
    for(int dy = dyMin; dy <= dyMax; dy++) {
      for(int dx = dxMin; dx <= dxMax; dx++) {
        Loc loc2 = loc + dx * dxi + dy * dyi;
        int y2 = dy + yCenter;
        int x2 = dx + xCenter;
        int xy2 = y2 * xSize + x2;
        hash ^= zobristLocalPattern[(int)board.colors[loc2] * xSize * ySize + xy2];
        if((board.colors[loc2] == P_BLACK || board.colors[loc2] == P_WHITE) && board.getNumLiberties(loc2) == 1)
          hash ^= zobristAtari[xy2];
      }
    }
  }

  return hash;
}

Hash128 LocalPatternHasher::getHashWithSym(const Board& board, Loc loc, Player pla, int symmetry, bool flipColors) const {
  Player symPla = flipColors ? getOpp(pla) : pla;
  Hash128 hash = zobristPla[symPla];

  if(loc != Board::PASS_LOC && loc != Board::NULL_LOC) {
    const int dxi = board.adj_offsets[2];
    const int dyi = board.adj_offsets[3];
    assert(dxi == 1);
    assert(dyi == board.x_size+1);

    int xRadius = xSize/2;
    int yRadius = ySize/2;
    int xCenter = xSize/2;
    int yCenter = ySize/2;

    bool transpose = SymmetryHelpers::isTranspose(symmetry);
    bool flipX = SymmetryHelpers::isFlipX(symmetry);
    bool flipY = SymmetryHelpers::isFlipY(symmetry);

    int x = Location::getX(loc,board.x_size);
    int y = Location::getY(loc,board.x_size);
    int dxMin = -xRadius, dxMax = xRadius, dyMin = -yRadius, dyMax = yRadius;
    if(x < xRadius) { dxMin = -x; } else if(x >= board.x_size-xRadius) { dxMax = board.x_size-1-x; }
    if(y < yRadius) { dyMin = -y; } else if(y >= board.y_size-yRadius) { dyMax = board.y_size-1-y; }
    for(int dy = dyMin; dy <= dyMax; dy++) {
      for(int dx = dxMin; dx <= dxMax; dx++) {
        Loc loc2 = loc + dx * dxi + dy * dyi;
        int y2 = dy + yCenter;
        int x2 = dx + xCenter;

        int symXY2;
        int symX2 = flipX ? xSize - x2 - 1 : x2;
        int symY2 = flipY ? ySize - y2 - 1 : y2;
        if(transpose) {
          std::swap(symX2,symY2);
          symXY2 = symY2 * ySize + symX2;
        }
        else {
          symXY2 = symY2 * xSize + symX2;
        }

        int symColor;
        if(board.colors[loc2] == P_BLACK || board.colors[loc2] == P_WHITE)
          symColor = (int)(flipColors ? getOpp(board.colors[loc2]) : board.colors[loc2]);
        else
          symColor = (int)board.colors[loc2];

        hash ^= zobristLocalPattern[symColor * xSize * ySize + symXY2];
        if((board.colors[loc2] == P_BLACK || board.colors[loc2] == P_WHITE) && board.getNumLiberties(loc2) == 1)
          hash ^= zobristAtari[symXY2];
      }
    }
  }

  return hash;
}

================
File: cpp/search/localpattern.h
================
#ifndef SEARCH_LOCALPATTERN_H
#define SEARCH_LOCALPATTERN_H

#include "../core/global.h"
#include "../core/hash.h"
#include "../core/rand.h"
#include "../game/board.h"

struct LocalPatternHasher {
  int xSize;
  int ySize;
  std::vector<Hash128> zobristLocalPattern;
  std::vector<Hash128> zobristPla;
  std::vector<Hash128> zobristAtari;

  LocalPatternHasher();
  ~LocalPatternHasher();

  void init(int xSize, int ySize, Rand& rand);

  Hash128 getHash(const Board& board, Loc loc, Player pla) const;

  //Returns the hash that would occur if symmetry were applied to both board and loc.
  //So basically, the only thing that changes is the zobrist indexing.
  Hash128 getHashWithSym(const Board& board, Loc loc, Player pla, int symmetry, bool flipColors) const;
};

#endif //SEARCH_LOCALPATTERN_H

================
File: cpp/search/mutexpool.cpp
================
#include "../search/mutexpool.h"

using namespace std;

MutexPool::MutexPool(uint32_t n) {
  numMutexes = n;
  mutexes = new mutex[n];
}

MutexPool::~MutexPool() {
  delete[] mutexes;
}

uint32_t MutexPool::getNumMutexes() const {
  return numMutexes;
}

mutex& MutexPool::getMutex(uint32_t idx) {
  return mutexes[idx];
}

================
File: cpp/search/mutexpool.h
================
#ifndef SEARCH_MUTEXPOOL_H_
#define SEARCH_MUTEXPOOL_H_

#include "../core/global.h"
#include "../core/multithread.h"

class MutexPool {
  std::mutex* mutexes;
  uint32_t numMutexes;

 public:
  MutexPool(uint32_t n);
  ~MutexPool();

  uint32_t getNumMutexes() const;
  std::mutex& getMutex(uint32_t idx);
};

#endif  // SEARCH_MUTEXPOOL_H_

================
File: cpp/search/patternbonustable.cpp
================
#include "../search/patternbonustable.h"

#include "../core/rand.h"
#include "../core/multithread.h"
#include "../core/fileutils.h"
#include "../neuralnet/nninputs.h"
#include "../search/localpattern.h"
#include "../dataio/sgf.h"
#include "../dataio/files.h"

using namespace std;

static std::mutex initMutex;
static std::atomic<bool> isInited(false);
static LocalPatternHasher patternHasher;
static Hash128 ZOBRIST_MOVE_LOCS[Board::MAX_ARR_SIZE];

static void initIfNeeded() {
  if(isInited)
    return;
  std::lock_guard<std::mutex> lock(initMutex);
  if(isInited)
    return;
  Rand rand("PatternBonusTable ZOBRIST STUFF");
  patternHasher.init(9,9,rand);

  rand.init("Reseed PatternBonusTable zobrist so that zobrists don't change when Board::MAX_ARR_SIZE changes");
  for(int i = 0; i<Board::MAX_ARR_SIZE; i++) {
    uint64_t h0 = rand.nextUInt64();
    uint64_t h1 = rand.nextUInt64();
    ZOBRIST_MOVE_LOCS[i] = Hash128(h0,h1);
  }
  isInited = true;
}

PatternBonusTable::PatternBonusTable() {
  initIfNeeded();
  entries.resize(1024);
}
PatternBonusTable::PatternBonusTable(int32_t numShards) {
  initIfNeeded();
  entries.resize(numShards);
}
PatternBonusTable::PatternBonusTable(const PatternBonusTable& other) {
  initIfNeeded();
  entries = other.entries;
}
PatternBonusTable::~PatternBonusTable() {
}

Hash128 PatternBonusTable::getHash(Player pla, Loc moveLoc, const Board& board) const {
  //We don't want to over-trigger this on a ko that repeats the same pattern over and over
  //So we just disallow this on ko fight
  //Also no bonuses for passing.
  if(moveLoc == Board::NULL_LOC || moveLoc == Board::PASS_LOC || board.wouldBeKoCapture(moveLoc,pla))
    return Hash128();

  Hash128 hash = patternHasher.getHash(board,moveLoc,pla);
  hash ^= ZOBRIST_MOVE_LOCS[moveLoc];
  hash ^= Board::ZOBRIST_SIZE_X_HASH[board.x_size];
  hash ^= Board::ZOBRIST_SIZE_Y_HASH[board.y_size];

  return hash;
}

PatternBonusEntry PatternBonusTable::get(Hash128 hash) const {
  //Hash 0 indicates to not do anything. If anything legit collides with it, then it will do nothing
  //but this should be very rare.
  if(hash == Hash128())
    return PatternBonusEntry();

  auto subMapIdx = hash.hash0 % entries.size();

  const std::map<Hash128,PatternBonusEntry>& subMap = entries[subMapIdx];
  auto iter = subMap.find(hash);
  if(iter == subMap.end())
    return PatternBonusEntry();
  return iter->second;
}

PatternBonusEntry PatternBonusTable::get(Player pla, Loc moveLoc, const Board& board) const {
  Hash128 hash = getHash(pla, moveLoc, board);
  return get(hash);
}

void PatternBonusTable::addBonus(Player pla, Loc moveLoc, const Board& board, double bonus, int symmetry, bool flipColors, std::set<Hash128>& hashesThisGame) {
  //We don't want to over-trigger this on a ko that repeats the same pattern over and over
  //So we just disallow this on ko fight
  //Also no bonuses for passing.
  if(moveLoc == Board::NULL_LOC || moveLoc == Board::PASS_LOC || board.wouldBeKoCapture(moveLoc,pla))
    return;

  Hash128 hash = patternHasher.getHashWithSym(board,moveLoc,pla,symmetry,flipColors);
  hash ^= ZOBRIST_MOVE_LOCS[SymmetryHelpers::getSymLoc(moveLoc,board,symmetry)];
  if(SymmetryHelpers::isTranspose(symmetry)) {
    hash ^= Board::ZOBRIST_SIZE_X_HASH[board.y_size];
    hash ^= Board::ZOBRIST_SIZE_Y_HASH[board.x_size];
  }
  else {
    hash ^= Board::ZOBRIST_SIZE_X_HASH[board.x_size];
    hash ^= Board::ZOBRIST_SIZE_Y_HASH[board.y_size];
  }

  if(contains(hashesThisGame,hash))
    return;
  hashesThisGame.insert(hash);

  auto subMapIdx = hash.hash0 % entries.size();

  std::map<Hash128,PatternBonusEntry>& subMap = entries[subMapIdx];
  subMap[hash].utilityBonus += bonus;
}

void PatternBonusTable::addBonusForGameMoves(const BoardHistory& game, double bonus) {
  addBonusForGameMoves(game,bonus,C_EMPTY);
}

void PatternBonusTable::addBonusForGameMoves(const BoardHistory& game, double bonus, Player onlyPla) {
  std::set<Hash128> hashesThisGame;
  Board board = game.initialBoard;
  BoardHistory hist(board, game.initialPla, game.rules, game.initialEncorePhase);
  for(size_t i = 0; i<game.moveHistory.size(); i++) {
    Player pla = game.moveHistory[i].pla;
    Loc loc = game.moveHistory[i].loc;
    //We first play the move to see if it's a move we can accept
    bool suc = hist.makeBoardMoveTolerant(board, loc, pla);
    if(!suc)
      break;
    if(onlyPla == C_EMPTY || onlyPla == pla) {
      for(int flipColors = 0; flipColors < 2; flipColors++) {
        for(int symmetry = 0; symmetry < 8; symmetry++) {
          //getRecentBoard(1) - the convention is to pattern match on the board BEFORE the move is played.
          //This is also more pricipled than convening on the board after since with different captures, moves
          //may have different effects even while leading to the same position.
          addBonus(pla, loc, hist.getRecentBoard(1), bonus, symmetry, (bool)flipColors, hashesThisGame);
        }
      }
    }
  }
}

void PatternBonusTable::avoidRepeatedSgfMoves(
  const vector<string>& sgfsDirsOrFiles,
  double penalty,
  double decayOlderFilesLambda,
  int64_t minTurnNumber,
  size_t maxFiles,
  const vector<string>& allowedPlayerNames,
  Logger& logger,
  const string& logSource
) {
  vector<string> sgfFiles;
  FileHelpers::collectSgfsFromDirsOrFiles(sgfsDirsOrFiles,sgfFiles);
  FileHelpers::sortNewestToOldest(sgfFiles);

  double factor = 1.0;
  for(size_t i = 0; i<sgfFiles.size() && i < maxFiles; i++) {
    const string& fileName = sgfFiles[i];
    Sgf* sgf = NULL;
    try {
      sgf = Sgf::loadFile(fileName);
    }
    catch(const StringError& e) {
      logger.write("Invalid SGF " + fileName + ": " + e.what());
      continue;
    }

    bool blackOkay = allowedPlayerNames.size() <= 0 || contains(allowedPlayerNames, sgf->getPlayerName(P_BLACK));
    bool whiteOkay = allowedPlayerNames.size() <= 0 || contains(allowedPlayerNames, sgf->getPlayerName(P_WHITE));

    std::set<Hash128> hashesThisGame;

    std::function<void(Sgf::PositionSample&, const BoardHistory&, const string&)> posHandler = [&](
      Sgf::PositionSample& posSample, const BoardHistory& hist, const string& comments
    ) {
      (void)posSample;
      if(comments.size() > 0 && comments.find("%SKIP%") != string::npos)
        return;
      if(hist.moveHistory.size() <= 0)
        return;
      if(hist.moveHistory.size() < minTurnNumber)
        return;
      Loc moveLoc = hist.moveHistory[hist.moveHistory.size()-1].loc;
      Player movePla = hist.moveHistory[hist.moveHistory.size()-1].pla;
      if(movePla == P_BLACK && !blackOkay)
        return;
      if(movePla == P_WHITE && !whiteOkay)
        return;

      for(int flipColorsInt = 0; flipColorsInt < 2; flipColorsInt++) {
        for(int symmetry = 0; symmetry < 8; symmetry++) {
          //getRecentBoard(1) - the convention is to pattern match on the board BEFORE the move is played.
          //This is also more pricipled than convening on the board after since with different captures, moves
          //may have different effects even while leading to the same position.
          bool flipColors = (bool)flipColorsInt;
          Player symPla = flipColors ? getOpp(movePla) : movePla;
          double bonus = symPla == P_WHITE ? -penalty*factor : penalty*factor;
          addBonus(movePla, moveLoc, hist.getRecentBoard(1), bonus, symmetry, flipColors, hashesThisGame);
        }
      }
    };

    bool hashComments = true;
    bool hashParent = true;
    bool flipIfPassOrWFirst = false;
    bool allowGameOver = false;
    std::set<Hash128> uniqueHashes;
    sgf->iterAllUniquePositions(uniqueHashes, hashComments, hashParent, flipIfPassOrWFirst, allowGameOver, NULL, posHandler);
    logger.write("Added " + Global::uint64ToString(hashesThisGame.size()) + " shapes to penalize repeats for " + logSource + " from " + fileName);

    delete sgf;
    factor *= decayOlderFilesLambda;
  }
}


void PatternBonusTable::avoidRepeatedPosMovesAndDeleteExcessFiles(
  const vector<string>& posesDirsToLoadAndPrune,
  double penalty,
  double decayOlderPosesLambda,
  int64_t minTurnNumber,
  int64_t maxTurnNumber,
  size_t maxPoses,
  Logger& logger,
  const string& logSource
) {
  vector<string> posFiles;
  FileHelpers::collectPosesFromDirs(posesDirsToLoadAndPrune,posFiles);
  FileHelpers::sortNewestToOldest(posFiles);

  size_t numPosesUsed = 0;
  size_t numPosesInvalid = 0;
  size_t numPosLoadErrors = 0; //May be due to concurrent access and pruning of the dir, that's fine, but we count it.
  double factor = 1.0;

  Sgf::PositionSample posSample;
  size_t i = 0;
  for(; i<posFiles.size(); i++) {
    if(numPosesUsed >= maxPoses)
      break;
    std::set<Hash128> hashesThisGame;
    const string& fileName = posFiles[i];
    vector<string> lines = FileUtils::readFileLines(fileName,'\n');
    for(size_t j = 0; j<lines.size(); j++) {
      string line = Global::trim(lines[j]);
      if(line.size() > 0) {
        try {
          posSample = Sgf::PositionSample::ofJsonLine(line);
        }
        catch(const StringError& err) {
          (void)err;
          numPosLoadErrors += 1;
          continue;
        }

        const bool isMultiStoneSuicideLegal = true;
        int64_t turnNumber = posSample.getCurrentTurnNumber();
        if(
          turnNumber < minTurnNumber ||
          turnNumber > maxTurnNumber ||
          posSample.moves.size() != 0 || // Right now auto pattern avoid expects moveless records
          !posSample.board.isLegal(posSample.hintLoc, posSample.nextPla, isMultiStoneSuicideLegal)
        ) {
          numPosesInvalid += 1;
          continue;
        }

        for(int flipColorsInt = 0; flipColorsInt < 2; flipColorsInt++) {
          for(int symmetry = 0; symmetry < 8; symmetry++) {
            //getRecentBoard(1) - the convention is to pattern match on the board BEFORE the move is played.
            //This is also more pricipled than convening on the board after since with different captures, moves
            //may have different effects even while leading to the same position.
            bool flipColors = (bool)flipColorsInt;
            Player symPla = flipColors ? getOpp(posSample.nextPla) : posSample.nextPla;
            double bonus = symPla == P_WHITE ? -penalty*factor : penalty*factor;
            addBonus(posSample.nextPla, posSample.hintLoc, posSample.board, bonus, symmetry, flipColors, hashesThisGame);
          }
        }
        numPosesUsed += 1;
        factor *= decayOlderPosesLambda;
      }
    }
  }
  for(; i<posFiles.size(); i++) {
    logger.write("Removing old pos file: " + posFiles[i]);
    FileUtils::tryRemoveFile(posFiles[i]);
  }

  logger.write("Loaded avoid poses from " + logSource);
  logger.write("numPosesUsed = " + Global::uint64ToString(numPosesUsed));
  logger.write("numPosesInvalid = " + Global::uint64ToString(numPosesInvalid));
  logger.write("numPosLoadErrors = " + Global::uint64ToString(numPosLoadErrors));
}

================
File: cpp/search/patternbonustable.h
================
#ifndef SEARCH_PATTERNBONUSTABLE_H
#define SEARCH_PATTERNBONUSTABLE_H

#include "../core/global.h"
#include "../core/hash.h"
#include "../core/logger.h"
#include "../game/boardhistory.h"

// All bonuses are bonuses to white's utility for the pattern occuring on the board.
struct PatternBonusEntry {
  double utilityBonus = 0.0;
};

struct PatternBonusTable {
  std::vector<std::map<Hash128,PatternBonusEntry>> entries;

  PatternBonusTable();
  PatternBonusTable(int32_t numShards);
  PatternBonusTable(const PatternBonusTable& other);
  ~PatternBonusTable();

  // The board specified here is expected to be the board BEFORE the move is played.
  PatternBonusEntry get(Player pla, Loc moveLoc, const Board& board) const;
  PatternBonusEntry get(Hash128 hash) const;
  // The board specified here is expected to be the board BEFORE the move is played.
  Hash128 getHash(Player pla, Loc moveLoc, const Board& board) const;

  // All bonuses are bonuses to white's utility for the pattern occuring on the board.
  // The board specified here is expected to be the board BEFORE the move is played.
  void addBonus(Player pla, Loc moveLoc, const Board& board, double bonus, int symmetry, bool flipColors, std::set<Hash128>& hashesThisGame);

  void addBonusForGameMoves(const BoardHistory& game, double bonus);
  void addBonusForGameMoves(const BoardHistory& game, double bonus, Player onlyPla);

  void avoidRepeatedSgfMoves(
    const std::vector<std::string>& sgfsDirsOrFiles,
    double penalty,
    double decayOlderFilesLambda,
    int64_t minTurnNumber,
    size_t maxFiles,
    const std::vector<std::string>& allowedPlayerNames,
    Logger& logger,
    const std::string& logSource
  );

  void avoidRepeatedPosMovesAndDeleteExcessFiles(
    const std::vector<std::string>& posesDirsToLoadAndPrune,
    double penalty,
    double decayOlderPosesLambda,
    int64_t minTurnNumber,
    int64_t maxTurnNumber,
    size_t maxPoses,
    Logger& logger,
    const std::string& logSource
  );
};

#endif

================
File: cpp/search/reportedsearchvalues.cpp
================
#include "../search/reportedsearchvalues.h"

#include "../neuralnet/nninputs.h"
#include "../search/search.h"

ReportedSearchValues::ReportedSearchValues()
{}
ReportedSearchValues::~ReportedSearchValues()
{}
ReportedSearchValues::ReportedSearchValues(
  const Search& search,
  double winLossValueAvg,
  double noResultValueAvg,
  double scoreMeanAvg,
  double scoreMeanSqAvg,
  double leadAvg,
  double utilityAvg,
  double totalWeight,
  int64_t totalVisits
) {
  winLossValue = winLossValueAvg;
  noResultValue = noResultValueAvg;
  double scoreMean = scoreMeanAvg;
  double scoreMeanSq = scoreMeanSqAvg;
  double scoreStdev = ScoreValue::getScoreStdev(scoreMean,scoreMeanSq);
  double sqrtBoardArea = search.rootBoard.sqrtBoardArea();
  staticScoreValue = ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,0.0,2.0, sqrtBoardArea);
  dynamicScoreValue = ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,search.recentScoreCenter,search.searchParams.dynamicScoreCenterScale, sqrtBoardArea);
  expectedScore = scoreMean;
  expectedScoreStdev = scoreStdev;
  lead = leadAvg;
  utility = utilityAvg;

  //Clamp. Due to tiny floating point errors, these could be outside range.
  if(winLossValue < -1.0) winLossValue = -1.0;
  if(winLossValue > 1.0) winLossValue = 1.0;
  if(noResultValue < 0.0) noResultValue = 0.0;
  if(noResultValue > 1.0-std::fabs(winLossValue)) noResultValue = 1.0-std::fabs(winLossValue);

  winValue = 0.5 * (winLossValue + (1.0 - noResultValue));
  lossValue = 0.5 * (-winLossValue + (1.0 - noResultValue));

  //Handle float imprecision
  if(winValue < 0.0) winValue = 0.0;
  if(winValue > 1.0) winValue = 1.0;
  if(lossValue < 0.0) lossValue = 0.0;
  if(lossValue > 1.0) lossValue = 1.0;

  weight = totalWeight;
  visits = totalVisits;
}

std::ostream& operator<<(std::ostream& out, const ReportedSearchValues& values) {
  out << "winValue " << values.winValue << "\n";
  out << "lossValue " << values.lossValue << "\n";
  out << "noResultValue " << values.noResultValue << "\n";
  out << "staticScoreValue " << values.staticScoreValue << "\n";
  out << "dynamicScoreValue " << values.dynamicScoreValue << "\n";
  out << "expectedScore " << values.expectedScore << "\n";
  out << "expectedScoreStdev " << values.expectedScoreStdev << "\n";
  out << "lead " << values.lead << "\n";
  out << "winLossValue " << values.winLossValue << "\n";
  out << "utility " << values.utility << "\n";
  out << "weight " << values.weight << "\n";
  out << "visits " << values.visits << "\n";
  return out;
}

================
File: cpp/search/reportedsearchvalues.h
================
#ifndef SEARCH_REPORTEDSEARCHVALUES_H_
#define SEARCH_REPORTEDSEARCHVALUES_H_

#include "../core/global.h"

struct Search;

struct ReportedSearchValues {
  double winValue;
  double lossValue;
  double noResultValue;
  double staticScoreValue;
  double dynamicScoreValue;
  double expectedScore;
  double expectedScoreStdev;
  double lead;
  double winLossValue;
  double utility;
  double weight;
  int64_t visits;

  ReportedSearchValues();
  ReportedSearchValues(
    const Search& search,
    double winLossValueAvg,
    double noResultValueAvg,
    double scoreMeanAvg,
    double scoreMeanSqAvg,
    double leadAvg,
    double utilityAvg,
    double totalWeight,
    int64_t totalVisits
  );
  ~ReportedSearchValues();

  friend std::ostream& operator<<(std::ostream& out, const ReportedSearchValues& values);
};

#endif

================
File: cpp/search/search.h
================
#ifndef SEARCH_SEARCH_H_
#define SEARCH_SEARCH_H_

#include <memory>
#include <unordered_set>

#include "../core/global.h"
#include "../core/hash.h"
#include "../core/logger.h"
#include "../core/multithread.h"
#include "../core/threadsafequeue.h"
#include "../core/threadsafecounter.h"
#include "../game/board.h"
#include "../game/boardhistory.h"
#include "../game/rules.h"
#include "../neuralnet/nneval.h"
#include "../search/analysisdata.h"
#include "../search/mutexpool.h"
#include "../search/reportedsearchvalues.h"
#include "../search/searchparams.h"
#include "../search/searchprint.h"
#include "../search/timecontrols.h"

#include "../external/nlohmann_json/json.hpp"

typedef int SearchNodeState; // See SearchNode::STATE_*

struct SearchNode;
struct SearchThread;
struct Search;
struct DistributionTable;
struct PatternBonusTable;
struct PolicySortEntry;
struct MoreNodeStats;
struct ReportedSearchValues;
struct SearchChildPointer;
struct SubtreeValueBiasTable;
struct SearchNodeTable;
struct SearchNodeChildrenReference;
struct ConstSearchNodeChildrenReference;

//Per-thread state
struct SearchThread {
  int threadIdx;

  Player pla;
  Board board;
  BoardHistory history;
  Hash128 graphHash;
  //The path we trace down the graph as we do a playout
  std::unordered_set<SearchNode*> graphPath;

  //Tracks whether this thread did something that "should" be counted as a playout
  //for the purpose of playout limits
  bool shouldCountPlayout;

  Rand rand;

  NNResultBuf nnResultBuf;
  std::vector<MoreNodeStats> statsBuf;

  double upperBoundVisitsLeft;

  //Occasionally we may need to swap out an NNOutput from a node mid-search.
  //However, to prevent access-after-delete races, the thread that swaps one out stores
  //it here instead of deleting it, so that pointers and accesses to it remain valid.
  std::vector<std::shared_ptr<NNOutput>*> oldNNOutputsToCleanUp;

  //Just controls some debug output
  std::set<Hash128> illegalMoveHashes;

  SearchThread(int threadIdx, const Search& search);
  ~SearchThread();

  SearchThread(const SearchThread&) = delete;
  SearchThread& operator=(const SearchThread&) = delete;
};

struct Search {
  //================================================================================================================
  // Constant/immutable during search
  //================================================================================================================

  Player rootPla;
  Board rootBoard;
  BoardHistory rootHistory;
  Hash128 rootGraphHash;
  Loc rootHintLoc;

  //External user-specified moves that are illegal or that should be nontrivially searched, and the number of turns for which they should
  //be excluded. Empty if not active, else of length MAX_ARR_SIZE and nonzero anywhere a move should be banned, for the number of ply
  //of depth that it should be banned.
  std::vector<int> avoidMoveUntilByLocBlack;
  std::vector<int> avoidMoveUntilByLocWhite;
  bool avoidMoveUntilRescaleRoot; // When avoiding moves at the root, rescale the root policy to sum to 1.

  //If rootSymmetryPruning==true and the board is symmetric, mask all the equivalent copies of each move except one.
  bool rootSymDupLoc[Board::MAX_ARR_SIZE];
  //If rootSymmetryPruning==true, symmetries under which the root board and history are invariant, including some heuristics for ko and encore-related state.
  std::vector<int> rootSymmetries;
  std::vector<int> rootPruneOnlySymmetries;

  //Strictly pass-alive areas in the root board position
  Color* rootSafeArea;
  //Used to center for dynamic scorevalue
  double recentScoreCenter;

  //If the opponent is mirroring, then the color of that opponent, for countering mirroring
  Player mirroringPla;
  double mirrorAdvantage; //Number of points the opponent wins by if mirror holds indefinitely.
  double mirrorCenterSymmetryError;

  bool alwaysIncludeOwnerMap;

  SearchParams searchParams;
  int64_t numSearchesBegun;
  uint32_t searchNodeAge;
  Player plaThatSearchIsFor;
  Player plaThatSearchIsForLastSearch;
  int64_t lastSearchNumPlayouts;
  double effectiveSearchTimeCarriedOver; //Effective search time carried over from previous moves due to ponder/tree reuse

  std::string randSeed;

  //Contains all koHashes of positions/situations up to and including the root
  KoHashTable* rootKoHashTable;

  //Precomputed distribution for downweighting child values based on their values
  DistributionTable* valueWeightDistribution;

  //Precomputed Fancymath::normToTApprox values, for a fixed Z
  double normToTApproxZ;
  std::vector<double> normToTApproxTable;

  //Pattern bonuses are currently only looked up for shapes completed by the player who the search is for.
  //Implicitly these utility adjustments "assume" the opponent likes the negative of our adjustments.
  PatternBonusTable* patternBonusTable;
  std::unique_ptr<PatternBonusTable> externalPatternBonusTable;

  Rand nonSearchRand; //only for use not in search, since rand isn't threadsafe

  //================================================================================================================
  // Externally owned values
  //================================================================================================================

  Logger* logger;
  NNEvaluator* nnEvaluator;
  NNEvaluator* humanEvaluator;
  int nnXLen;
  int nnYLen;
  int policySize;

  //================================================================================================================
  // Mutated during search
  //================================================================================================================

  SearchNode* rootNode;
  SearchNodeTable* nodeTable;
  MutexPool* mutexPool;
  SubtreeValueBiasTable* subtreeValueBiasTable;

  //Thread pool
  int numThreadsSpawned;
  std::thread* threads;
  ThreadSafeQueue<std::function<void(int)>*>* threadTasks;
  ThreadSafeCounter* threadTasksRemaining;

  //Occasionally we may need to swap out an NNOutput from a node mid-search.
  //However, to prevent access-after-delete races, this vector collects them after a thread exits, and is cleaned up
  //very lazily only when a new search begins or the search is cleared.
  std::mutex oldNNOutputsToCleanUpMutex;
  std::vector<std::shared_ptr<NNOutput>*> oldNNOutputsToCleanUp;

  //================================================================================================================
  // Constructors and Destructors
  // search.cpp
  //================================================================================================================

  //Note - randSeed controls a few things in the search, but a lot of the randomness actually comes from
  //random symmetries of the neural net evaluations, see nneval.h
  Search(
    SearchParams params,
    NNEvaluator* nnEval,
    Logger* logger,
    const std::string& randSeed
  );
  Search(
    SearchParams params,
    NNEvaluator* nnEval,
    NNEvaluator* humanEval,
    Logger* logger,
    const std::string& randSeed
  );
  ~Search();

  Search(const Search&) = delete;
  Search& operator=(const Search&) = delete;
  Search(Search&&) = delete;
  Search& operator=(Search&&) = delete;

  //================================================================================================================
  // TOP-LEVEL OUTSIDE-OF-SEARCH CONTROL METHODS
  // search.cpp
  //
  // Functions for setting the board position or other parameters, clearing, and running search.
  // None of these top-level functions are thread-safe. They should only ever be called sequentially.
  //================================================================================================================

  const Board& getRootBoard() const;
  const BoardHistory& getRootHist() const;
  Player getRootPla() const;
  Player getPlayoutDoublingAdvantagePla() const;

  //Get the NNPos corresponding to a loc, convenience method
  int getPos(Loc moveLoc) const;

  //Clear all results of search and sets a new position or something else
  void setPosition(Player pla, const Board& board, const BoardHistory& history);

  void setPlayerAndClearHistory(Player pla);
  void setPlayerIfNew(Player pla);
  void setKomiIfNew(float newKomi); //Does not clear history, does clear search unless komi is equal.
  void setRootHintLoc(Loc hintLoc);
  void setAvoidMoveUntilByLoc(const std::vector<int>& bVec, const std::vector<int>& wVec);
  void setAvoidMoveUntilRescaleRoot(bool b);
  void setAlwaysIncludeOwnerMap(bool b);
  void setRootSymmetryPruningOnly(const std::vector<int>& rootPruneOnlySymmetries);
  void setParams(SearchParams params);
  void setParamsNoClearing(SearchParams params); //Does not clear search
  void setExternalPatternBonusTable(std::unique_ptr<PatternBonusTable>&& table);
  void setCopyOfExternalPatternBonusTable(const std::unique_ptr<PatternBonusTable>& table);
  void setNNEval(NNEvaluator* nnEval);

  //If the number of threads is reduced, this can free up some excess threads in the thread pool.
  //Calling this is never necessary, it may just reduce some resource use.
  //searchmultithreadhelpers.cpp
  void respawnThreads();

  //Just directly clear search without changing anything
  void clearSearch();

  //Updates position and preserves the relevant subtree of search
  //If the move is not legal for the specified player, returns false and does nothing, else returns true
  //In the case where the player was not the expected one moving next, also clears history.
  bool makeMove(Loc moveLoc, Player movePla);
  bool makeMove(Loc moveLoc, Player movePla, bool preventEncore);

  //isLegalTolerant also specially handles players moving multiple times in a row.
  bool isLegalTolerant(Loc moveLoc, Player movePla) const;
  bool isLegalStrict(Loc moveLoc, Player movePla) const;

  //Run an entire search from start to finish
  Loc runWholeSearchAndGetMove(Player movePla);
  void runWholeSearch(Player movePla);
  void runWholeSearch(std::atomic<bool>& shouldStopNow);

  //Pondering indicates that we are searching "for" the last player that we did a non-ponder search for, and should use ponder search limits.
  Loc runWholeSearchAndGetMove(Player movePla, bool pondering);
  void runWholeSearch(Player movePla, bool pondering);
  void runWholeSearch(std::atomic<bool>& shouldStopNow, bool pondering);

  void runWholeSearch(
    std::atomic<bool>& shouldStopNow,
    std::function<void()>* searchBegun, //If not null, will be called once search has begun and tree inspection is safe
    bool pondering,
    const TimeControls& tc,
    double searchFactor
  );

  //Without performing a whole search, recompute the root nn output for any root-level parameters.
  void maybeRecomputeRootNNOutput();

  //Expert manual playout-by-playout interface
  void beginSearch(bool pondering);
  bool runSinglePlayout(SearchThread& thread, double upperBoundVisitsLeft);

  //================================================================================================================
  // SEARCH RESULTS AND TREE INSPECTION METHODS
  // searchresults.cpp
  //
  // Functions for analyzing the results of search or getting back scores and analysis.
  //
  // All of these functions are safe to call in multithreadedly WHILE the search is ongoing, to print out
  // intermediate states of the search, so long as the search has initialized itself and actually begun.
  // In particular, they are allowed to run concurrently with runWholeSearch, so long as searchBegun has
  // been called-back, continuing up until the next call to any other top-level control function above or
  // the next runWholeSearch call.
  // They are NOT safe to call in parallel with any of the other top level-functions besides the search.
  //================================================================================================================

  //Choose a move at the root of the tree, with randomization, if possible.
  //Might return Board::NULL_LOC if there is no root, or no legal moves that aren't forcibly pruned, etc.
  Loc getChosenMoveLoc();
  //Get the vector of values (e.g. modified visit counts) used to select a move.
  //Does take into account chosenMoveSubtract but does NOT apply temperature.
  //If somehow the max value is less than scaleMaxToAtLeast, scale it to at least that value.
  //Always returns false in the case where no actual legal moves are found or there is no nnOutput or no root node.
  //If returning true, the is at least one loc and playSelectionValue.
  bool getPlaySelectionValues(
    std::vector<Loc>& locs, std::vector<double>& playSelectionValues, double scaleMaxToAtLeast
  ) const;
  bool getPlaySelectionValues(
    std::vector<Loc>& locs, std::vector<double>& playSelectionValues, std::vector<double>* retVisitCounts, double scaleMaxToAtLeast
  ) const;
  //Same, but works on a node within the search, not just the root
  bool getPlaySelectionValues(
    const SearchNode& node,
    std::vector<Loc>& locs, std::vector<double>& playSelectionValues, std::vector<double>* retVisitCounts, double scaleMaxToAtLeast,
    bool allowDirectPolicyMoves
  ) const;

  //Get the values recorded for the root node, if possible.
  bool getRootValues(ReportedSearchValues& values) const;
  //Same, same, but throws an exception if no values could be obtained
  ReportedSearchValues getRootValuesRequireSuccess() const;
  //Same, but works on a node within the search, not just the root
  bool getNodeValues(const SearchNode* node, ReportedSearchValues& values) const;
  bool getPrunedRootValues(ReportedSearchValues& values) const;
  bool getPrunedNodeValues(const SearchNode* node, ReportedSearchValues& values) const;

  const SearchNode* getRootNode() const;
  const SearchNode* getChildForMove(const SearchNode* node, Loc moveLoc) const;

  //Same, but based only on the single raw neural net evaluation.
  bool getRootRawNNValues(ReportedSearchValues& values) const;
  ReportedSearchValues getRootRawNNValuesRequireSuccess() const;
  bool getNodeRawNNValues(const SearchNode& node, ReportedSearchValues& values) const;

  //Get the number of visits recorded for the root node
  int64_t getRootVisits() const;
  //Get the root node's policy prediction
  bool getPolicy(float policyProbs[NNPos::MAX_NN_POLICY_SIZE]) const;
  bool getPolicy(const SearchNode* node, float policyProbs[NNPos::MAX_NN_POLICY_SIZE]) const;
  //Get the surprisingness (kl-divergence) of the search result given the policy prior, as well as the entropy of each.
  //Returns false if could not be computed.
  bool getPolicySurpriseAndEntropy(double& surpriseRet, double& searchEntropyRet, double& policyEntropyRet) const;
  bool getPolicySurpriseAndEntropy(double& surpriseRet, double& searchEntropyRet, double& policyEntropyRet, const SearchNode* node) const;
  double getPolicySurprise() const;

  void printPV(std::ostream& out, const SearchNode* node, int maxDepth) const;
  void printPVForMove(std::ostream& out, const SearchNode* node, Loc move, int maxDepth) const;
  void printTree(std::ostream& out, const SearchNode* node, PrintTreeOptions options, Player perspective) const;
  void printRootPolicyMap(std::ostream& out) const;
  void printRootOwnershipMap(std::ostream& out, Player perspective) const;
  void printRootEndingScoreValueBonus(std::ostream& out) const;

  //Get detailed analysis data, designed for lz-analyze and kata-analyze commands.
  void getAnalysisData(
    std::vector<AnalysisData>& buf, int minMovesToTryToGet, bool includeWeightFactors, int maxPVDepth, bool duplicateForSymmetries
  ) const;
  void getAnalysisData(
    const SearchNode& node, std::vector<AnalysisData>& buf, int minMovesToTryToGet, bool includeWeightFactors, int maxPVDepth, bool duplicateForSymmetries
  ) const;

  //Append the PV from node n onward (not including the move if any that reached node n)
  void appendPV(
    std::vector<Loc>& buf,
    std::vector<int64_t>& visitsBuf,
    std::vector<int64_t>& edgeVisitsBuf,
    std::vector<Loc>& scratchLocs,
    std::vector<double>& scratchValues,
    const SearchNode* n,
    int maxDepth
  ) const;
  //Append the PV from node n for specified move, assuming move is a child move of node n
  void appendPVForMove(
    std::vector<Loc>& buf,
    std::vector<int64_t>& visitsBuf,
    std::vector<int64_t>& edgeVisitsBuf,
    std::vector<Loc>& scratchLocs,
    std::vector<double>& scratchValues,
    const SearchNode* n,
    Loc move,
    int maxDepth
  ) const;

  //Get the ownership map averaged throughout the search tree.
  //Must have ownership present on all neural net evals.
  //Safe to call DURING search, but NOT necessarily safe to call multithreadedly when updating the root position
  //or changing parameters or clearing search.
  //If node is not provided, defaults to using the root node.
  std::vector<double> getAverageTreeOwnership(const SearchNode* node = NULL) const;
  std::pair<std::vector<double>,std::vector<double>> getAverageAndStandardDeviationTreeOwnership(const SearchNode* node = NULL) const;

  //Same, but applies symmetry and perspective
  std::vector<double> getAverageTreeOwnership(
    const Player perspective,
    const SearchNode* node,
    int symmetry
 ) const;
  std::pair<std::vector<double>,std::vector<double>> getAverageAndStandardDeviationTreeOwnership(
    const Player perspective,
    const SearchNode* node,
    int symmetry
  ) const;


  std::pair<double,double> getShallowAverageShorttermWLAndScoreError(const SearchNode* node = NULL) const;
  bool getSharpScore(const SearchNode* node, double& ret) const;

  //Fill json with analysis engine format information about search results
  bool getAnalysisJson(
    const Player perspective,
    int analysisPVLen, bool preventEncore, bool includePolicy,
    bool includeOwnership, bool includeOwnershipStdev, bool includeMovesOwnership, bool includeMovesOwnershipStdev, bool includePVVisits,
    nlohmann::json& ret
  ) const;


  //================================================================================================================
  // HELPER FUNCTIONS FOR THE SEARCH
  //================================================================================================================

private:
  static constexpr double POLICY_ILLEGAL_SELECTION_VALUE = -1e50;
  static constexpr double FUTILE_VISITS_PRUNE_VALUE = -1e40;
  static constexpr double EVALUATING_SELECTION_VALUE_PENALTY = 1e20;

  //----------------------------------------------------------------------------------------
  // Dirichlet noise and temperature
  // searchhelpers.cpp
  //----------------------------------------------------------------------------------------
public:
  static uint32_t chooseIndexWithTemperature(
    Rand& rand, const double* relativeProbs, int numRelativeProbs, double temperature, double onlyBelowProb, double* processedRelProbsBuf
  );
  static void computeDirichletAlphaDistribution(int policySize, const float* policyProbs, double* alphaDistr);
  static void addDirichletNoise(const SearchParams& searchParams, Rand& rand, int policySize, float* policyProbs);
private:
  std::shared_ptr<NNOutput>* maybeAddPolicyNoiseAndTemp(SearchThread& thread, bool isRoot, NNOutput* oldNNOutput) const;

  //----------------------------------------------------------------------------------------
  // Computing basic utility and scores
  // searchhelpers.cpp
  //----------------------------------------------------------------------------------------
  double getResultUtility(double winlossValue, double noResultValue) const;
  double getResultUtilityFromNN(const NNOutput& nnOutput) const;
  double getScoreUtility(double scoreMeanAvg, double scoreMeanSqAvg) const;
  double getScoreUtilityDiff(double scoreMeanAvg, double scoreMeanSqAvg, double delta) const;
  double getApproxScoreUtilityDerivative(double scoreMean) const;
  double getUtilityFromNN(const NNOutput& nnOutput) const;

  //----------------------------------------------------------------------------------------
  // Miscellaneous search biasing helpers, root move selection, etc.
  // searchhelpers.cpp
  //----------------------------------------------------------------------------------------
  bool isAllowedRootMove(Loc moveLoc) const;
  double getPatternBonus(Hash128 patternBonusHash, Player prevMovePla) const;
  double getEndingWhiteScoreBonus(const SearchNode& parent, Loc moveLoc) const;
  bool shouldSuppressPass(const SearchNode* n) const;

  double interpolateEarly(double halflife, double earlyValue, double value) const;

  // LCB helpers
  void getSelfUtilityLCBAndRadius(const SearchNode& parent, const SearchNode* child, int64_t edgeVisits, Loc moveLoc, double& lcbBuf, double& radiusBuf) const;
  void getSelfUtilityLCBAndRadiusZeroVisits(double& lcbBuf, double& radiusBuf) const;

  //----------------------------------------------------------------------------------------
  // Mirror handling logic
  // searchmirror.cpp
  //----------------------------------------------------------------------------------------
  void updateMirroring();
  bool isMirroringSinceSearchStart(const BoardHistory& threadHistory, int skipRecent) const;
  void maybeApplyAntiMirrorPolicy(
    float& nnPolicyProb,
    const Loc moveLoc,
    const float* policyProbs,
    const Player movePla,
    const SearchThread* thread
  ) const;
  void maybeApplyAntiMirrorForcedExplore(
    double& childUtility,
    const double parentUtility,
    const Loc moveLoc,
    const float* policyProbs,
    const double thisChildWeight,
    const double totalChildWeight,
    const Player movePla,
    const SearchThread* thread,
    const SearchNode& parent
  ) const;
  void hackNNOutputForMirror(std::shared_ptr<NNOutput>& result) const;

  //----------------------------------------------------------------------------------------
  // Recursive graph-walking and thread pooling
  // searchmultithreadhelpers.cpp
  //----------------------------------------------------------------------------------------
  int numAdditionalThreadsToUseForTasks() const;
  void spawnThreadsIfNeeded();
  void killThreads();
  void performTaskWithThreads(std::function<void(int)>* task, int capThreads);

  void applyRecursivelyPostOrderMulithreaded(const std::vector<SearchNode*>& nodes, std::function<void(SearchNode*,int)>* f);
  void applyRecursivelyPostOrderMulithreadedHelper(
    SearchNode* node, int threadIdx, PCG32* rand, std::unordered_set<SearchNode*>& nodeBuf, std::vector<int>& randBuf, std::function<void(SearchNode*,int)>* f
  );
  void applyRecursivelyAnyOrderMulithreaded(const std::vector<SearchNode*>& nodes, std::function<void(SearchNode*,int)>* f);
  void applyRecursivelyAnyOrderMulithreadedHelper(
    SearchNode* node, int threadIdx, PCG32* rand, std::unordered_set<SearchNode*>& nodeBuf, std::vector<int>& randBuf, std::function<void(SearchNode*,int)>* f
  );

public:
  std::vector<SearchNode*> enumerateTreePostOrder();
private:

  //----------------------------------------------------------------------------------------
  // Time management
  // searchtimehelpers.cpp
  //----------------------------------------------------------------------------------------
  double numVisitsNeededToBeNonFutile(double maxVisitsMoveVisits);
  double computeUpperBoundVisitsLeftDueToTime(
    int64_t rootVisits, double timeUsed, double plannedTimeLimit
  );
  double recomputeSearchTimeLimit(const TimeControls& tc, double timeUsed, double searchFactor, int64_t rootVisits);

  //----------------------------------------------------------------------------------------
  // Neural net queries
  // searchnnhelpers.cpp
  //----------------------------------------------------------------------------------------
  void computeRootNNEvaluation(NNResultBuf& nnResultBuf, bool includeOwnerMap);
  bool initNodeNNOutput(
    SearchThread& thread, SearchNode& node,
    bool isRoot, bool skipCache, bool isReInit
  );
  // Returns true if any recomputation happened
  bool maybeRecomputeExistingNNOutput(
    SearchThread& thread, SearchNode& node, bool isRoot
  );

  bool needsHumanOutputAtRoot() const;
  bool needsHumanOutputInTree() const;

  //----------------------------------------------------------------------------------------
  // Move selection during search
  // searchexplorehelpers.cpp
  //----------------------------------------------------------------------------------------
  double getExploreScaling(
    double totalChildWeight, double parentUtilityStdevFactor
  ) const;
  double getExploreScalingHuman(
    double totalChildWeight
  ) const;
  double getExploreSelectionValue(
    double exploreScaling,
    double nnPolicyProb,
    double childWeight,
    double childUtility,
    Player pla
  ) const;
  double getExploreSelectionValueInverse(
    double exploreScaling,
    double exploreSelectionValue,
    double nnPolicyProb,
    double childUtility,
    Player pla
  ) const;
  double getExploreSelectionValueOfChild(
    const SearchNode& parent, const float* parentPolicyProbs, const SearchNode* child,
    Loc moveLoc,
    double exploreScaling,
    double totalChildWeight, int64_t childEdgeVisits, double fpuValue,
    double parentUtility, double parentWeightPerVisit,
    bool isDuringSearch, bool antiMirror, double maxChildWeight,
    bool countEdgeVisit,
    SearchThread* thread
  ) const;
  double getNewExploreSelectionValue(
    const SearchNode& parent,
    double exploreScaling,
    float nnPolicyProb,
    double fpuValue,
    double parentWeightPerVisit,
    double maxChildWeight,
    bool countEdgeVisit,
    SearchThread* thread
  ) const;
  double getReducedPlaySelectionWeight(
    const SearchNode& parent, const float* parentPolicyProbs, const SearchNode* child,
    Loc moveLoc,
    double exploreScaling,
    int64_t childEdgeVisits,
    double bestChildExploreSelectionValue
  ) const;

  double getFpuValueForChildrenAssumeVisited(
    const SearchNode& node, Player pla, bool isRoot, double policyProbMassVisited,
    double& parentUtility, double& parentWeightPerVisit, double& parentUtilityStdevFactor
  ) const;

  void selectBestChildToDescend(
    SearchThread& thread, const SearchNode& node, SearchNodeState nodeState,
    int& numChildrenFound, int& bestChildIdx, Loc& bestChildMoveLoc, bool& countEdgeVisit,
    bool isRoot
  ) const;

  //----------------------------------------------------------------------------------------
  // Update of node values during search
  // searchupdatehelpers.cpp
  //----------------------------------------------------------------------------------------

  void addLeafValue(
    SearchNode& node,
    double winLossValue,
    double noResultValue,
    double scoreMean,
    double scoreMeanSq,
    double lead,
    double weight,
    bool isTerminal,
    bool assumeNoExistingWeight
  );
  void addCurrentNNOutputAsLeafValue(SearchNode& node, bool assumeNoExistingWeight);

  double computeWeightFromNNOutput(const NNOutput* nnOutput) const;

  void updateStatsAfterPlayout(SearchNode& node, SearchThread& thread, bool isRoot);
  void recomputeNodeStats(SearchNode& node, SearchThread& thread, int32_t numVisitsToAdd, bool isRoot);

  void downweightBadChildrenAndNormalizeWeight(
    int numChildren,
    double currentTotalWeight,
    double desiredTotalWeight,
    double amountToSubtract,
    double amountToPrune,
    std::vector<MoreNodeStats>& statsBuf
  ) const;

  double pruneNoiseWeight(std::vector<MoreNodeStats>& statsBuf, int numChildren, double totalChildWeight, const double* policyProbsBuf) const;

  //----------------------------------------------------------------------------------------
  // Allocation, search clearing and garbage collection
  // search.cpp
  //----------------------------------------------------------------------------------------
  uint32_t createMutexIdxForNode(SearchThread& thread) const;
  SearchNode* allocateOrFindNode(SearchThread& thread, Player nextPla, Loc bestChildMoveLoc, bool forceNonTerminal, Hash128 graphHash);
  void clearOldNNOutputs();
  void transferOldNNOutputs(SearchThread& thread);
  void removeSubtreeValueBias(SearchNode* node);
  void deleteAllOldOrAllNewTableNodesAndSubtreeValueBiasMulithreaded(bool old);
  void deleteAllTableNodesMulithreaded();

  //----------------------------------------------------------------------------------------
  // Initialization and core search logic
  // search.cpp
  //----------------------------------------------------------------------------------------
  void computeRootValues(); // Helper for begin search
  void recursivelyRecomputeStats(SearchNode& node); // Helper for search initialization

  bool playoutDescend(
    SearchThread& thread, SearchNode& node,
    bool isRoot
  );

  bool maybeCatchUpEdgeVisits(
    SearchThread& thread,
    SearchNode& node,
    SearchNode* child,
    const SearchNodeState& nodeState,
    const int bestChildIdx
  );

  //----------------------------------------------------------------------------------------
  // Private helpers for search results and analysis and top level move selection
  // searchresults.cpp
  //----------------------------------------------------------------------------------------
  bool getPlaySelectionValues(
    const SearchNode& node,
    std::vector<Loc>& locs, std::vector<double>& playSelectionValues, std::vector<double>* retVisitCounts, double scaleMaxToAtLeast,
    bool allowDirectPolicyMoves, bool alwaysComputeLcb, bool neverUseLcb,
    double lcbBuf[NNPos::MAX_NN_POLICY_SIZE], double radiusBuf[NNPos::MAX_NN_POLICY_SIZE]
  ) const;

  AnalysisData getAnalysisDataOfSingleChild(
    const SearchNode* child, int64_t edgeVisits, std::vector<Loc>& scratchLocs, std::vector<double>& scratchValues,
    Loc move, double policyProb, double fpuValue, double parentUtility, double parentWinLossValue,
    double parentScoreMean, double parentScoreStdev, double parentLead, int maxPVDepth
  ) const;

  void printPV(std::ostream& out, const std::vector<Loc>& buf) const;

  void printTreeHelper(
    std::ostream& out, const SearchNode* node, const PrintTreeOptions& options,
    std::string& prefix, int64_t origVisits, int depth, const AnalysisData& data, Player perspective
  ) const;

  bool getSharpScoreHelper(
    const SearchNode* node,
    std::unordered_set<const SearchNode*>& graphPath,
    double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE],
    double minProp,
    double desiredProp,
    double& ret
  ) const;
  void getShallowAverageShorttermWLAndScoreErrorHelper(
    const SearchNode* node,
    std::unordered_set<const SearchNode*>& graphPath,
    double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE],
    double minProp,
    double desiredProp,
    double& wlError,
    double& scoreError
  ) const;

  template<typename Func>
  bool traverseTreeForOwnership(
    double minProp,
    double pruneProp,
    double desiredProp,
    const SearchNode* node,
    std::unordered_set<const SearchNode*>& graphPath,
    Func& averaging
  ) const;
  template<typename Func>
  double traverseTreeForOwnershipChildren(
    double minProp,
    double pruneProp,
    double desiredProp,
    double thisNodeWeight,
    ConstSearchNodeChildrenReference children,
    double* childWeightBuf,
    int childrenCapacity,
    std::unordered_set<const SearchNode*>& graphPath,
    Func& averaging
  ) const;


};

#endif  // SEARCH_SEARCH_H_

================
File: cpp/search/searchhelpers.cpp
================
#include "../search/search.h"

#include "../core/fancymath.h"
#include "../core/test.h"
#include "../search/searchnode.h"
#include "../search/patternbonustable.h"

//------------------------
#include "../core/using.h"
//------------------------

uint32_t Search::chooseIndexWithTemperature(
  Rand& rand,
  const double* relativeProbs,
  int numRelativeProbs,
  double temperature,
  double onlyBelowProb,
  double* processedRelProbsBuf
) {
  testAssert(numRelativeProbs > 0);
  testAssert(numRelativeProbs <= Board::MAX_ARR_SIZE); //We're just doing this on the stack
  double processedRelProbs[Board::MAX_ARR_SIZE];
  if(processedRelProbsBuf == NULL)
    processedRelProbsBuf = &processedRelProbs[0];

  double maxRelProb = 0.0;
  double sumRelProb = 0.0;
  for(int i = 0; i<numRelativeProbs; i++) {
    sumRelProb += std::max(0.0, relativeProbs[i]);
    if(relativeProbs[i] > maxRelProb)
      maxRelProb = relativeProbs[i];
  }
  testAssert(maxRelProb > 0.0);
  testAssert(sumRelProb > 0.0);

  //Temperature so close to 0 that we just calculate the max directly
  if(temperature <= 1.0e-4 && onlyBelowProb >= 1.0) {
    double bestProb = relativeProbs[0];
    int bestIdx = 0;
    processedRelProbsBuf[0] = 0;
    for(int i = 1; i<numRelativeProbs; i++) {
      processedRelProbsBuf[i] = 0;
      if(relativeProbs[i] > bestProb) {
        bestProb = relativeProbs[i];
        bestIdx = i;
      }
    }
    processedRelProbsBuf[bestIdx] = 1.0;
    return bestIdx;
  }
  //Actual temperature
  else {
    double logMaxRelProb = log(maxRelProb);
    double logSumRelProb = log(sumRelProb);
    double logOnlyBelowProb = log(std::max(1e-50,onlyBelowProb));
    double sum = 0.0;
    for(int i = 0; i<numRelativeProbs; i++) {
      if(relativeProbs[i] <= 0.0)
        processedRelProbsBuf[i] = 0.0;
      else {
        double logRelProb = log(relativeProbs[i]) - logMaxRelProb;
        double logRelProbThreshold = std::min(0.0, logOnlyBelowProb + logSumRelProb - logMaxRelProb);
        double newLogRelProb;
        if(logRelProb > logRelProbThreshold)
          newLogRelProb = logRelProb;
        else
          newLogRelProb = (logRelProb - logRelProbThreshold) / temperature + logRelProbThreshold;
        processedRelProbsBuf[i] = exp(newLogRelProb);
      }
      sum += processedRelProbsBuf[i];
    }
    testAssert(sum > 0.0);
    uint32_t idxChosen = rand.nextUInt(processedRelProbsBuf,numRelativeProbs);
    return idxChosen;
  }
}

void Search::computeDirichletAlphaDistribution(int policySize, const float* policyProbs, double* alphaDistr) {
  int legalCount = 0;
  for(int i = 0; i<policySize; i++) {
    if(policyProbs[i] >= 0)
      legalCount += 1;
  }

  if(legalCount <= 0)
    throw StringError("computeDirichletAlphaDistribution: No move with nonnegative policy value - can't even pass?");

  //We're going to generate a gamma draw on each move with alphas that sum up to searchParams.rootDirichletNoiseTotalConcentration.
  //Half of the alpha weight are uniform.
  //The other half are shaped based on the log of the existing policy.
  double logPolicySum = 0.0;
  for(int i = 0; i<policySize; i++) {
    if(policyProbs[i] >= 0) {
      alphaDistr[i] = log(std::min(0.01, (double)policyProbs[i]) + 1e-20);
      logPolicySum += alphaDistr[i];
    }
  }
  double logPolicyMean = logPolicySum / legalCount;
  double alphaPropSum = 0.0;
  for(int i = 0; i<policySize; i++) {
    if(policyProbs[i] >= 0) {
      alphaDistr[i] = std::max(0.0, alphaDistr[i] - logPolicyMean);
      alphaPropSum += alphaDistr[i];
    }
  }
  double uniformProb = 1.0 / legalCount;
  if(alphaPropSum <= 0.0) {
    for(int i = 0; i<policySize; i++) {
      if(policyProbs[i] >= 0)
        alphaDistr[i] = uniformProb;
    }
  }
  else {
    for(int i = 0; i<policySize; i++) {
      if(policyProbs[i] >= 0)
        alphaDistr[i] = 0.5 * (alphaDistr[i] / alphaPropSum + uniformProb);
    }
  }
}

void Search::addDirichletNoise(const SearchParams& searchParams, Rand& rand, int policySize, float* policyProbs) {
  double r[NNPos::MAX_NN_POLICY_SIZE];
  Search::computeDirichletAlphaDistribution(policySize, policyProbs, r);

  //r now contains the proportions with which we would like to split the alpha
  //The total of the alphas is searchParams.rootDirichletNoiseTotalConcentration
  //Generate gamma draw on each move
  double rSum = 0.0;
  for(int i = 0; i<policySize; i++) {
    if(policyProbs[i] >= 0) {
      r[i] = rand.nextGamma(r[i] * searchParams.rootDirichletNoiseTotalConcentration);
      rSum += r[i];
    }
    else
      r[i] = 0.0;
  }

  //Normalized gamma draws -> dirichlet noise
  for(int i = 0; i<policySize; i++)
    r[i] /= rSum;

  //At this point, r[i] contains a dirichlet distribution draw, so add it into the nnOutput.
  for(int i = 0; i<policySize; i++) {
    if(policyProbs[i] >= 0) {
      double weight = searchParams.rootDirichletNoiseWeight;
      policyProbs[i] = (float)(r[i] * weight + policyProbs[i] * (1.0-weight));
    }
  }
}


std::shared_ptr<NNOutput>* Search::maybeAddPolicyNoiseAndTemp(SearchThread& thread, bool isRoot, NNOutput* oldNNOutput) const {
  if(!isRoot)
    return NULL;
  if(!searchParams.rootNoiseEnabled &&
     searchParams.rootPolicyTemperature == 1.0 &&
     searchParams.rootPolicyTemperatureEarly == 1.0 &&
     rootHintLoc == Board::NULL_LOC &&
     !avoidMoveUntilRescaleRoot
  )
    return NULL;
  if(oldNNOutput == NULL)
    return NULL;
  if(oldNNOutput->noisedPolicyProbs != NULL)
    return NULL;

  //Copy nnOutput as we're about to modify its policy to add noise or temperature
  std::shared_ptr<NNOutput>* newNNOutputSharedPtr = new std::shared_ptr<NNOutput>(new NNOutput(*oldNNOutput));
  NNOutput* newNNOutput = newNNOutputSharedPtr->get();

  float* noisedPolicyProbs = new float[NNPos::MAX_NN_POLICY_SIZE];
  newNNOutput->noisedPolicyProbs = noisedPolicyProbs;
  std::copy(newNNOutput->policyProbs, newNNOutput->policyProbs + NNPos::MAX_NN_POLICY_SIZE, noisedPolicyProbs);

  if(searchParams.rootPolicyTemperature != 1.0 || searchParams.rootPolicyTemperatureEarly != 1.0) {
    double rootPolicyTemperature = interpolateEarly(
      searchParams.chosenMoveTemperatureHalflife, searchParams.rootPolicyTemperatureEarly, searchParams.rootPolicyTemperature
    );

    double maxValue = 0.0;
    for(int i = 0; i<policySize; i++) {
      double prob = noisedPolicyProbs[i];
      if(prob > maxValue)
        maxValue = prob;
    }
    assert(maxValue > 0.0);

    double logMaxValue = log(maxValue);
    double invTemp = 1.0 / rootPolicyTemperature;
    double sum = 0.0;

    for(int i = 0; i<policySize; i++) {
      if(noisedPolicyProbs[i] > 0) {
        //Numerically stable way to raise to power and normalize
        float p = (float)exp((log((double)noisedPolicyProbs[i]) - logMaxValue) * invTemp);
        noisedPolicyProbs[i] = p;
        sum += p;
      }
    }
    assert(sum > 0.0);
    for(int i = 0; i<policySize; i++) {
      if(noisedPolicyProbs[i] >= 0) {
        noisedPolicyProbs[i] = (float)(noisedPolicyProbs[i] / sum);
      }
    }
  }

  if(searchParams.rootNoiseEnabled) {
    addDirichletNoise(searchParams, thread.rand, policySize, noisedPolicyProbs);
  }

  if(avoidMoveUntilRescaleRoot) {
    const std::vector<int>& avoidMoveUntilByLoc = rootPla == P_BLACK ? avoidMoveUntilByLocBlack : avoidMoveUntilByLocWhite;
    if(avoidMoveUntilByLoc.size() > 0) {
      assert(avoidMoveUntilByLoc.size() >= Board::MAX_ARR_SIZE);
      double policySum = 0.0;
      for(Loc loc = 0; loc<Board::MAX_ARR_SIZE; loc++) {
        if((rootBoard.isOnBoard(loc) || loc == Board::PASS_LOC) && avoidMoveUntilByLoc[loc] <= 0) {
          int pos = getPos(loc);
          if(noisedPolicyProbs[pos] > 0) {
            policySum += noisedPolicyProbs[pos];
          }
        }
      }
      if(policySum > 0.0) {
        for(int i = 0; i<policySize; i++) {
          if(noisedPolicyProbs[i] > 0) {
            noisedPolicyProbs[i] = (float)(noisedPolicyProbs[i] / policySum);
          }
        }
      }
    }
  }

  //Move a small amount of policy to the hint move, around the same level that noising it would achieve
  if(rootHintLoc != Board::NULL_LOC) {
    const float propToMove = 0.02f;
    int pos = getPos(rootHintLoc);
    if(noisedPolicyProbs[pos] >= 0) {
      double amountToMove = 0.0;
      for(int i = 0; i<policySize; i++) {
        if(noisedPolicyProbs[i] >= 0) {
          amountToMove += noisedPolicyProbs[i] * propToMove;
          noisedPolicyProbs[i] *= (1.0f-propToMove);
        }
      }
      noisedPolicyProbs[pos] += (float)amountToMove;
    }
  }

  return newNNOutputSharedPtr;
}




double Search::getResultUtility(double winLossValue, double noResultValue) const {
  return (
    winLossValue * searchParams.winLossUtilityFactor +
    noResultValue * searchParams.noResultUtilityForWhite
  );
}

double Search::getResultUtilityFromNN(const NNOutput& nnOutput) const {
  return (
    (nnOutput.whiteWinProb - nnOutput.whiteLossProb) * searchParams.winLossUtilityFactor +
    nnOutput.whiteNoResultProb * searchParams.noResultUtilityForWhite
  );
}

double Search::getScoreUtility(double scoreMeanAvg, double scoreMeanSqAvg) const {
  double scoreMean = scoreMeanAvg;
  double scoreMeanSq = scoreMeanSqAvg;
  double scoreStdev = ScoreValue::getScoreStdev(scoreMean, scoreMeanSq);
  double sqrtBoardArea = rootBoard.sqrtBoardArea();
  double staticScoreValue = ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,0.0,2.0, sqrtBoardArea);
  double dynamicScoreValue = ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,recentScoreCenter,searchParams.dynamicScoreCenterScale, sqrtBoardArea);
  return staticScoreValue * searchParams.staticScoreUtilityFactor + dynamicScoreValue * searchParams.dynamicScoreUtilityFactor;
}

double Search::getScoreUtilityDiff(double scoreMeanAvg, double scoreMeanSqAvg, double delta) const {
  double scoreMean = scoreMeanAvg;
  double scoreMeanSq = scoreMeanSqAvg;
  double scoreStdev = ScoreValue::getScoreStdev(scoreMean, scoreMeanSq);
  double sqrtBoardArea = rootBoard.sqrtBoardArea();
  double staticScoreValueDiff =
    ScoreValue::expectedWhiteScoreValue(scoreMean + delta,scoreStdev,0.0,2.0, sqrtBoardArea)
    -ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,0.0,2.0, sqrtBoardArea);
  double dynamicScoreValueDiff =
    ScoreValue::expectedWhiteScoreValue(scoreMean + delta,scoreStdev,recentScoreCenter,searchParams.dynamicScoreCenterScale, sqrtBoardArea)
    -ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,recentScoreCenter,searchParams.dynamicScoreCenterScale, sqrtBoardArea);
  return staticScoreValueDiff * searchParams.staticScoreUtilityFactor + dynamicScoreValueDiff * searchParams.dynamicScoreUtilityFactor;
}

//Ignores scoreMeanSq's effect on the utility, since that's complicated
double Search::getApproxScoreUtilityDerivative(double scoreMean) const {
  double sqrtBoardArea = rootBoard.sqrtBoardArea();
  double staticScoreValueDerivative = ScoreValue::whiteDScoreValueDScoreSmoothNoDrawAdjust(scoreMean,0.0,2.0, sqrtBoardArea);
  double dynamicScoreValueDerivative = ScoreValue::whiteDScoreValueDScoreSmoothNoDrawAdjust(scoreMean,recentScoreCenter,searchParams.dynamicScoreCenterScale, sqrtBoardArea);
  return staticScoreValueDerivative * searchParams.staticScoreUtilityFactor + dynamicScoreValueDerivative * searchParams.dynamicScoreUtilityFactor;
}


double Search::getUtilityFromNN(const NNOutput& nnOutput) const {
  double resultUtility = getResultUtilityFromNN(nnOutput);
  return resultUtility + getScoreUtility(nnOutput.whiteScoreMean, nnOutput.whiteScoreMeanSq);
}


bool Search::isAllowedRootMove(Loc moveLoc) const {
  assert(moveLoc == Board::PASS_LOC || rootBoard.isOnBoard(moveLoc));

  //A bad situation that can happen that unnecessarily prolongs training games is where one player
  //repeatedly passes and the other side repeatedly fills the opponent's space and/or suicides over and over.
  //To mitigate some of this and save computation, we make it so that at the root, if the last four moves by the opponent
  //were passes, we will never play a move in either player's pass-alive area. In theory this could prune
  //a good move in situations like https://senseis.xmp.net/?1EyeFlaw, but this should be extraordinarly rare,
  if(searchParams.rootPruneUselessMoves &&
     rootHistory.moveHistory.size() > 0 &&
     moveLoc != Board::PASS_LOC
  ) {
    size_t lastIdx = rootHistory.moveHistory.size()-1;
    Player opp = getOpp(rootPla);
    if(lastIdx >= 6 &&
       rootHistory.moveHistory[lastIdx-0].loc == Board::PASS_LOC &&
       rootHistory.moveHistory[lastIdx-2].loc == Board::PASS_LOC &&
       rootHistory.moveHistory[lastIdx-4].loc == Board::PASS_LOC &&
       rootHistory.moveHistory[lastIdx-6].loc == Board::PASS_LOC &&
       rootHistory.moveHistory[lastIdx-0].pla == opp &&
       rootHistory.moveHistory[lastIdx-2].pla == opp &&
       rootHistory.moveHistory[lastIdx-4].pla == opp &&
       rootHistory.moveHistory[lastIdx-6].pla == opp &&
       (rootSafeArea[moveLoc] == opp || rootSafeArea[moveLoc] == rootPla))
      return false;
  }

  if(searchParams.rootSymmetryPruning && moveLoc != Board::PASS_LOC && rootSymDupLoc[moveLoc]) {
    return false;
  }

  return true;
}

double Search::getPatternBonus(Hash128 patternBonusHash, Player prevMovePla) const {
  if(patternBonusTable == NULL || prevMovePla != plaThatSearchIsFor)
    return 0;
  return patternBonusTable->get(patternBonusHash).utilityBonus;
}


double Search::getEndingWhiteScoreBonus(const SearchNode& parent, Loc moveLoc) const {
  if(&parent != rootNode || moveLoc == Board::NULL_LOC)
    return 0.0;

  const NNOutput* nnOutput = parent.getNNOutput();
  if(nnOutput == NULL || nnOutput->whiteOwnerMap == NULL)
    return 0.0;

  bool isAreaIsh = rootHistory.rules.scoringRule == Rules::SCORING_AREA
    || (rootHistory.rules.scoringRule == Rules::SCORING_TERRITORY && rootHistory.encorePhase >= 2);
  assert(nnOutput->nnXLen == nnXLen);
  assert(nnOutput->nnYLen == nnYLen);
  float* whiteOwnerMap = nnOutput->whiteOwnerMap;

  const double extreme = 0.95;
  const double tail = 0.05;

  //Extra points from the perspective of the root player
  double extraRootPoints = 0.0;
  if(isAreaIsh) {
    //Areaish scoring - in an effort to keep the game short and slightly discourage pointless territory filling at the end
    //discourage any move that, except in case of ko, is either:
    // * On a spot that the opponent almost surely owns, unless it captures stones.
    // * On a spot that the player almost surely owns and it is not adjacent to opponent stones and is not a connection of non-pass-alive groups.
    //These conditions should still make it so that "cleanup" and dame-filling moves are not discouraged.
    // * When playing button go, very slightly discourage passing - so that if there are an even number of dame, filling a dame is still favored over passing.
    if(moveLoc != Board::PASS_LOC && rootBoard.ko_loc == Board::NULL_LOC) {
      int pos = NNPos::locToPos(moveLoc,rootBoard.x_size,nnXLen,nnYLen);
      double plaOwnership = rootPla == P_WHITE ? whiteOwnerMap[pos] : -whiteOwnerMap[pos];
      if(plaOwnership <= -extreme) {
        if(!rootBoard.wouldBeCapture(moveLoc,rootPla))
          extraRootPoints -= searchParams.rootEndingBonusPoints * ((-extreme - plaOwnership) / tail);
      }
      else if(plaOwnership >= extreme) {
        if(!rootBoard.isAdjacentToPla(moveLoc,getOpp(rootPla)) &&
           !rootBoard.isNonPassAliveSelfConnection(moveLoc,rootPla,rootSafeArea)) {
          extraRootPoints -= searchParams.rootEndingBonusPoints * ((plaOwnership - extreme) / tail);
        }
      }
    }
    if(moveLoc == Board::PASS_LOC && rootHistory.hasButton) {
      extraRootPoints -= searchParams.rootEndingBonusPoints * 0.5;
    }
  }
  else {
    //Territorish scoring - slightly encourage dame-filling by discouraging passing, so that the player will try to do everything
    //non-point-losing first, like filling dame.
    //Human japanese rules often "want" you to fill the dame so this is a cosmetic adjustment to encourage the neural
    //net to learn to do so in the main phase rather than waiting until the encore.
    //But cosmetically, it's also not great if we just encourage useless threat moves in the opponent's territory to prolong the game.
    //So also discourage those moves except in cases of ko. Also similar to area scoring just to be symmetrical, discourage moves on spots
    //that the player almost surely owns that are not adjacent to opponent stones and are not a connection of non-pass-alive groups.
    if(moveLoc == Board::PASS_LOC)
      extraRootPoints -= searchParams.rootEndingBonusPoints * (2.0/3.0);
    else if(rootBoard.ko_loc == Board::NULL_LOC) {
      int pos = NNPos::locToPos(moveLoc,rootBoard.x_size,nnXLen,nnYLen);
      double plaOwnership = rootPla == P_WHITE ? whiteOwnerMap[pos] : -whiteOwnerMap[pos];
      if(plaOwnership <= -extreme)
        extraRootPoints -= searchParams.rootEndingBonusPoints * ((-extreme - plaOwnership) / tail);
      else if(plaOwnership >= extreme) {
        if(!rootBoard.isAdjacentToPla(moveLoc,getOpp(rootPla)) &&
           !rootBoard.isNonPassAliveSelfConnection(moveLoc,rootPla,rootSafeArea)) {
          extraRootPoints -= searchParams.rootEndingBonusPoints * ((plaOwnership - extreme) / tail);
        }
      }
    }
  }

  if(rootPla == P_WHITE)
    return extraRootPoints;
  else
    return -extraRootPoints;
}

//Hack to encourage well-behaved dame filling behavior under territory scoring
bool Search::shouldSuppressPass(const SearchNode* n) const {
  if(!searchParams.fillDameBeforePass || n == NULL || n != rootNode)
    return false;
  if(rootHistory.rules.scoringRule != Rules::SCORING_TERRITORY || rootHistory.encorePhase > 0)
    return false;

  const SearchNode& node = *n;
  const NNOutput* nnOutput = node.getNNOutput();
  if(nnOutput == NULL)
    return false;
  if(nnOutput->whiteOwnerMap == NULL)
    return false;
  assert(nnOutput->nnXLen == nnXLen);
  assert(nnOutput->nnYLen == nnYLen);
  const float* whiteOwnerMap = nnOutput->whiteOwnerMap;

  //Find the pass move
  const SearchNode* passNode = NULL;
  int64_t passEdgeVisits = 0;

  ConstSearchNodeChildrenReference children = node.getChildren();
  int childrenCapacity = children.getCapacity();
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    Loc moveLoc = childPointer.getMoveLocRelaxed();
    if(moveLoc == Board::PASS_LOC) {
      passNode = child;
      passEdgeVisits = childPointer.getEdgeVisits();
      break;
    }
  }
  if(passNode == NULL)
    return false;

  double passWeight;
  double passUtility;
  double passScoreMean;
  double passLead;
  {
    int64_t passVisits = passNode->stats.visits.load(std::memory_order_acquire);
    double scoreMeanAvg = passNode->stats.scoreMeanAvg.load(std::memory_order_acquire);
    double leadAvg = passNode->stats.leadAvg.load(std::memory_order_acquire);
    double utilityAvg = passNode->stats.utilityAvg.load(std::memory_order_acquire);
    double childWeight = passNode->stats.getChildWeight(passEdgeVisits,passVisits);

    if(passVisits <= 0 || childWeight <= 1e-10)
      return false;
    passWeight = childWeight;
    passUtility = utilityAvg;
    passScoreMean = scoreMeanAvg;
    passLead = leadAvg;
  }

  const double extreme = 0.95;

  //Suppress pass if we find a move that is not a spot that the opponent almost certainly owns
  //or that is adjacent to a pla owned spot, and is not greatly worse than pass.
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    Loc moveLoc = childPointer.getMoveLocRelaxed();
    if(moveLoc == Board::PASS_LOC)
      continue;
    int pos = NNPos::locToPos(moveLoc,rootBoard.x_size,nnXLen,nnYLen);
    double plaOwnership = rootPla == P_WHITE ? whiteOwnerMap[pos] : -whiteOwnerMap[pos];
    bool oppOwned = plaOwnership < -extreme;
    bool adjToPlaOwned = false;
    for(int j = 0; j<4; j++) {
      Loc adj = moveLoc + rootBoard.adj_offsets[j];
      if(rootBoard.isOnBoard(adj)) {
        int adjPos = NNPos::locToPos(adj,rootBoard.x_size,nnXLen,nnYLen);
        double adjPlaOwnership = rootPla == P_WHITE ? whiteOwnerMap[adjPos] : -whiteOwnerMap[adjPos];
        if(adjPlaOwnership > extreme) {
          adjToPlaOwned = true;
          break;
        }
      }
    }
    if(oppOwned && !adjToPlaOwned)
      continue;

    int64_t edgeVisits = childPointer.getEdgeVisits();

    double scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
    double leadAvg = child->stats.leadAvg.load(std::memory_order_acquire);
    double utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);
    double childWeight = child->stats.getChildWeight(edgeVisits);

    //Too few visits - reject move
    if((edgeVisits <= 500 && childWeight <= 2 * sqrt(passWeight)) || childWeight <= 1e-10)
      continue;

    double utility = utilityAvg;
    double scoreMean = scoreMeanAvg;
    double lead = leadAvg;

    if(rootPla == P_WHITE
       && utility > passUtility - 0.1
       && scoreMean > passScoreMean - 0.5
       && lead > passLead - 0.5)
      return true;
    if(rootPla == P_BLACK
       && utility < passUtility + 0.1
       && scoreMean < passScoreMean + 0.5
       && lead < passLead + 0.5)
      return true;
  }
  return false;
}

double Search::interpolateEarly(double halflife, double earlyValue, double value) const {
  double rawHalflives = (double)rootHistory.getCurrentTurnNumber() / halflife;
  double halflives = rawHalflives * 19.0 / sqrt(rootBoard.x_size*rootBoard.y_size);
  return value + (earlyValue - value) * pow(0.5, halflives);
}

void Search::getSelfUtilityLCBAndRadiusZeroVisits(double& lcbBuf, double& radiusBuf) const {
  // Max radius of the entire utility range
  double utilityRangeRadius = searchParams.winLossUtilityFactor + searchParams.staticScoreUtilityFactor + searchParams.dynamicScoreUtilityFactor;
  radiusBuf = 2.0 * utilityRangeRadius * searchParams.lcbStdevs;
  lcbBuf = -radiusBuf;
  return;
}

void Search::getSelfUtilityLCBAndRadius(const SearchNode& parent, const SearchNode* child, int64_t edgeVisits, Loc moveLoc, double& lcbBuf, double& radiusBuf) const {
  int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
  double scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
  double scoreMeanSqAvg = child->stats.scoreMeanSqAvg.load(std::memory_order_acquire);
  double utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);
  double utilitySqAvg = child->stats.utilitySqAvg.load(std::memory_order_acquire);
  double weightSum = child->stats.getChildWeight(edgeVisits,childVisits);
  double weightSqSum = child->stats.getChildWeightSq(edgeVisits,childVisits);

  // Max radius of the entire utility range
  double utilityRangeRadius = searchParams.winLossUtilityFactor + searchParams.staticScoreUtilityFactor + searchParams.dynamicScoreUtilityFactor;
  radiusBuf = 2.0 * utilityRangeRadius * searchParams.lcbStdevs;
  lcbBuf = -radiusBuf;
  if(childVisits <= 0 || weightSum <= 0.0 || weightSqSum <= 0.0)
    return;

  // Effective sample size for weighted data
  double ess = weightSum * weightSum / weightSqSum;

  // To behave well at low playouts, we'd like a variance approximation that makes sense even with very small sample sizes.
  // We'd like to avoid using a T distribution approximation because we actually know a bound on the scale of the utilities
  // involved, namely utilityRangeRadius. So instead add a prior with a small weight that the variance is the largest it can be.
  // This should give a relatively smooth scaling that works for small discrete samples but diminishes for larger playouts.
  double priorWeight = weightSum / (ess * ess * ess);
  utilitySqAvg = std::max(utilitySqAvg, utilityAvg * utilityAvg + 1e-8);
  utilitySqAvg = (utilitySqAvg * weightSum + (utilitySqAvg + utilityRangeRadius * utilityRangeRadius) * priorWeight) / (weightSum + priorWeight);
  weightSum += priorWeight;
  weightSqSum += priorWeight*priorWeight;

  // Recompute effective sample size now that we have the prior
  ess = weightSum * weightSum / weightSqSum;

  double endingScoreBonus = getEndingWhiteScoreBonus(parent,moveLoc);
  double utilityDiff = getScoreUtilityDiff(scoreMeanAvg, scoreMeanSqAvg, endingScoreBonus);
  double utilityWithBonus = utilityAvg + utilityDiff;
  double selfUtility = parent.nextPla == P_WHITE ? utilityWithBonus : -utilityWithBonus;

  double utilityVariance = utilitySqAvg - utilityAvg * utilityAvg;
  double estimateStdev = sqrt(utilityVariance / ess);
  double radius = estimateStdev * searchParams.lcbStdevs;

  lcbBuf = selfUtility - radius;
  radiusBuf = radius;
}

================
File: cpp/search/searchmirror.cpp
================
#include "../search/search.h"

#include "../search/searchnode.h"

//------------------------
#include "../core/using.h"
//------------------------

// Updates mirroringPla, mirrorAdvantage, mirrorCenterSymmetryError
void Search::updateMirroring() {
  mirroringPla = C_EMPTY;
  mirrorAdvantage = 0.0;
  mirrorCenterSymmetryError = 1e10;

  if(searchParams.antiMirror) {
    const Board& board = rootBoard;
    const BoardHistory& hist = rootHistory;
    int mirrorCount = 0;
    int totalCount = 0;
    double mirrorEwms = 0;
    double totalEwms = 0;
    bool lastWasMirror = false;
    for(int i = 1; i<hist.moveHistory.size(); i++) {
      if(hist.moveHistory[i].pla != rootPla) {
        lastWasMirror = false;
        if(hist.moveHistory[i].loc == Location::getMirrorLoc(hist.moveHistory[i-1].loc,board.x_size,board.y_size)) {
          mirrorCount += 1;
          mirrorEwms += 1;
          lastWasMirror = true;
        }
        totalCount += 1;
        totalEwms += 1;
        mirrorEwms *= 0.75;
        totalEwms *= 0.75;
      }
    }
    //If at most of the moves in the game are mirror moves, and many of the recent moves were mirrors, and the last move
    //was a mirror, then the opponent is mirroring.
    if(mirrorCount >= 7.0 + 0.5 * totalCount && mirrorEwms >= 0.45 * totalEwms && lastWasMirror) {
      mirroringPla = getOpp(rootPla);

      double blackExtraPoints = 0.0;
      int numHandicapStones = hist.computeNumHandicapStones();
      if(hist.rules.scoringRule == Rules::SCORING_AREA) {
        if(numHandicapStones > 0)
          blackExtraPoints += numHandicapStones-1;
        bool blackGetsLastMove = (board.x_size % 2 == 1 && board.y_size % 2 == 1) == (numHandicapStones == 0 || numHandicapStones % 2 == 1);
        if(blackGetsLastMove)
          blackExtraPoints += 1;
      }
      if(numHandicapStones > 0 && hist.rules.whiteHandicapBonusRule == Rules::WHB_N)
        blackExtraPoints -= numHandicapStones;
      if(numHandicapStones > 0 && hist.rules.whiteHandicapBonusRule == Rules::WHB_N_MINUS_ONE)
        blackExtraPoints -= numHandicapStones-1;
      mirrorAdvantage = mirroringPla == P_BLACK ? blackExtraPoints - hist.rules.komi : hist.rules.komi - blackExtraPoints;
    }

    if(board.x_size >= 7 && board.y_size >= 7) {
      mirrorCenterSymmetryError = 0.0;
      int halfX = board.x_size / 2;
      int halfY = board.y_size / 2;
      int unmatchedMirrorPlaStones = 0;
      for(int dy = -3; dy <= 3; dy++) {
        for(int dx = -3; dx <= 3; dx++) {
          Loc loc = Location::getLoc(halfX+dx,halfY+dy,board.x_size);
          Loc mirrorLoc = Location::getMirrorLoc(loc,board.x_size,board.y_size);
          if(loc == mirrorLoc)
            continue;
          Color c0 = board.colors[loc];
          Color c1 = board.colors[mirrorLoc];
          if(c0 == getOpp(mirroringPla) && c1 != mirroringPla)
            mirrorCenterSymmetryError += 1.0;
          if(c0 == mirroringPla && c1 == C_EMPTY)
            unmatchedMirrorPlaStones += 1;
        }
      }
      if(mirrorCenterSymmetryError > 0.0)
        mirrorCenterSymmetryError += 0.2 * unmatchedMirrorPlaStones;
      if(mirrorCenterSymmetryError >= 1.0)
        mirrorCenterSymmetryError = 0.5 * mirrorCenterSymmetryError * (1.0 + mirrorCenterSymmetryError);
    }
  }
}

bool Search::isMirroringSinceSearchStart(const BoardHistory& threadHistory, int skipRecent) const {
  int xSize = threadHistory.initialBoard.x_size;
  int ySize = threadHistory.initialBoard.y_size;
  for(size_t i = rootHistory.moveHistory.size()+1; i+skipRecent < threadHistory.moveHistory.size(); i += 2) {
    if(threadHistory.moveHistory[i].loc != Location::getMirrorLoc(threadHistory.moveHistory[i-1].loc,xSize,ySize))
      return false;
  }
  return true;
}

void Search::maybeApplyAntiMirrorPolicy(
  float& nnPolicyProb,
  const Loc moveLoc,
  const float* policyProbs,
  const Player movePla,
  const SearchThread* thread
) const {
  int xSize = thread->board.x_size;
  int ySize = thread->board.y_size;

  double weight = 0.0;

  //Put significant prior probability on the opponent continuing to mirror, at least for the next few turns.
  if(movePla == getOpp(rootPla) && thread->history.moveHistory.size() > 0) {
    Loc prevLoc = thread->history.moveHistory[thread->history.moveHistory.size()-1].loc;
    if(prevLoc == Board::PASS_LOC)
      return;
    Loc mirrorLoc = Location::getMirrorLoc(prevLoc,xSize,ySize);
    if(policyProbs[getPos(mirrorLoc)] < 0)
      mirrorLoc = Board::PASS_LOC;
    if(moveLoc == mirrorLoc) {
      weight = 1.0;
      Loc centerLoc = Location::getCenterLoc(xSize,ySize);
      bool isDifficult = centerLoc != Board::NULL_LOC && thread->board.colors[centerLoc] == mirroringPla && mirrorAdvantage >= -0.5;
      if(isDifficult)
        weight *= 3.0;
    }
  }
  //Put a small prior on playing the center or attaching to center, bonusing moves that are relatively more likely.
  else if(movePla == rootPla && moveLoc != Board::PASS_LOC) {
    if(Location::isCentral(moveLoc,xSize,ySize))
      weight = 0.3;
    else {
      if(Location::isNearCentral(moveLoc,xSize,ySize))
        weight = 0.05;

      Loc centerLoc = Location::getCenterLoc(xSize,ySize);
      if(centerLoc != Board::NULL_LOC) {
        if(rootBoard.colors[centerLoc] == getOpp(movePla)) {
          if(thread->board.isAdjacentToChain(moveLoc,centerLoc))
            weight = 0.05;
          else {
            int distanceSq = Location::euclideanDistanceSquared(moveLoc,centerLoc,xSize);
            if(distanceSq <= 2)
              weight = 0.05;
            else if(distanceSq <= 4)
              weight = 0.03;
          }
        }
      }
    }
  }

  if(weight > 0) {
    weight = weight / (1.0 + sqrt(thread->history.moveHistory.size() - rootHistory.moveHistory.size()));
    nnPolicyProb = nnPolicyProb + (1.0f - nnPolicyProb) * (float)weight;
  }
}

//Force the search to dump playouts down a mirror move, so as to encourage moves that cause mirror moves
//to have bad values, and also tolerate us playing certain countering moves even if their values are a bit worse.
void Search::maybeApplyAntiMirrorForcedExplore(
  double& childUtility,
  const double parentUtility,
  const Loc moveLoc,
  const float* policyProbs,
  const double thisChildWeight,
  const double totalChildWeight,
  const Player movePla,
  const SearchThread* thread,
  const SearchNode& parent
) const {
  assert(mirroringPla == getOpp(rootPla));

  int xSize = thread->board.x_size;
  int ySize = thread->board.y_size;
  Loc centerLoc = Location::getCenterLoc(xSize,ySize);
  //The difficult case is when the opponent has occupied tengen, and ALSO the komi favors them.
  //In such a case, we're going to have a hard time.
  //Technically there are other configurations (like if the opponent makes a diamond around tengen)
  //but we're not going to worry about breaking that.
  bool isDifficult = centerLoc != Board::NULL_LOC && thread->board.colors[centerLoc] == mirroringPla && mirrorAdvantage >= -0.5;
  // bool isSemiDifficult = !isDifficult && mirrorAdvantage >= 6.5;
  bool isRoot = &parent == rootNode;

  //Force mirroring pla to dump playouts down mirror moves
  if(movePla == mirroringPla && thread->history.moveHistory.size() > 0) {
    Loc prevLoc = thread->history.moveHistory[thread->history.moveHistory.size()-1].loc;
    if(prevLoc == Board::PASS_LOC)
      return;
    Loc mirrorLoc = Location::getMirrorLoc(prevLoc,xSize,ySize);
    if(policyProbs[getPos(mirrorLoc)] < 0)
      mirrorLoc = Board::PASS_LOC;
    if(moveLoc == mirrorLoc) {
      double proportionToDump = 0.0;
      double proportionToBias = 0.0;
      if(isDifficult) {
        proportionToDump = 0.20;
        if(mirrorLoc != Board::PASS_LOC) {
          proportionToDump = std::max(
            proportionToDump,
            1.0 / (0.75 + 0.5 * sqrt(Location::euclideanDistanceSquared(centerLoc,mirrorLoc,xSize)))
            / std::max(1.0,mirrorCenterSymmetryError)
          );
        }
        proportionToBias = 0.75;
      }
      else if(mirrorAdvantage >= 5.0) {
        proportionToDump = 0.15;
        proportionToBias = 0.50;
      }
      else if(mirrorAdvantage >= -5.0) {
        proportionToDump = 0.10 + mirrorAdvantage;
        proportionToBias = 0.30 + mirrorAdvantage * 4;
      }
      else {
        proportionToDump = 0.05;
        proportionToBias = 0.10;
      }

      if(mirrorLoc == Board::PASS_LOC)
        proportionToDump *= (moveLoc == centerLoc ? 0.35 : 0.35 / std::max(1.0,sqrt(mirrorCenterSymmetryError)));
      if(mirrorCenterSymmetryError >= 1.0) {
        proportionToDump /= mirrorCenterSymmetryError;
        proportionToBias /= mirrorCenterSymmetryError;
      }

      if(thisChildWeight < proportionToDump * totalChildWeight) {
        childUtility += (parent.nextPla == P_WHITE ? 100.0 : -100.0);
      }
      if(thisChildWeight < proportionToBias * totalChildWeight) {
        childUtility += (parent.nextPla == P_WHITE ? 0.18 : -0.18) * std::max(0.3, 1.0 - 0.7 * parentUtility * parentUtility);
      }
      if(thisChildWeight < 0.5 * proportionToBias * totalChildWeight) {
        childUtility += (parent.nextPla == P_WHITE ? 0.36 : -0.36) * std::max(0.3, 1.0 - 0.7 * parentUtility * parentUtility);
      }
    }
  }
  //Encourage us to find refuting moves, even if they look a little bad, in the difficult case
  //Force us to dump playouts down tengen if possible, to encourage us to make tengen into a good move.
  else if(movePla == rootPla && moveLoc != Board::PASS_LOC) {
    double proportionToDump = 0.0;
    if(isDifficult) {
      if(thread->board.isAdjacentToChain(moveLoc,centerLoc)) {
        childUtility += (parent.nextPla == P_WHITE ? 0.75 : -0.75) / (1.0 + thread->board.getNumLiberties(centerLoc))
          / std::max(1.0,mirrorCenterSymmetryError) * std::max(0.3, 1.0 - 0.7 * parentUtility * parentUtility);
        proportionToDump = 0.10 / thread->board.getNumLiberties(centerLoc);
      }
      int distanceSq = Location::euclideanDistanceSquared(moveLoc,centerLoc,xSize);
      if(distanceSq <= 2)
        proportionToDump = std::max(proportionToDump, 0.010);
      else if(distanceSq <= 4)
        proportionToDump = std::max(proportionToDump, 0.005);

      //proportionToDump *= (1.0 / (1.0 + sqrt(thread->history.moveHistory.size() - rootHistory.moveHistory.size())));
    }
    if(moveLoc == centerLoc) {
      if(isRoot)
        proportionToDump = 0.06;
      else
        proportionToDump = 0.12;
    }

    double utilityLoss = (parent.nextPla == P_WHITE) ? parentUtility - childUtility : childUtility - parentUtility;
    if(utilityLoss > 0 && utilityLoss * proportionToDump > 0.03)
      proportionToDump += 0.5 * (0.03 / utilityLoss - proportionToDump);

    if(thread->history.moveHistory.size() > 0) {
      Loc prevLoc = thread->history.moveHistory[thread->history.moveHistory.size()-1].loc;
      if(prevLoc != Board::NULL_LOC && prevLoc != Board::PASS_LOC) {
        int centerDistanceSquared = Location::euclideanDistanceSquared(centerLoc,prevLoc,xSize);
        if(centerDistanceSquared <= 16)
          proportionToDump *= 0.900;
        if(centerDistanceSquared <= 5)
          proportionToDump *= 0.825;
        if(centerDistanceSquared <= 2)
          proportionToDump *= 0.750;
      }
    }

    if(thisChildWeight < proportionToDump * totalChildWeight) {
      childUtility += (parent.nextPla == P_WHITE ? 100.0 : -100.0);
    }
  }
}

void Search::hackNNOutputForMirror(std::shared_ptr<NNOutput>& result) const {
  // Root player gets a bonus/penalty based on the strength of the center.
  int centerPos = getPos(Location::getCenterLoc(rootBoard));
  double totalWLProb = result->whiteWinProb + result->whiteLossProb;
  double ownScale = mirrorCenterSymmetryError <= 0.0 ? 0.7 : 0.3;
  double wl = (result->whiteWinProb - result->whiteLossProb) / (totalWLProb+1e-10);
  wl = std::min(std::max(wl,-1.0+1e-15),1.0-1e-15);
  wl = tanh(atanh(wl) + ownScale * result->whiteOwnerMap[centerPos]);
  double whiteNewWinProb = 0.5 + 0.5 * wl;
  whiteNewWinProb = totalWLProb * whiteNewWinProb;

  result->whiteWinProb = (float)whiteNewWinProb;
  result->whiteLossProb = (float)(totalWLProb - whiteNewWinProb);
}

================
File: cpp/search/searchnnhelpers.cpp
================
#include "../search/search.h"

#include "../search/searchnode.h"

//------------------------
#include "../core/using.h"
//------------------------

void Search::computeRootNNEvaluation(NNResultBuf& nnResultBuf, bool includeOwnerMap) {
  Board board = rootBoard;
  const BoardHistory& hist = rootHistory;
  Player pla = rootPla;
  bool skipCache = false;
  bool isRoot = true;
  MiscNNInputParams nnInputParams;
  nnInputParams.drawEquivalentWinsForWhite = searchParams.drawEquivalentWinsForWhite;
  nnInputParams.conservativePassAndIsRoot = searchParams.conservativePass && isRoot;
  nnInputParams.enablePassingHacks = searchParams.enablePassingHacks;
  nnInputParams.nnPolicyTemperature = searchParams.nnPolicyTemperature;
  nnInputParams.avoidMYTDaggerHack = searchParams.avoidMYTDaggerHackPla == pla;
  nnInputParams.policyOptimism = searchParams.rootPolicyOptimism;
  if(searchParams.playoutDoublingAdvantage != 0) {
    Player playoutDoublingAdvantagePla = getPlayoutDoublingAdvantagePla();
    nnInputParams.playoutDoublingAdvantage = (
      getOpp(pla) == playoutDoublingAdvantagePla ? -searchParams.playoutDoublingAdvantage : searchParams.playoutDoublingAdvantage
    );
  }
  if(searchParams.ignorePreRootHistory || searchParams.ignoreAllHistory)
    nnInputParams.maxHistory = 0;
  nnEvaluator->evaluate(
    board, hist, pla, &searchParams.humanSLProfile,
    nnInputParams,
    nnResultBuf, skipCache, includeOwnerMap
  );
}

bool Search::needsHumanOutputAtRoot() const {
  return humanEvaluator != NULL && (searchParams.humanSLProfile.initialized || !humanEvaluator->requiresSGFMetadata());
}
bool Search::needsHumanOutputInTree() const {
  return needsHumanOutputAtRoot() && (
    searchParams.humanSLPlaExploreProbWeightless > 0 ||
    searchParams.humanSLPlaExploreProbWeightful > 0 ||
    searchParams.humanSLOppExploreProbWeightless > 0 ||
    searchParams.humanSLOppExploreProbWeightful > 0
  );
}

//If isReInit is false, among any threads trying to store, the first one wins
//If isReInit is true, we always replace, even for threads that come later.
//Returns true if a nnOutput was set where there was none before.
bool Search::initNodeNNOutput(
  SearchThread& thread, SearchNode& node,
  bool isRoot, bool skipCache, bool isReInit
) {
  bool includeOwnerMap = isRoot || alwaysIncludeOwnerMap;
  bool antiMirrorDifficult = false;
  if(searchParams.antiMirror && mirroringPla != C_EMPTY && mirrorAdvantage >= -0.5 &&
     Location::getCenterLoc(thread.board) != Board::NULL_LOC && thread.board.colors[Location::getCenterLoc(thread.board)] == getOpp(rootPla) &&
     isMirroringSinceSearchStart(thread.history,4) // skip recent 4 ply to be a bit tolerant
  ) {
    includeOwnerMap = true;
    antiMirrorDifficult = true;
  }
  MiscNNInputParams nnInputParams;
  nnInputParams.drawEquivalentWinsForWhite = searchParams.drawEquivalentWinsForWhite;
  nnInputParams.conservativePassAndIsRoot = searchParams.conservativePass && isRoot;
  nnInputParams.enablePassingHacks = searchParams.enablePassingHacks;
  nnInputParams.nnPolicyTemperature = searchParams.nnPolicyTemperature;
  nnInputParams.avoidMYTDaggerHack = searchParams.avoidMYTDaggerHackPla == thread.pla;
  nnInputParams.policyOptimism = isRoot ? searchParams.rootPolicyOptimism : searchParams.policyOptimism;
  if(searchParams.playoutDoublingAdvantage != 0) {
    Player playoutDoublingAdvantagePla = getPlayoutDoublingAdvantagePla();
    nnInputParams.playoutDoublingAdvantage = (
      getOpp(thread.pla) == playoutDoublingAdvantagePla ? -searchParams.playoutDoublingAdvantage : searchParams.playoutDoublingAdvantage
    );
  }
  if(searchParams.ignoreAllHistory)
    nnInputParams.maxHistory = 0;
  else if(searchParams.ignorePreRootHistory) {
    nnInputParams.maxHistory = isRoot ? 0 : std::max(0, (int)thread.history.moveHistory.size() - (int)rootHistory.moveHistory.size());
  }

  std::shared_ptr<NNOutput>* result = NULL;
  std::shared_ptr<NNOutput>* humanResult = NULL;
  if(isRoot && searchParams.rootNumSymmetriesToSample > 1) {
    result = nnEvaluator->averageMultipleSymmetries(
      thread.board, thread.history, thread.pla, &searchParams.humanSLProfile,
      nnInputParams,
      thread.nnResultBuf, includeOwnerMap,
      thread.rand, searchParams.rootNumSymmetriesToSample
    );
    if(needsHumanOutputInTree() || (isRoot && needsHumanOutputAtRoot())) {
      humanResult = humanEvaluator->averageMultipleSymmetries(
        thread.board, thread.history, thread.pla, &searchParams.humanSLProfile,
        nnInputParams,
        thread.nnResultBuf, includeOwnerMap,
        thread.rand, searchParams.rootNumSymmetriesToSample
      );
    }
  }
  else {
    nnEvaluator->evaluate(
      thread.board, thread.history, thread.pla, &searchParams.humanSLProfile,
      nnInputParams,
      thread.nnResultBuf, skipCache, includeOwnerMap
    );
    result = new std::shared_ptr<NNOutput>(std::move(thread.nnResultBuf.result));
    if(needsHumanOutputInTree() || (isRoot && needsHumanOutputAtRoot())) {
      humanEvaluator->evaluate(
        thread.board, thread.history, thread.pla, &searchParams.humanSLProfile,
        nnInputParams,
        thread.nnResultBuf, skipCache, includeOwnerMap
      );
      humanResult = new std::shared_ptr<NNOutput>(std::move(thread.nnResultBuf.result));
    }
  }

  if(antiMirrorDifficult) {
    // Copy
    std::shared_ptr<NNOutput>* newNNOutputSharedPtr = new std::shared_ptr<NNOutput>(new NNOutput(**result));
    std::shared_ptr<NNOutput>* tmp = result;
    result = newNNOutputSharedPtr;
    delete tmp;
    hackNNOutputForMirror(*result);
  }

  assert((*result)->noisedPolicyProbs == NULL);
  std::shared_ptr<NNOutput>* noisedResult = maybeAddPolicyNoiseAndTemp(thread,isRoot,result->get());
  if(noisedResult != NULL) {
    std::shared_ptr<NNOutput>* tmp = result;
    result = noisedResult;
    delete tmp;
  }

  node.nodeAge.store(searchNodeAge,std::memory_order_release);
  //If this is a re-initialization of the nnOutput, we don't want to add any visits or anything.
  //Also don't bother updating any of the stats. Technically we should do so because winLossValueSum
  //and such will have changed potentially due to a new orientation of the neural net eval
  //slightly affecting the evals, but this is annoying to recompute from scratch, and on the next
  //visit updateStatsAfterPlayout should fix it all up anyways.
  if(isReInit) {
    if(humanResult != NULL)
      node.storeHumanOutput(humanResult,thread); // ignore the wasNullBefore from this one
    bool wasNullBefore = node.storeNNOutput(result,thread);
    return wasNullBefore;
  }
  else {
    // Store human result first, so that the presence of the main result guarantees
    // that the human result exists in the case we have a human evaluator.
    if(humanResult != NULL) {
      bool humanSuc = node.storeHumanOutputIfNull(humanResult);
      if(!humanSuc)
        delete humanResult;
    }
    bool suc = node.storeNNOutputIfNull(result);
    if(!suc)
      delete result;
    else {
      addCurrentNNOutputAsLeafValue(node,true);
    }
    return suc;
  }
}


//Assumes node already has an nnOutput
bool Search::maybeRecomputeExistingNNOutput(
  SearchThread& thread, SearchNode& node, bool isRoot
) {
  bool recomputeHappened = false;
  //Right now only the root node currently ever needs to recompute, and only if it's old
  if(isRoot && node.nodeAge.load(std::memory_order_acquire) != searchNodeAge) {
    //See if we're the lucky thread that gets to do the update!
    //Threads that pass by here later will NOT wait for us to be done before proceeding with search.
    //We accept this and tolerate that for a few iterations potentially we will be using the OLD policy - without noise,
    //or without root temperature, etc.
    //Or if we have none of those things, then we'll not end up updating anything except the age, which is okay too.
    uint32_t oldAge = node.nodeAge.exchange(searchNodeAge,std::memory_order_acq_rel);
    if(oldAge < searchNodeAge) {
      NNOutput* nnOutput = node.getNNOutput();
      NNOutput* humanOutput = node.getHumanOutput();
      assert(nnOutput != NULL);

      //Recompute if we have no ownership map, since we need it for getEndingWhiteScoreBonus
      //If conservative passing, then we may also need to recompute the root policy ignoring the history if a pass ends the game
      //If averaging a bunch of symmetries, then we need to recompute it too
      //If root needs different optimism, we need to recompute it.
      //If humanSL is missing, but we want it, we need to recompute.
      //Also do so when ignoring history pre root
      if(nnOutput->whiteOwnerMap == NULL ||
         (searchParams.conservativePass && thread.history.passWouldEndGame(thread.board,thread.pla)) ||
         searchParams.rootNumSymmetriesToSample > 1 ||
         searchParams.rootPolicyOptimism != searchParams.policyOptimism ||
         (searchParams.ignorePreRootHistory && !searchParams.ignoreAllHistory) ||
         (humanOutput == NULL && needsHumanOutputAtRoot())
      ) {
        //We *can* use cached evaluations even though parameters are changing, because:
        //conservativePass is part of the nn hash
        //Symmetry averaging skips the cache on its own when it does symmetry sampling without replacement
        //The optimism is part of the nn hash
        //When pre-root history is ignored at the root, maxHistory is 0 and the nn cache distinguishes 0 from nonzero.
        const bool skipCache = false;
        initNodeNNOutput(thread,node,isRoot,skipCache,true);
        recomputeHappened = true;
      }
      //We also need to recompute the root nn if we have root noise or temperature and that's missing.
      else {
        //We don't need to go all the way to the nnEvaluator, we just need to maybe add those transforms
        //to the existing policy.
        std::shared_ptr<NNOutput>* result = maybeAddPolicyNoiseAndTemp(thread,isRoot,nnOutput);
        if(result != NULL) {
          node.storeNNOutput(result,thread);
          recomputeHappened = true;
        }
      }
    }
  }
  return recomputeHappened;
}

================
File: cpp/search/searchnode.cpp
================
#include "../search/searchnode.h"

#include "../search/search.h"
#include "../core/test.h"

NodeStatsAtomic::NodeStatsAtomic()
  :visits(0),
   winLossValueAvg(0.0),
   noResultValueAvg(0.0),
   scoreMeanAvg(0.0),
   scoreMeanSqAvg(0.0),
   leadAvg(0.0),
   utilityAvg(0.0),
   utilitySqAvg(0.0),
   weightSum(0.0),
   weightSqSum(0.0)
{}
NodeStatsAtomic::NodeStatsAtomic(const NodeStatsAtomic& other)
  :visits(other.visits.load(std::memory_order_acquire)),
   winLossValueAvg(other.winLossValueAvg.load(std::memory_order_acquire)),
   noResultValueAvg(other.noResultValueAvg.load(std::memory_order_acquire)),
   scoreMeanAvg(other.scoreMeanAvg.load(std::memory_order_acquire)),
   scoreMeanSqAvg(other.scoreMeanSqAvg.load(std::memory_order_acquire)),
   leadAvg(other.leadAvg.load(std::memory_order_acquire)),
   utilityAvg(other.utilityAvg.load(std::memory_order_acquire)),
   utilitySqAvg(other.utilitySqAvg.load(std::memory_order_acquire)),
   weightSum(other.weightSum.load(std::memory_order_acquire)),
   weightSqSum(other.weightSqSum.load(std::memory_order_acquire))
{}
NodeStatsAtomic::~NodeStatsAtomic()
{}

NodeStats::NodeStats()
  :visits(0),
   winLossValueAvg(0.0),
   noResultValueAvg(0.0),
   scoreMeanAvg(0.0),
   scoreMeanSqAvg(0.0),
   leadAvg(0.0),
   utilityAvg(0.0),
   utilitySqAvg(0.0),
   weightSum(0.0),
   weightSqSum(0.0)
{}
NodeStats::NodeStats(const NodeStatsAtomic& other)
  :visits(other.visits.load(std::memory_order_acquire)),
   winLossValueAvg(other.winLossValueAvg.load(std::memory_order_acquire)),
   noResultValueAvg(other.noResultValueAvg.load(std::memory_order_acquire)),
   scoreMeanAvg(other.scoreMeanAvg.load(std::memory_order_acquire)),
   scoreMeanSqAvg(other.scoreMeanSqAvg.load(std::memory_order_acquire)),
   leadAvg(other.leadAvg.load(std::memory_order_acquire)),
   utilityAvg(other.utilityAvg.load(std::memory_order_acquire)),
   utilitySqAvg(other.utilitySqAvg.load(std::memory_order_acquire)),
   weightSum(other.weightSum.load(std::memory_order_acquire)),
   weightSqSum(other.weightSqSum.load(std::memory_order_acquire))
{}
NodeStats::~NodeStats()
{}


//----------------------------------------------------------------------------------------


MoreNodeStats::MoreNodeStats()
  :stats(),
   selfUtility(0.0),
   weightAdjusted(0.0),
   prevMoveLoc(Board::NULL_LOC)
{}
MoreNodeStats::~MoreNodeStats()
{}


//----------------------------------------------------------------------------------------


SearchChildPointer::SearchChildPointer():
  data(NULL),
  edgeVisits(0),
  moveLoc(Board::NULL_LOC)
{}

void SearchChildPointer::storeAll(const SearchChildPointer& other) {
  SearchNode* d = other.data.load(std::memory_order_acquire);
  int64_t e = other.edgeVisits.load(std::memory_order_acquire);
  Loc m = other.moveLoc.load(std::memory_order_acquire);
  moveLoc.store(m,std::memory_order_release);
  edgeVisits.store(e,std::memory_order_release);
  data.store(d,std::memory_order_release);
}

SearchNode* SearchChildPointer::getIfAllocated() {
  return data.load(std::memory_order_acquire);
}

const SearchNode* SearchChildPointer::getIfAllocated() const {
  return data.load(std::memory_order_acquire);
}

SearchNode* SearchChildPointer::getIfAllocatedRelaxed() {
  return data.load(std::memory_order_relaxed);
}

void SearchChildPointer::store(SearchNode* node) {
  data.store(node, std::memory_order_release);
}

void SearchChildPointer::storeRelaxed(SearchNode* node) {
  data.store(node, std::memory_order_relaxed);
}

bool SearchChildPointer::storeIfNull(SearchNode* node) {
  SearchNode* expected = NULL;
  return data.compare_exchange_strong(expected, node, std::memory_order_acq_rel);
}

int64_t SearchChildPointer::getEdgeVisits() const {
  return edgeVisits.load(std::memory_order_acquire);
}
int64_t SearchChildPointer::getEdgeVisitsRelaxed() const {
  return edgeVisits.load(std::memory_order_relaxed);
}
void SearchChildPointer::setEdgeVisits(int64_t x) {
  edgeVisits.store(x, std::memory_order_release);
}
void SearchChildPointer::setEdgeVisitsRelaxed(int64_t x) {
  edgeVisits.store(x, std::memory_order_relaxed);
}
void SearchChildPointer::addEdgeVisits(int64_t delta) {
  edgeVisits.fetch_add(delta, std::memory_order_acq_rel);
}
bool SearchChildPointer::compexweakEdgeVisits(int64_t& expected, int64_t desired) {
  return edgeVisits.compare_exchange_weak(expected, desired, std::memory_order_acq_rel);
}


Loc SearchChildPointer::getMoveLoc() const {
  return moveLoc.load(std::memory_order_acquire);
}
Loc SearchChildPointer::getMoveLocRelaxed() const {
  return moveLoc.load(std::memory_order_relaxed);
}
void SearchChildPointer::setMoveLoc(Loc loc) {
  moveLoc.store(loc, std::memory_order_release);
}
void SearchChildPointer::setMoveLocRelaxed(Loc loc) {
  moveLoc.store(loc, std::memory_order_relaxed);
}


//-----------------------------------------------------------------------------------------


//Makes a search node resulting from prevPla playing prevLoc
SearchNode::SearchNode(Player pla, bool fnt, uint32_t mIdx)
  :nextPla(pla),
   forceNonTerminal(fnt),
   patternBonusHash(),
   mutexIdx(mIdx),
   state(SearchNode::STATE_UNEVALUATED),
   nnOutput(),
   humanOutput(),
   nodeAge(0),
   children0(NULL),
   children1(NULL),
   children2(NULL),
   stats(),
   virtualLosses(0),
   lastSubtreeValueBiasDeltaSum(0.0),
   lastSubtreeValueBiasWeight(0.0),
   subtreeValueBiasTableEntry(),
   dirtyCounter(0)
{
}

SearchNode::SearchNode(const SearchNode& other, bool fnt, bool copySubtreeValueBias)
  :nextPla(other.nextPla),
   forceNonTerminal(fnt),
   patternBonusHash(other.patternBonusHash),
   mutexIdx(other.mutexIdx),
   state(other.state.load(std::memory_order_acquire)),
   nnOutput(),
   humanOutput(),
   nodeAge(other.nodeAge.load(std::memory_order_acquire)),
   children0(NULL),
   children1(NULL),
   children2(NULL),
   stats(other.stats),
   virtualLosses(other.virtualLosses.load(std::memory_order_acquire)),
   lastSubtreeValueBiasDeltaSum(0.0),
   lastSubtreeValueBiasWeight(0.0),
   subtreeValueBiasTableEntry(),
   dirtyCounter(other.dirtyCounter.load(std::memory_order_acquire))
{
  {
    std::shared_ptr<NNOutput>* otherVal = other.nnOutput.load(std::memory_order_acquire);
    if(otherVal != NULL)
      nnOutput.store(new std::shared_ptr<NNOutput>(*otherVal), std::memory_order_release);
  }
  {
    std::shared_ptr<NNOutput>* otherVal = other.humanOutput.load(std::memory_order_acquire);
    if(otherVal != NULL)
      humanOutput.store(new std::shared_ptr<NNOutput>(*otherVal), std::memory_order_release);
  }
  if(other.children0 != NULL) {
    children0 = new SearchChildPointer[SearchChildrenSizes::SIZE0OVERFLOW];
    for(int i = 0; i<SearchChildrenSizes::SIZE0OVERFLOW; i++)
      children0[i].storeAll(other.children0[i]);
  }
  if(other.children1 != NULL) {
    children1 = new SearchChildPointer[SearchChildrenSizes::SIZE1OVERFLOW];
    for(int i = 0; i<SearchChildrenSizes::SIZE1OVERFLOW; i++)
      children1[i].storeAll(other.children1[i]);
  }
  if(other.children2 != NULL) {
    children2 = new SearchChildPointer[SearchChildrenSizes::SIZE2OVERFLOW];
    for(int i = 0; i<SearchChildrenSizes::SIZE2OVERFLOW; i++)
      children2[i].storeAll(other.children2[i]);
  }
  if(copySubtreeValueBias) {
    //Currently NOT implemented. If we ever want this, think very carefully about copying subtree value bias since
    //if we later delete this node we risk double-counting removal of the subtree value bias!
    assert(false);
    //lastSubtreeValueBiasDeltaSum = other.lastSubtreeValueBiasDeltaSum;
    //lastSubtreeValueBiasWeight = other.lastSubtreeValueBiasWeight;
    //subtreeValueBiasTableEntry = other.subtreeValueBiasTableEntry;
  }
}

SearchNodeChildrenReference SearchNode::getChildren() {
  return SearchNodeChildrenReference(state.load(std::memory_order_acquire),this);
}
ConstSearchNodeChildrenReference SearchNode::getChildren() const {
  return ConstSearchNodeChildrenReference(state.load(std::memory_order_acquire),this);
}
SearchNodeChildrenReference SearchNode::getChildren(SearchNodeState stateValue) {
  return SearchNodeChildrenReference(stateValue,this);
}
ConstSearchNodeChildrenReference SearchNode::getChildren(SearchNodeState stateValue) const {
  return ConstSearchNodeChildrenReference(stateValue,this);
}

static int getChildrenCapacity(SearchNodeState stateValue) {
  if(stateValue < SearchNode::STATE_EXPANDED0)
    return 0;
  else if(stateValue < SearchNode::STATE_EXPANDED1)
    return SearchChildrenSizes::SIZE0TOTAL;
  else if(stateValue < SearchNode::STATE_EXPANDED2)
    return SearchChildrenSizes::SIZE1TOTAL;
  else
    return SearchChildrenSizes::SIZE2TOTAL;
}

int SearchNodeChildrenReference::getCapacity() const {
  return getChildrenCapacity(snapshottedState);
}
int ConstSearchNodeChildrenReference::getCapacity() const {
  return getChildrenCapacity(snapshottedState);
}

int SearchNodeChildrenReference::iterateAndCountChildren() const {
  return ConstSearchNodeChildrenReference(*this).iterateAndCountChildren();
}

int ConstSearchNodeChildrenReference::iterateAndCountChildren() const {
  SearchChildPointer* arr;
  int arrCapacity;
  int offset;

  if(snapshottedState < SearchNode::STATE_EXPANDED0) {
    return 0;
  }
  else if(snapshottedState < SearchNode::STATE_EXPANDED1) {
    arr = node->children0;
    arrCapacity = SearchChildrenSizes::SIZE0OVERFLOW;
    offset = 0;
  }
  else if(snapshottedState < SearchNode::STATE_EXPANDED2) {
    arr = node->children1;
    arrCapacity = SearchChildrenSizes::SIZE1OVERFLOW;
    offset = SearchChildrenSizes::SIZE0TOTAL;
  }
  else {
    arr = node->children2;
    arrCapacity = SearchChildrenSizes::SIZE2OVERFLOW;
    offset = SearchChildrenSizes::SIZE1TOTAL;
  }

  for(int i = 0; i<arrCapacity; i++) {
    if(arr[i].getIfAllocated() == NULL)
      return offset + i;
  }
  return offset+arrCapacity;
}

//Precondition: Assumes that we have actually checked the children array that stateValue suggests that
//we should use, and that every slot in it is full up to numChildrenFullPlusOne-1, and
//that we have found a new legal child to add.
//Postcondition:
//Returns true: node state, stateValue, children arrays are all updated if needed so that they are large enough.
//Returns false: failure since another thread is handling it.
//Thread-safe.
bool SearchNode::maybeExpandChildrenCapacityForNewChild(SearchNodeState& stateValue, int numChildrenFullPlusOne) {
  int capacity = getChildrenCapacity(stateValue);
  if(capacity < numChildrenFullPlusOne) {
    assert(capacity == numChildrenFullPlusOne-1);
    return tryExpandingChildrenCapacityAssumeFull(stateValue);
  }
  return true;
}

void SearchNode::initializeChildren() {
  assert(children0 == NULL);
  children0 = new SearchChildPointer[SearchChildrenSizes::SIZE0OVERFLOW];
}

//Precondition: Assumes that we have actually checked the childen array that stateValue suggests that
//we should use, and that every slot in it is full.
bool SearchNode::tryExpandingChildrenCapacityAssumeFull(SearchNodeState& stateValue) {
  if(stateValue < SearchNode::STATE_EXPANDED1) {
    if(stateValue == SearchNode::STATE_GROWING1)
      return false;
    assert(stateValue == SearchNode::STATE_EXPANDED0);
    bool suc = state.compare_exchange_strong(stateValue,SearchNode::STATE_GROWING1,std::memory_order_acq_rel);
    if(!suc) return false;
    stateValue = SearchNode::STATE_GROWING1;

    SearchChildPointer* children = new SearchChildPointer[SearchChildrenSizes::SIZE1OVERFLOW];
    assert(children1 == NULL);
    children1 = children;
    state.store(SearchNode::STATE_EXPANDED1,std::memory_order_release);
    stateValue = SearchNode::STATE_EXPANDED1;
  }
  else if(stateValue < SearchNode::STATE_EXPANDED2) {
    if(stateValue == SearchNode::STATE_GROWING2)
      return false;
    assert(stateValue == SearchNode::STATE_EXPANDED1);
    bool suc = state.compare_exchange_strong(stateValue,SearchNode::STATE_GROWING2,std::memory_order_acq_rel);
    if(!suc) return false;
    stateValue = SearchNode::STATE_GROWING2;

    SearchChildPointer* children = new SearchChildPointer[SearchChildrenSizes::SIZE2OVERFLOW];
    assert(children2 == NULL);
    children2 = children;
    state.store(SearchNode::STATE_EXPANDED2,std::memory_order_release);
    stateValue = SearchNode::STATE_EXPANDED2;
  }
  else {
    ASSERT_UNREACHABLE;
  }
  return true;
}

//If we pruned some of the child nodes, collapse down the arrays and the node state to fit.
//This preserves the invariant that the level of expansion is only the minimum needed to hold those child nodes.
//Not thread-safe.
void SearchNode::collapseChildrenCapacity(int numGoodChildren) {
  int stateValue = state.load(std::memory_order_acquire);
  if(numGoodChildren <= SearchChildrenSizes::SIZE1TOTAL && stateValue > SearchNode::STATE_EXPANDED1) {
    assert(stateValue == SearchNode::STATE_EXPANDED2);
    assert(children2 != NULL);
    for(int i = 0; i<SearchChildrenSizes::SIZE2OVERFLOW; i++) {
      testAssert(children2[i].getIfAllocatedRelaxed() == NULL);
    }
    delete[] children2;
    children2 = NULL;
    stateValue = SearchNode::STATE_EXPANDED1;
    state.store(stateValue,std::memory_order_release);
  }
  if(numGoodChildren <= SearchChildrenSizes::SIZE0TOTAL && stateValue > SearchNode::STATE_EXPANDED0) {
    assert(stateValue == SearchNode::STATE_EXPANDED1);
    assert(children1 != NULL);
    for(int i = 0; i<SearchChildrenSizes::SIZE1OVERFLOW; i++) {
      testAssert(children1[i].getIfAllocatedRelaxed() == NULL);
    }
    delete[] children1;
    children1 = NULL;
    stateValue = SearchNode::STATE_EXPANDED0;
    state.store(stateValue,std::memory_order_release);
  }
}

NNOutput* SearchNode::getNNOutput() {
  std::shared_ptr<NNOutput>* nn = nnOutput.load(std::memory_order_acquire);
  if(nn == NULL)
    return NULL;
  return nn->get();
}

const NNOutput* SearchNode::getNNOutput() const {
  const std::shared_ptr<NNOutput>* nn = nnOutput.load(std::memory_order_acquire);
  if(nn == NULL)
    return NULL;
  return nn->get();
}

NNOutput* SearchNode::getHumanOutput() {
  std::shared_ptr<NNOutput>* nn = humanOutput.load(std::memory_order_acquire);
  if(nn == NULL)
    return NULL;
  return nn->get();
}

const NNOutput* SearchNode::getHumanOutput() const {
  const std::shared_ptr<NNOutput>* nn = humanOutput.load(std::memory_order_acquire);
  if(nn == NULL)
    return NULL;
  return nn->get();
}


bool SearchNode::storeNNOutput(std::shared_ptr<NNOutput>* newNNOutput, SearchThread& thread) {
  std::shared_ptr<NNOutput>* toCleanUp = nnOutput.exchange(newNNOutput, std::memory_order_acq_rel);
  if(toCleanUp != NULL) {
    thread.oldNNOutputsToCleanUp.push_back(toCleanUp);
    return false;
  }
  return true;
}

bool SearchNode::storeNNOutputIfNull(std::shared_ptr<NNOutput>* newNNOutput) {
  std::shared_ptr<NNOutput>* expected = NULL;
  return nnOutput.compare_exchange_strong(expected, newNNOutput, std::memory_order_acq_rel);
}


bool SearchNode::storeHumanOutput(std::shared_ptr<NNOutput>* newHumanOutput, SearchThread& thread) {
  std::shared_ptr<NNOutput>* toCleanUp = humanOutput.exchange(newHumanOutput, std::memory_order_acq_rel);
  if(toCleanUp != NULL) {
    thread.oldNNOutputsToCleanUp.push_back(toCleanUp);
    return false;
  }
  return true;
}

bool SearchNode::storeHumanOutputIfNull(std::shared_ptr<NNOutput>* newHumanOutput) {
  std::shared_ptr<NNOutput>* expected = NULL;
  return humanOutput.compare_exchange_strong(expected, newHumanOutput, std::memory_order_acq_rel);
}


SearchNode::~SearchNode() {
  // Do NOT recursively delete children
  // The children may have other references (e.g. graph search).
  if(children2 != NULL)
    delete[] children2;
  if(children1 != NULL)
    delete[] children1;
  if(children0 != NULL)
    delete[] children0;
  if(nnOutput != NULL)
    delete nnOutput;
  if(humanOutput != NULL)
    delete humanOutput;
}

================
File: cpp/search/searchnode.h
================
#ifndef SEARCH_SEARCHNODE_H_
#define SEARCH_SEARCHNODE_H_

#include "../core/global.h"
#include "../core/hash.h"
#include "../core/multithread.h"
#include "../game/boardhistory.h"
#include "../neuralnet/nneval.h"
#include "../search/subtreevaluebiastable.h"

typedef int SearchNodeState; // See SearchNode::STATE_*

struct SearchNode;
struct SearchThread;

struct NodeStatsAtomic {
  std::atomic<int64_t> visits;
  std::atomic<double> winLossValueAvg;
  std::atomic<double> noResultValueAvg;
  std::atomic<double> scoreMeanAvg;
  std::atomic<double> scoreMeanSqAvg;
  std::atomic<double> leadAvg;
  std::atomic<double> utilityAvg;
  std::atomic<double> utilitySqAvg;
  std::atomic<double> weightSum;
  std::atomic<double> weightSqSum;

  NodeStatsAtomic();
  explicit NodeStatsAtomic(const NodeStatsAtomic& other);
  ~NodeStatsAtomic();

  NodeStatsAtomic& operator=(const NodeStatsAtomic&) = delete;
  NodeStatsAtomic(NodeStatsAtomic&& other) = delete;
  NodeStatsAtomic& operator=(NodeStatsAtomic&& other) = delete;

  double getChildWeight(int64_t edgeVisits) const;
  double getChildWeight(int64_t edgeVisits, int64_t childVisits) const;
  double getChildWeightSq(int64_t edgeVisits) const;
  double getChildWeightSq(int64_t edgeVisits, int64_t childVisits) const;
};

struct NodeStats {
  int64_t visits;
  double winLossValueAvg;
  double noResultValueAvg;
  double scoreMeanAvg;
  double scoreMeanSqAvg;
  double leadAvg;
  double utilityAvg;
  double utilitySqAvg;
  double weightSum;
  double weightSqSum;

  NodeStats();
  explicit NodeStats(const NodeStatsAtomic& other);
  ~NodeStats();

  NodeStats(const NodeStats&) = default;
  NodeStats& operator=(const NodeStats&) = default;
  NodeStats(NodeStats&& other) = default;
  NodeStats& operator=(NodeStats&& other) = default;

  inline static double childWeight(int64_t edgeVisits, int64_t childVisits, double rawChildWeight) {
    return rawChildWeight * ((double)edgeVisits / (double)std::max(childVisits,(int64_t)1));
  }
  inline static double childWeightSq(int64_t edgeVisits, int64_t childVisits, double rawChildWeightSq) {
    return rawChildWeightSq * ((double)edgeVisits / (double)std::max(childVisits,(int64_t)1));
  }
  double getChildWeight(int64_t edgeVisits) {
    return childWeight(edgeVisits, visits, weightSum);
  }
};

inline double NodeStatsAtomic::getChildWeight(int64_t edgeVisits) const {
  return NodeStats::childWeight(edgeVisits, visits.load(std::memory_order_acquire), weightSum.load(std::memory_order_acquire));
}
inline double NodeStatsAtomic::getChildWeight(int64_t edgeVisits, int64_t childVisits) const {
  return NodeStats::childWeight(edgeVisits, childVisits, weightSum.load(std::memory_order_acquire));
}
inline double NodeStatsAtomic::getChildWeightSq(int64_t edgeVisits) const {
  return NodeStats::childWeightSq(edgeVisits, visits.load(std::memory_order_acquire), weightSqSum.load(std::memory_order_acquire));
}
inline double NodeStatsAtomic::getChildWeightSq(int64_t edgeVisits, int64_t childVisits) const {
  return NodeStats::childWeightSq(edgeVisits, childVisits, weightSqSum.load(std::memory_order_acquire));
}


struct MoreNodeStats {
  NodeStats stats;
  double selfUtility;
  double weightAdjusted;
  Loc prevMoveLoc;

  MoreNodeStats();
  ~MoreNodeStats();

  MoreNodeStats(const MoreNodeStats&) = default;
  MoreNodeStats& operator=(const MoreNodeStats&) = default;
  MoreNodeStats(MoreNodeStats&& other) = default;
  MoreNodeStats& operator=(MoreNodeStats&& other) = default;
};


struct SearchChildPointer {
private:
  std::atomic<SearchNode*> data;
  std::atomic<int64_t> edgeVisits;
  std::atomic<Loc> moveLoc; // Generally this will be always guarded under release semantics of data or of the array itself.
public:
  SearchChildPointer();

  SearchChildPointer(const SearchChildPointer&) = delete;
  SearchChildPointer& operator=(const SearchChildPointer&) = delete;
  SearchChildPointer(SearchChildPointer&& other) = delete;
  SearchChildPointer& operator=(SearchChildPointer&& other) = delete;

  void storeAll(const SearchChildPointer& other);

  SearchNode* getIfAllocated();
  const SearchNode* getIfAllocated() const;
  SearchNode* getIfAllocatedRelaxed();
  void store(SearchNode* node);
  void storeRelaxed(SearchNode* node);
  bool storeIfNull(SearchNode* node);

  int64_t getEdgeVisits() const;
  int64_t getEdgeVisitsRelaxed() const;
  void setEdgeVisits(int64_t x);
  void setEdgeVisitsRelaxed(int64_t x);
  void addEdgeVisits(int64_t delta);
  bool compexweakEdgeVisits(int64_t& expected, int64_t desired);

  Loc getMoveLoc() const;
  Loc getMoveLocRelaxed() const;
  void setMoveLoc(Loc loc);
  void setMoveLocRelaxed(Loc loc);
};

namespace SearchChildrenSizes {
  constexpr int SIZE0TOTAL = 8;
  constexpr int SIZE1TOTAL = 64;
  constexpr int SIZE2TOTAL = NNPos::MAX_NN_POLICY_SIZE;
  constexpr int SIZE0OVERFLOW = SIZE0TOTAL;
  constexpr int SIZE1OVERFLOW = SIZE1TOTAL - SIZE0TOTAL;
  constexpr int SIZE2OVERFLOW = SIZE2TOTAL - SIZE1TOTAL;
}

// Abstracts children{0,1,2} in SearchNode as a single concatenated array
struct SearchNodeChildrenReference {
  SearchNodeState snapshottedState;
  SearchNode* node;

  inline SearchNodeChildrenReference() {}
  inline SearchNodeChildrenReference(SearchNodeState snapshottedState_, SearchNode* node_)
    : snapshottedState(snapshottedState_), node(node_) {}
  SearchChildPointer& operator[](int i);
  int getCapacity() const;
  int iterateAndCountChildren() const;
};
struct ConstSearchNodeChildrenReference {
  int capacity;
  SearchNodeState snapshottedState;
  const SearchNode* node;

  inline ConstSearchNodeChildrenReference() {}
  inline ConstSearchNodeChildrenReference(const SearchNodeChildrenReference& other)
    : snapshottedState(other.snapshottedState), node(other.node) {}
  inline ConstSearchNodeChildrenReference(SearchNodeState snapshottedState_, const SearchNode* node_)
    : snapshottedState(snapshottedState_), node(node_) {}
  const SearchChildPointer& operator[](int i);
  int getCapacity() const;
  int iterateAndCountChildren() const;
};

struct SearchNode {
  //Locks------------------------------------------------------------------------------
  mutable std::atomic_flag statsLock = ATOMIC_FLAG_INIT;

  //Constant during search--------------------------------------------------------------
  const Player nextPla;
  const bool forceNonTerminal;
  Hash128 patternBonusHash;
  const uint32_t mutexIdx; // For lookup into mutex pool

  //Mutable---------------------------------------------------------------------------
  //During search, only ever transitions forward.
  std::atomic<SearchNodeState> state;
  static constexpr SearchNodeState STATE_UNEVALUATED = 0;
  static constexpr SearchNodeState STATE_EVALUATING = 1;
  static constexpr SearchNodeState STATE_EXPANDED0 = 2;
  static constexpr SearchNodeState STATE_GROWING1 = 3;
  static constexpr SearchNodeState STATE_EXPANDED1 = 4;
  static constexpr SearchNodeState STATE_GROWING2 = 5;
  static constexpr SearchNodeState STATE_EXPANDED2 = 6;

  //During search, will only ever transition from NULL -> non-NULL.
  //Guaranteed to be non-NULL once state >= STATE_EXPANDED0.
  //After this is non-NULL, might rarely change mid-search, but it is guaranteed that old values remain
  //valid to access for the duration of the search and will not be deallocated.
  std::atomic<std::shared_ptr<NNOutput>*> nnOutput;
  std::atomic<std::shared_ptr<NNOutput>*> humanOutput;

  //Used to coordinate various multithreaded updates.
  //During search, for updating nnOutput when it needs recomputation at the root if it wasn't updated yet.
  //During various other events - for coordinating recursive updates of the tree or subtree value bias cleanup
  std::atomic<uint32_t> nodeAge;

  //During search, each will only ever transition from NULL -> non-NULL.
  //We get progressive resizing of children array simply overflowing on to successive later arrays.
  //Mutex pool guards insertion of children at a node. Reading of children is always fine.
  SearchChildPointer* children0; //Guaranteed to be non-NULL once state >= STATE_EXPANDED0
  SearchChildPointer* children1; //Guaranteed to be non-NULL once state >= STATE_EXPANDED1
  SearchChildPointer* children2; //Guaranteed to be non-NULL once state >= STATE_EXPANDED2

  //Lightweight mutable---------------------------------------------------------------
  //Protected under statsLock for writing
  NodeStatsAtomic stats;
  std::atomic<int32_t> virtualLosses;

  //Protected under the entryLock in subtreeValueBiasTableEntry
  //Used only if subtreeValueBiasTableEntry is not nullptr.
  //During search, subtreeValueBiasTableEntry itself is set upon creation of the node and remains constant
  //thereafter, making it safe to access without synchronization.
  double lastSubtreeValueBiasDeltaSum;
  double lastSubtreeValueBiasWeight;
  std::shared_ptr<SubtreeValueBiasEntry> subtreeValueBiasTableEntry;

  std::atomic<int32_t> dirtyCounter;

  //--------------------------------------------------------------------------------
  SearchNode(Player prevPla, bool forceNonTerminal, uint32_t mutexIdx);
  SearchNode(const SearchNode&, bool forceNonTerminal, bool copySubtreeValueBias);
  ~SearchNode();

  SearchNode& operator=(const SearchNode&) = delete;
  SearchNode(SearchNode&& other) = delete;
  SearchNode& operator=(SearchNode&& other) = delete;

  //The array returned by these is guaranteed not to be deallocated during the lifetime of a search or even
  //any time up until a new operation is peformed (such as starting a new search, or making a move, or setting params).
  SearchNodeChildrenReference getChildren();
  ConstSearchNodeChildrenReference getChildren() const;
  SearchNodeChildrenReference getChildren(SearchNodeState state);
  ConstSearchNodeChildrenReference getChildren(SearchNodeState state) const;

  //The NNOutput returned by these is guaranteed not to be deallocated during the lifetime of a search or even
  //any time up until a new operation is peformed (such as starting a new search, or making a move, or setting params).
  NNOutput* getNNOutput();
  const NNOutput* getNNOutput() const;
  NNOutput* getHumanOutput();
  const NNOutput* getHumanOutput() const;

  //Always replaces the current nnoutput, and stores the existing one in the thread for later deletion.
  //Returns true if there was NOT already an nnOutput
  bool storeNNOutput(std::shared_ptr<NNOutput>* newNNOutput, SearchThread& thread);
  bool storeHumanOutput(std::shared_ptr<NNOutput>* newHumanOutput, SearchThread& thread);
  //Only stores if there isn't an nnOutput already. Returns true if it was stored.
  bool storeNNOutputIfNull(std::shared_ptr<NNOutput>* newNNOutput);
  bool storeHumanOutputIfNull(std::shared_ptr<NNOutput>* newHumanOutput);

  //Used within search to update state and allocate children arrays
  void initializeChildren();
  bool maybeExpandChildrenCapacityForNewChild(SearchNodeState& stateValue, int numChildrenFullPlusOne);
  void collapseChildrenCapacity(int numGoodChildren);

private:
  bool tryExpandingChildrenCapacityAssumeFull(SearchNodeState& stateValue);
};

inline SearchChildPointer& SearchNodeChildrenReference::operator[](int i) {
  if(i < SearchChildrenSizes::SIZE0TOTAL) return node->children0[i];
  if(i < SearchChildrenSizes::SIZE1TOTAL) return node->children1[i-SearchChildrenSizes::SIZE0TOTAL];
  return node->children2[i-SearchChildrenSizes::SIZE1TOTAL];
}
inline const SearchChildPointer& ConstSearchNodeChildrenReference::operator[](int i) {
  if(i < SearchChildrenSizes::SIZE0TOTAL) return node->children0[i];
  if(i < SearchChildrenSizes::SIZE1TOTAL) return node->children1[i-SearchChildrenSizes::SIZE0TOTAL];
  return node->children2[i-SearchChildrenSizes::SIZE1TOTAL];
}



#endif

================
File: cpp/search/searchnodetable.cpp
================
#include "../search/searchnodetable.h"

#include "../core/rand.h"
#include "../search/localpattern.h"

SearchNodeTable::SearchNodeTable(int numShardsPowerOfTwo) {
  numShards = (uint32_t)1 << numShardsPowerOfTwo;
  mutexPool = new MutexPool(numShards);
  entries.resize(numShards);
}
SearchNodeTable::~SearchNodeTable() {
  delete mutexPool;
}

uint32_t SearchNodeTable::getIndex(uint64_t hash) const {
  uint32_t mutexPoolMask = numShards-1; //Always a power of two
  return (uint32_t)(hash & mutexPoolMask);
}

================
File: cpp/search/searchnodetable.h
================
#ifndef SEARCH_SEARCHNODETABLE_H
#define SEARCH_SEARCHNODETABLE_H

#include "../core/global.h"
#include "../core/hash.h"
#include "../core/multithread.h"
#include "../game/board.h"
#include "../search/mutexpool.h"

struct SearchNode;

struct SearchNodeTable {
  std::vector<std::map<Hash128,SearchNode*>> entries;
  MutexPool* mutexPool;
  uint32_t numShards;

  SearchNodeTable(int numShardsPowerOfTwo);
  ~SearchNodeTable();

  uint32_t getIndex(uint64_t hash) const;
};

#endif

================
File: cpp/search/searchparams.cpp
================
#include "../search/searchparams.h"

#include "../external/nlohmann_json/json.hpp"

using nlohmann::json;

//Default search params
//The intent is that the are good default guesses for values of the parameters,
//with deterministic behavior (no noise, no randomization) and no bounds (unbounded time and visits).
//They are not necessarily the best parameters though, and have been kept mostly fixed over time even as things
//have changed to preserve the behavior of tests.
SearchParams::SearchParams()
  :winLossUtilityFactor(1.0),
   staticScoreUtilityFactor(0.3),
   dynamicScoreUtilityFactor(0.0),
   dynamicScoreCenterZeroWeight(0.0),
   dynamicScoreCenterScale(1.0),
   noResultUtilityForWhite(0.0),
   drawEquivalentWinsForWhite(0.5),
   cpuctExploration(1.0),
   cpuctExplorationLog(0.0),
   cpuctExplorationBase(500),
   cpuctUtilityStdevPrior(0.25),
   cpuctUtilityStdevPriorWeight(1.0),
   cpuctUtilityStdevScale(0.0),
   fpuReductionMax(0.2),
   fpuLossProp(0.0),
   fpuParentWeightByVisitedPolicy(false),
   fpuParentWeightByVisitedPolicyPow(1.0),
   fpuParentWeight(0.0),
   policyOptimism(0.0),
   valueWeightExponent(0.5),
   useNoisePruning(false),
   noisePruneUtilityScale(0.15),
   noisePruningCap(1e50),
   useUncertainty(false),
   uncertaintyCoeff(0.2),
   uncertaintyExponent(1.0),
   uncertaintyMaxWeight(8.0),
   useGraphSearch(false),
   graphSearchRepBound(11),
   graphSearchCatchUpLeakProb(0.0),
   //graphSearchCatchUpProp(0.0),
   rootNoiseEnabled(false),
   rootDirichletNoiseTotalConcentration(10.83),
   rootDirichletNoiseWeight(0.25),
   rootPolicyTemperature(1.0),
   rootPolicyTemperatureEarly(1.0),
   rootFpuReductionMax(0.2),
   rootFpuLossProp(0.0),
   rootNumSymmetriesToSample(1),
   rootSymmetryPruning(false),
   rootDesiredPerChildVisitsCoeff(0.0),
   rootPolicyOptimism(0.0),
   chosenMoveTemperature(0.0),
   chosenMoveTemperatureEarly(0.0),
   chosenMoveTemperatureHalflife(19),
   chosenMoveTemperatureOnlyBelowProb(1.0),
   chosenMoveSubtract(0.0),
   chosenMovePrune(1.0),
   useLcbForSelection(false),
   lcbStdevs(4.0),
   minVisitPropForLCB(0.05),
   useNonBuggyLcb(false),
   rootEndingBonusPoints(0.0),
   rootPruneUselessMoves(false),
   conservativePass(false),
   fillDameBeforePass(false),
   avoidMYTDaggerHackPla(C_EMPTY),
   wideRootNoise(0.0),
   enablePassingHacks(false),
   enableMorePassingHacks(false),
   playoutDoublingAdvantage(0.0),
   playoutDoublingAdvantagePla(C_EMPTY),
   avoidRepeatedPatternUtility(0.0),
   nnPolicyTemperature(1.0f),
   antiMirror(false),
   ignorePreRootHistory(false),
   ignoreAllHistory(false),
   subtreeValueBiasFactor(0.0),
   subtreeValueBiasTableNumShards(65536),
   subtreeValueBiasFreeProp(0.8),
   subtreeValueBiasWeightExponent(0.5),
   nodeTableShardsPowerOfTwo(16),
   numVirtualLossesPerThread(3.0),
   numThreads(1),
   minPlayoutsPerThread(0.0),
   maxVisits(((int64_t)1) << 50),
   maxPlayouts(((int64_t)1) << 50),
   maxTime(1.0e20),
   maxVisitsPondering(((int64_t)1) << 50),
   maxPlayoutsPondering(((int64_t)1) << 50),
   maxTimePondering(1.0e20),
   lagBuffer(0.0),
   searchFactorAfterOnePass(1.0),
   searchFactorAfterTwoPass(1.0),
   treeReuseCarryOverTimeFactor(0.0),
   overallocateTimeFactor(1.0),
   midgameTimeFactor(1.0),
   midgameTurnPeakTime(130.0),
   endgameTurnTimeDecay(100.0),
   obviousMovesTimeFactor(1.0),
   obviousMovesPolicyEntropyTolerance(0.30),
   obviousMovesPolicySurpriseTolerance(0.15),
   futileVisitsThreshold(0.0),
   humanSLProfile(),
   humanSLCpuctExploration(1.0),
   humanSLCpuctPermanent(0.0),
   humanSLRootExploreProbWeightless(0.0),
   humanSLRootExploreProbWeightful(0.0),
   humanSLPlaExploreProbWeightless(0.0),
   humanSLPlaExploreProbWeightful(0.0),
   humanSLOppExploreProbWeightless(0.0),
   humanSLOppExploreProbWeightful(0.0),
   humanSLChosenMoveProp(0.0),
   humanSLChosenMoveIgnorePass(false),
   humanSLChosenMovePiklLambda(1000000000.0)
{}

SearchParams::~SearchParams()
{}

bool SearchParams::operator==(const SearchParams& other) const {
  return (
    winLossUtilityFactor == other.winLossUtilityFactor &&
    staticScoreUtilityFactor == other.staticScoreUtilityFactor &&
    dynamicScoreUtilityFactor == other.dynamicScoreUtilityFactor &&
    dynamicScoreCenterZeroWeight == other.dynamicScoreCenterZeroWeight &&
    dynamicScoreCenterScale == other.dynamicScoreCenterScale &&
    noResultUtilityForWhite == other.noResultUtilityForWhite &&
    drawEquivalentWinsForWhite == other.drawEquivalentWinsForWhite &&

    cpuctExploration == other.cpuctExploration &&
    cpuctExplorationLog == other.cpuctExplorationLog &&
    cpuctExplorationBase == other.cpuctExplorationBase &&

    cpuctUtilityStdevPrior == other.cpuctUtilityStdevPrior &&
    cpuctUtilityStdevPriorWeight == other.cpuctUtilityStdevPriorWeight &&
    cpuctUtilityStdevScale == other.cpuctUtilityStdevScale &&

    fpuReductionMax == other.fpuReductionMax &&
    fpuLossProp == other.fpuLossProp &&

    fpuParentWeightByVisitedPolicy == other.fpuParentWeightByVisitedPolicy &&
    fpuParentWeightByVisitedPolicyPow == other.fpuParentWeightByVisitedPolicyPow &&
    fpuParentWeight == other.fpuParentWeight &&

    policyOptimism == other.policyOptimism &&

    valueWeightExponent == other.valueWeightExponent &&
    useNoisePruning == other.useNoisePruning &&
    noisePruneUtilityScale == other.noisePruneUtilityScale &&
    noisePruningCap == other.noisePruningCap &&

    useUncertainty == other.useUncertainty &&
    uncertaintyCoeff == other.uncertaintyCoeff &&
    uncertaintyExponent == other.uncertaintyExponent &&
    uncertaintyMaxWeight == other.uncertaintyMaxWeight &&

    useGraphSearch == other.useGraphSearch &&
    graphSearchRepBound == other.graphSearchRepBound &&
    graphSearchCatchUpLeakProb == other.graphSearchCatchUpLeakProb &&

    rootNoiseEnabled == other.rootNoiseEnabled &&
    rootDirichletNoiseTotalConcentration == other.rootDirichletNoiseTotalConcentration &&
    rootDirichletNoiseWeight == other.rootDirichletNoiseWeight &&

    rootPolicyTemperature == other.rootPolicyTemperature &&
    rootPolicyTemperatureEarly == other.rootPolicyTemperatureEarly &&
    rootFpuReductionMax == other.rootFpuReductionMax &&
    rootFpuLossProp == other.rootFpuLossProp &&
    rootNumSymmetriesToSample == other.rootNumSymmetriesToSample &&
    rootSymmetryPruning == other.rootSymmetryPruning &&
    rootDesiredPerChildVisitsCoeff == other.rootDesiredPerChildVisitsCoeff &&

    rootPolicyOptimism == other.rootPolicyOptimism &&

    chosenMoveTemperature == other.chosenMoveTemperature &&
    chosenMoveTemperatureEarly == other.chosenMoveTemperatureEarly &&
    chosenMoveTemperatureHalflife == other.chosenMoveTemperatureHalflife &&

    chosenMoveTemperatureOnlyBelowProb == other.chosenMoveTemperatureOnlyBelowProb &&
    chosenMoveSubtract == other.chosenMoveSubtract &&
    chosenMovePrune == other.chosenMovePrune &&

    useLcbForSelection == other.useLcbForSelection &&
    lcbStdevs == other.lcbStdevs &&
    minVisitPropForLCB == other.minVisitPropForLCB &&
    useNonBuggyLcb == other.useNonBuggyLcb &&

    rootEndingBonusPoints == other.rootEndingBonusPoints &&
    rootPruneUselessMoves == other.rootPruneUselessMoves &&
    conservativePass == other.conservativePass &&
    fillDameBeforePass == other.fillDameBeforePass &&
    avoidMYTDaggerHackPla == other.avoidMYTDaggerHackPla &&
    wideRootNoise == other.wideRootNoise &&
    enablePassingHacks == other.enablePassingHacks &&
    enableMorePassingHacks == other.enableMorePassingHacks &&

    playoutDoublingAdvantage == other.playoutDoublingAdvantage &&
    playoutDoublingAdvantagePla == other.playoutDoublingAdvantagePla &&

    avoidRepeatedPatternUtility == other.avoidRepeatedPatternUtility &&

    nnPolicyTemperature == other.nnPolicyTemperature &&
    antiMirror == other.antiMirror &&

    ignorePreRootHistory == other.ignorePreRootHistory &&
    ignoreAllHistory == other.ignoreAllHistory &&

    subtreeValueBiasFactor == other.subtreeValueBiasFactor &&
    subtreeValueBiasTableNumShards == other.subtreeValueBiasTableNumShards &&
    subtreeValueBiasFreeProp == other.subtreeValueBiasFreeProp &&
    subtreeValueBiasWeightExponent == other.subtreeValueBiasWeightExponent &&

    nodeTableShardsPowerOfTwo == other.nodeTableShardsPowerOfTwo &&
    numVirtualLossesPerThread == other.numVirtualLossesPerThread &&

    numThreads == other.numThreads &&
    minPlayoutsPerThread == other.minPlayoutsPerThread &&
    maxVisits == other.maxVisits &&
    maxPlayouts == other.maxPlayouts &&
    maxTime == other.maxTime &&

    maxVisitsPondering == other.maxVisitsPondering &&
    maxPlayoutsPondering == other.maxPlayoutsPondering &&
    maxTimePondering == other.maxTimePondering &&

    lagBuffer == other.lagBuffer &&

    searchFactorAfterOnePass == other.searchFactorAfterOnePass &&
    searchFactorAfterTwoPass == other.searchFactorAfterTwoPass &&

    treeReuseCarryOverTimeFactor == other.treeReuseCarryOverTimeFactor &&
    overallocateTimeFactor == other.overallocateTimeFactor &&
    midgameTimeFactor == other.midgameTimeFactor &&
    midgameTurnPeakTime == other.midgameTurnPeakTime &&
    endgameTurnTimeDecay == other.endgameTurnTimeDecay &&
    obviousMovesTimeFactor == other.obviousMovesTimeFactor &&
    obviousMovesPolicyEntropyTolerance == other.obviousMovesPolicyEntropyTolerance &&
    obviousMovesPolicySurpriseTolerance == other.obviousMovesPolicySurpriseTolerance &&

    futileVisitsThreshold == other.futileVisitsThreshold &&

    humanSLProfile == other.humanSLProfile &&
    humanSLCpuctExploration == other.humanSLCpuctExploration &&
    humanSLCpuctPermanent == other.humanSLCpuctPermanent &&
    humanSLRootExploreProbWeightless == other.humanSLRootExploreProbWeightless &&
    humanSLRootExploreProbWeightful == other.humanSLRootExploreProbWeightful &&
    humanSLPlaExploreProbWeightless == other.humanSLPlaExploreProbWeightless &&
    humanSLPlaExploreProbWeightful == other.humanSLPlaExploreProbWeightful &&
    humanSLOppExploreProbWeightless == other.humanSLOppExploreProbWeightless &&
    humanSLOppExploreProbWeightful == other.humanSLOppExploreProbWeightful &&

    humanSLChosenMoveProp == other.humanSLChosenMoveProp &&
    humanSLChosenMoveIgnorePass == other.humanSLChosenMoveIgnorePass &&
    humanSLChosenMovePiklLambda == other.humanSLChosenMovePiklLambda
  );
}

bool SearchParams::operator!=(const SearchParams& other) const {
  return !(*this == other);
}


SearchParams SearchParams::forTestsV1() {
  SearchParams params;
  params.staticScoreUtilityFactor = 0.1;
  params.dynamicScoreUtilityFactor = 0.3;
  params.dynamicScoreCenterZeroWeight = 0.2;
  params.dynamicScoreCenterScale = 0.75;
  params.cpuctExploration = 0.9;
  params.cpuctExplorationLog = 0.4;
  params.rootFpuReductionMax = 0.1;
  params.rootPolicyTemperatureEarly = 1.2;
  params.rootPolicyTemperature = 1.1;
  params.useLcbForSelection = true;
  params.lcbStdevs = 5;
  params.minVisitPropForLCB = 0.15;
  params.rootEndingBonusPoints = 0.5;
  params.rootPruneUselessMoves = true;
  params.conservativePass = true;
  params.useNonBuggyLcb = true;
  return params;
}

SearchParams SearchParams::forTestsV2() {
  SearchParams params;
  params.staticScoreUtilityFactor = 0.1;
  params.dynamicScoreUtilityFactor = 0.3;
  params.dynamicScoreCenterZeroWeight = 0.2;
  params.dynamicScoreCenterScale = 0.75;
  params.cpuctExploration = 0.9;
  params.cpuctExplorationLog = 0.4;
  params.rootFpuReductionMax = 0.1;
  params.rootPolicyTemperatureEarly = 1.2;
  params.rootPolicyTemperature = 1.1;
  params.useLcbForSelection = true;
  params.lcbStdevs = 5;
  params.minVisitPropForLCB = 0.15;
  params.rootEndingBonusPoints = 0.5;
  params.rootPruneUselessMoves = true;
  params.conservativePass = true;
  params.useNonBuggyLcb = true;
  params.useGraphSearch = true;
  params.fpuParentWeightByVisitedPolicy = true;
  params.valueWeightExponent = 0.25;
  params.useNoisePruning = true;
  params.useUncertainty = true;
  params.uncertaintyCoeff = 0.25;
  params.uncertaintyExponent = 1.0;
  params.uncertaintyMaxWeight = 8.0;
  params.cpuctUtilityStdevPrior = 0.40;
  params.cpuctUtilityStdevPriorWeight = 2.0;
  params.cpuctUtilityStdevScale = 0.85;
  params.fillDameBeforePass = true;
  params.subtreeValueBiasFactor = 0.45;
  params.subtreeValueBiasFreeProp = 0.8;
  params.subtreeValueBiasWeightExponent = 0.85;
  return params;
}

SearchParams SearchParams::basicDecentParams() {
  SearchParams params;
  params.staticScoreUtilityFactor = 0.1;
  params.dynamicScoreUtilityFactor = 0.3;
  params.dynamicScoreCenterZeroWeight = 0.2;
  params.dynamicScoreCenterScale = 0.75;
  params.cpuctExploration = 1.0;
  params.cpuctExplorationLog = 0.45;
  params.rootFpuReductionMax = 0.1;
  params.rootPolicyTemperatureEarly = 1.0;
  params.rootPolicyTemperature = 1.0;
  params.useLcbForSelection = true;
  params.lcbStdevs = 5;
  params.minVisitPropForLCB = 0.20;
  params.rootEndingBonusPoints = 0.5;
  params.rootPruneUselessMoves = true;
  params.conservativePass = true;
  params.enablePassingHacks = true;
  params.useNonBuggyLcb = true;
  params.useGraphSearch = true;
  params.fpuParentWeightByVisitedPolicy = true;
  params.valueWeightExponent = 0.25;
  params.useNoisePruning = true;
  params.useUncertainty = true;
  params.uncertaintyCoeff = 0.25;
  params.uncertaintyExponent = 1.0;
  params.uncertaintyMaxWeight = 8.0;
  params.cpuctUtilityStdevPrior = 0.40;
  params.cpuctUtilityStdevPriorWeight = 2.0;
  params.cpuctUtilityStdevScale = 0.85;
  params.fillDameBeforePass = true;
  params.subtreeValueBiasFactor = 0.45;
  params.subtreeValueBiasFreeProp = 0.8;
  params.subtreeValueBiasWeightExponent = 0.85;
  return params;
}

void SearchParams::failIfParamsDifferOnUnchangeableParameter(const SearchParams& initial, const SearchParams& dynamic) {
  if(dynamic.nodeTableShardsPowerOfTwo != initial.nodeTableShardsPowerOfTwo) {
    throw StringError("Cannot change nodeTableShardsPowerOfTwo after initialization");
  }
}

json SearchParams::changeableParametersToJson() const {
  json ret;
  ret["winLossUtilityFactor"] = winLossUtilityFactor;
  ret["staticScoreUtilityFactor"] = staticScoreUtilityFactor;
  ret["dynamicScoreUtilityFactor"] = dynamicScoreUtilityFactor;
  ret["dynamicScoreCenterZeroWeight"] = dynamicScoreCenterZeroWeight;
  ret["dynamicScoreCenterScale"] = dynamicScoreCenterScale;
  ret["noResultUtilityForWhite"] = noResultUtilityForWhite;
  ret["drawEquivalentWinsForWhite"] = drawEquivalentWinsForWhite;

  ret["cpuctExploration"] = cpuctExploration;
  ret["cpuctExplorationLog"] = cpuctExplorationLog;
  ret["cpuctExplorationBase"] = cpuctExplorationBase;

  ret["cpuctUtilityStdevPrior"] = cpuctUtilityStdevPrior;
  ret["cpuctUtilityStdevPriorWeight"] = cpuctUtilityStdevPriorWeight;
  ret["cpuctUtilityStdevScale"] = cpuctUtilityStdevScale;

  ret["fpuReductionMax"] = fpuReductionMax;
  ret["fpuLossProp"] = fpuLossProp;

  ret["fpuParentWeightByVisitedPolicy"] = fpuParentWeightByVisitedPolicy;
  ret["fpuParentWeightByVisitedPolicyPow"] = fpuParentWeightByVisitedPolicyPow;
  ret["fpuParentWeight"] = fpuParentWeight;

  ret["policyOptimism"] = policyOptimism;

  ret["valueWeightExponent"] = valueWeightExponent;
  ret["useNoisePruning"] = useNoisePruning;
  ret["noisePruneUtilityScale"] = noisePruneUtilityScale;
  ret["noisePruningCap"] = noisePruningCap;

  ret["useUncertainty"] = useUncertainty;
  ret["uncertaintyCoeff"] = uncertaintyCoeff;
  ret["uncertaintyExponent"] = uncertaintyExponent;
  ret["uncertaintyMaxWeight"] = uncertaintyMaxWeight;

  ret["useGraphSearch"] = useGraphSearch;
  ret["graphSearchRepBound"] = graphSearchRepBound;
  ret["graphSearchCatchUpLeakProb"] = graphSearchCatchUpLeakProb;

  ret["rootNoiseEnabled"] = rootNoiseEnabled;
  ret["rootDirichletNoiseTotalConcentration"] = rootDirichletNoiseTotalConcentration;
  ret["rootDirichletNoiseWeight"] = rootDirichletNoiseWeight;

  ret["rootPolicyTemperature"] = rootPolicyTemperature;
  ret["rootPolicyTemperatureEarly"] = rootPolicyTemperatureEarly;
  ret["rootFpuReductionMax"] = rootFpuReductionMax;
  ret["rootFpuLossProp"] = rootFpuLossProp;
  ret["rootNumSymmetriesToSample"] = rootNumSymmetriesToSample;
  ret["rootSymmetryPruning"] = rootSymmetryPruning;
  ret["rootDesiredPerChildVisitsCoeff"] = rootDesiredPerChildVisitsCoeff;

  ret["rootPolicyOptimism"] = rootPolicyOptimism;

  ret["chosenMoveTemperature"] = chosenMoveTemperature;
  ret["chosenMoveTemperatureEarly"] = chosenMoveTemperatureEarly;
  ret["chosenMoveTemperatureHalflife"] = chosenMoveTemperatureHalflife;
  ret["chosenMoveTemperatureOnlyBelowProb"] = chosenMoveTemperatureOnlyBelowProb;

  ret["chosenMoveSubtract"] = chosenMoveSubtract;
  ret["chosenMovePrune"] = chosenMovePrune;
  ret["useLcbForSelection"] = useLcbForSelection;

  ret["lcbStdevs"] = lcbStdevs;
  ret["minVisitPropForLCB"] = minVisitPropForLCB;
  ret["useNonBuggyLcb"] = useNonBuggyLcb;
  ret["rootEndingBonusPoints"] = rootEndingBonusPoints;

  ret["rootPruneUselessMoves"] = rootPruneUselessMoves;
  ret["conservativePass"] = conservativePass;
  ret["fillDameBeforePass"] = fillDameBeforePass;
  // Unused
  // ret["avoidMYTDaggerHackPla"] = PlayerIO::playerToStringShort(avoidMYTDaggerHackPla);
  ret["wideRootNoise"] = wideRootNoise;
  ret["enablePassingHacks"] = enablePassingHacks;
  ret["enableMorePassingHacks"] = enableMorePassingHacks;

  // Special handling in GTP
  ret["playoutDoublingAdvantage"] = playoutDoublingAdvantage;
  ret["playoutDoublingAdvantagePla"] = PlayerIO::playerToStringShort(playoutDoublingAdvantagePla);

  // Special handling in GTP
  // ret["avoidRepeatedPatternUtility"] = avoidRepeatedPatternUtility;

  ret["nnPolicyTemperature"] = nnPolicyTemperature;
  // Special handling in GTP
  // ret["antiMirror"] = antiMirror;

  ret["ignorePreRootHistory"] = ignorePreRootHistory;
  ret["ignoreAllHistory"] = ignoreAllHistory;

  ret["subtreeValueBiasFactor"] = subtreeValueBiasFactor;
  ret["subtreeValueBiasTableNumShards"] = subtreeValueBiasTableNumShards;
  ret["subtreeValueBiasFreeProp"] = subtreeValueBiasFreeProp;
  ret["subtreeValueBiasWeightExponent"] = subtreeValueBiasWeightExponent;

  // ret["nodeTableShardsPowerOfTwo"] = nodeTableShardsPowerOfTwo;
  ret["numVirtualLossesPerThread"] = numVirtualLossesPerThread;

  ret["numSearchThreads"] = numThreads; // NOTE: different name since that's how setup.cpp loads it
  ret["minPlayoutsPerThread"] = minPlayoutsPerThread;
  ret["maxVisits"] = maxVisits;
  ret["maxPlayouts"] = maxPlayouts;
  ret["maxTime"] = maxTime;

  ret["maxVisitsPondering"] = maxVisitsPondering;
  ret["maxPlayoutsPondering"] = maxPlayoutsPondering;
  ret["maxTimePondering"] = maxTimePondering;

  ret["lagBuffer"] = lagBuffer;

  ret["searchFactorAfterOnePass"] = searchFactorAfterOnePass;
  ret["searchFactorAfterTwoPass"] = searchFactorAfterTwoPass;

  ret["treeReuseCarryOverTimeFactor"] = treeReuseCarryOverTimeFactor;
  ret["overallocateTimeFactor"] = overallocateTimeFactor;
  ret["midgameTimeFactor"] = midgameTimeFactor;
  ret["midgameTurnPeakTime"] = midgameTurnPeakTime;
  ret["endgameTurnTimeDecay"] = endgameTurnTimeDecay;
  ret["obviousMovesTimeFactor"] = obviousMovesTimeFactor;
  ret["obviousMovesPolicyEntropyTolerance"] = obviousMovesPolicyEntropyTolerance;
  ret["obviousMovesPolicySurpriseTolerance"] = obviousMovesPolicySurpriseTolerance;

  ret["futileVisitsThreshold"] = futileVisitsThreshold;

  ret["humanSLCpuctExploration"] = humanSLCpuctExploration;
  ret["humanSLCpuctPermanent"] = humanSLCpuctPermanent;

  ret["humanSLRootExploreProbWeightless"] = humanSLRootExploreProbWeightless;
  ret["humanSLRootExploreProbWeightful"] = humanSLRootExploreProbWeightful;
  ret["humanSLPlaExploreProbWeightless"] = humanSLPlaExploreProbWeightless;
  ret["humanSLPlaExploreProbWeightful"] = humanSLPlaExploreProbWeightful;
  ret["humanSLOppExploreProbWeightless"] = humanSLOppExploreProbWeightless;
  ret["humanSLOppExploreProbWeightful"] = humanSLOppExploreProbWeightful;

  ret["humanSLChosenMoveProp"] = humanSLChosenMoveProp;
  ret["humanSLChosenMoveIgnorePass"] = humanSLChosenMoveIgnorePass;
  ret["humanSLChosenMovePiklLambda"] = humanSLChosenMovePiklLambda;

  return ret;
}

#define PRINTPARAM(PARAMNAME) out << #PARAMNAME << ": " << PARAMNAME << std::endl;
void SearchParams::printParams(std::ostream& out) const {


  PRINTPARAM(winLossUtilityFactor);
  PRINTPARAM(staticScoreUtilityFactor);
  PRINTPARAM(dynamicScoreUtilityFactor);
  PRINTPARAM(dynamicScoreCenterZeroWeight);
  PRINTPARAM(dynamicScoreCenterScale);
  PRINTPARAM(noResultUtilityForWhite);
  PRINTPARAM(drawEquivalentWinsForWhite);

  PRINTPARAM(cpuctExploration);
  PRINTPARAM(cpuctExplorationLog);
  PRINTPARAM(cpuctExplorationBase);

  PRINTPARAM(cpuctUtilityStdevPrior);
  PRINTPARAM(cpuctUtilityStdevPriorWeight);
  PRINTPARAM(cpuctUtilityStdevScale);

  PRINTPARAM(fpuReductionMax);
  PRINTPARAM(fpuLossProp);

  PRINTPARAM(fpuParentWeightByVisitedPolicy);
  PRINTPARAM(fpuParentWeightByVisitedPolicyPow);
  PRINTPARAM(fpuParentWeight);

  PRINTPARAM(policyOptimism);

  PRINTPARAM(valueWeightExponent);
  PRINTPARAM(useNoisePruning);
  PRINTPARAM(noisePruneUtilityScale);
  PRINTPARAM(noisePruningCap);


  PRINTPARAM(useUncertainty);
  PRINTPARAM(uncertaintyCoeff);
  PRINTPARAM(uncertaintyExponent);
  PRINTPARAM(uncertaintyMaxWeight);


  PRINTPARAM(useGraphSearch);
  PRINTPARAM(graphSearchRepBound);
  PRINTPARAM(graphSearchCatchUpLeakProb);



  PRINTPARAM(rootNoiseEnabled);
  PRINTPARAM(rootDirichletNoiseTotalConcentration);
  PRINTPARAM(rootDirichletNoiseWeight);

  PRINTPARAM(rootPolicyTemperature);
  PRINTPARAM(rootPolicyTemperatureEarly);
  PRINTPARAM(rootFpuReductionMax);
  PRINTPARAM(rootFpuLossProp);
  PRINTPARAM(rootNumSymmetriesToSample);
  PRINTPARAM(rootSymmetryPruning);

  PRINTPARAM(rootDesiredPerChildVisitsCoeff);

  PRINTPARAM(rootPolicyOptimism);

  PRINTPARAM(chosenMoveTemperature);
  PRINTPARAM(chosenMoveTemperatureEarly);
  PRINTPARAM(chosenMoveTemperatureHalflife);
  PRINTPARAM(chosenMoveTemperatureOnlyBelowProb);
  PRINTPARAM(chosenMoveSubtract);
  PRINTPARAM(chosenMovePrune);

  PRINTPARAM(useLcbForSelection);
  PRINTPARAM(lcbStdevs);
  PRINTPARAM(minVisitPropForLCB);
  PRINTPARAM(useNonBuggyLcb);


  PRINTPARAM(rootEndingBonusPoints);
  PRINTPARAM(rootPruneUselessMoves);
  PRINTPARAM(conservativePass);
  PRINTPARAM(fillDameBeforePass);
  std::cout << "avoidMYTDaggerHackPla" << ": " << (int)avoidMYTDaggerHackPla << std::endl;
  PRINTPARAM(wideRootNoise);
  PRINTPARAM(enablePassingHacks);
  PRINTPARAM(enableMorePassingHacks);

  PRINTPARAM(playoutDoublingAdvantage);
  std::cout << "playoutDoublingAdvantagePla" << ": " << (int)playoutDoublingAdvantagePla << std::endl;

  PRINTPARAM(avoidRepeatedPatternUtility);

  PRINTPARAM(nnPolicyTemperature);
  PRINTPARAM(antiMirror);

  PRINTPARAM(ignorePreRootHistory);
  PRINTPARAM(ignoreAllHistory);

  PRINTPARAM(subtreeValueBiasFactor);
  PRINTPARAM(subtreeValueBiasTableNumShards);
  PRINTPARAM(subtreeValueBiasFreeProp);
  PRINTPARAM(subtreeValueBiasWeightExponent);


  PRINTPARAM(nodeTableShardsPowerOfTwo);
  PRINTPARAM(numVirtualLossesPerThread);


  PRINTPARAM(numThreads);
  PRINTPARAM(minPlayoutsPerThread);
  PRINTPARAM(maxVisits);
  PRINTPARAM(maxPlayouts);
  PRINTPARAM(maxTime);


  PRINTPARAM(maxVisitsPondering);
  PRINTPARAM(maxPlayoutsPondering);
  PRINTPARAM(maxTimePondering);


  PRINTPARAM(lagBuffer);


  PRINTPARAM(searchFactorAfterOnePass);
  PRINTPARAM(searchFactorAfterTwoPass);


  PRINTPARAM(treeReuseCarryOverTimeFactor);
  PRINTPARAM(overallocateTimeFactor);
  PRINTPARAM(midgameTimeFactor);
  PRINTPARAM(midgameTurnPeakTime);
  PRINTPARAM(endgameTurnTimeDecay);
  PRINTPARAM(obviousMovesTimeFactor);
  PRINTPARAM(obviousMovesPolicyEntropyTolerance);
  PRINTPARAM(obviousMovesPolicySurpriseTolerance);

  PRINTPARAM(futileVisitsThreshold);


  PRINTPARAM(humanSLCpuctExploration);
  PRINTPARAM(humanSLCpuctPermanent);
  PRINTPARAM(humanSLRootExploreProbWeightless);
  PRINTPARAM(humanSLRootExploreProbWeightful);
  PRINTPARAM(humanSLPlaExploreProbWeightless);
  PRINTPARAM(humanSLPlaExploreProbWeightful);
  PRINTPARAM(humanSLOppExploreProbWeightless);
  PRINTPARAM(humanSLOppExploreProbWeightful);
  PRINTPARAM(humanSLChosenMoveProp);
  PRINTPARAM(humanSLChosenMoveIgnorePass);
  PRINTPARAM(humanSLChosenMovePiklLambda);

}

================
File: cpp/search/searchparams.h
================
#ifndef SEARCH_SEARCHPARAMS_H_
#define SEARCH_SEARCHPARAMS_H_

#include "../core/global.h"
#include "../game/board.h"
#include "../neuralnet/sgfmetadata.h"

#include "../external/nlohmann_json/json.hpp"

struct SearchParams {
  //Utility function parameters
  double winLossUtilityFactor;     //Scaling for [-1,1] value for winning/losing
  double staticScoreUtilityFactor; //Scaling for a [-1,1] "scoreValue" for having more/fewer points, centered at 0.
  double dynamicScoreUtilityFactor; //Scaling for a [-1,1] "scoreValue" for having more/fewer points, centered at recent estimated expected score.
  double dynamicScoreCenterZeroWeight; //Adjust dynamic score center this proportion of the way towards zero, capped at a reasonable amount.
  double dynamicScoreCenterScale; //Adjust dynamic score scale. 1.0 indicates that score is cared about roughly up to board sizeish.
  double noResultUtilityForWhite; //Utility of having a no-result game (simple ko rules or nonterminating territory encore)
  double drawEquivalentWinsForWhite; //Consider a draw to be this many wins and one minus this many losses.

  //Search tree exploration parameters
  double cpuctExploration;  //Constant factor on exploration, should also scale up linearly with magnitude of utility
  double cpuctExplorationLog; //Constant factor on log-scaling exploration, should also scale up linearly with magnitude of utility
  double cpuctExplorationBase; //Scale of number of visits at which log behavior starts having an effect

  double cpuctUtilityStdevPrior;
  double cpuctUtilityStdevPriorWeight;
  double cpuctUtilityStdevScale;

  double fpuReductionMax;   //Max amount to reduce fpu value for unexplore children
  double fpuLossProp; //Scale fpu this proportion of the way towards assuming a move is a loss.

  bool fpuParentWeightByVisitedPolicy; //For fpu, blend between parent average and parent nn value based on proportion of policy visited.
  double fpuParentWeightByVisitedPolicyPow; //If fpuParentWeightByVisitedPolicy, what power to raise the proportion of policy visited for blending.
  double fpuParentWeight; //For fpu, 0 = use parent average, 1 = use parent nn value, interpolates between.

  double policyOptimism; //Interpolate geometrically between raw policy and optimistic policy

  //Tree value aggregation parameters
  double valueWeightExponent; //Amount to apply a downweighting of children with very bad values relative to good ones
  bool useNoisePruning; //For computation of value, prune out weight that greatly exceeds what is justified by policy prior
  double noisePruneUtilityScale; //The scale of the utility difference at which useNoisePruning has effect
  double noisePruningCap; //Maximum amount of weight that noisePruning can remove

  //Uncertainty weighting
  bool useUncertainty; //Weight visits by uncertainty
  double uncertaintyCoeff; //The amount of visits weight that an uncertainty of 1 utility is.
  double uncertaintyExponent; //Visits weight scales inversely with this power of the uncertainty
  double uncertaintyMaxWeight; //Add minimum uncertainty so that the most weight a node can have is this

  //Graph search
  bool useGraphSearch; //Enable graph search instead of tree search?
  int graphSearchRepBound; //Rep bound to use for graph search transposition safety. Higher will reduce transpositions but be more safe.
  double graphSearchCatchUpLeakProb; //Chance to perform a visit to deepen a branch anyways despite being behind on visit count.
  //double graphSearchCatchUpProp; //When sufficiently far behind on visits on a transposition, catch up extra by adding up to this fraction of parents visits at once.

  //Root parameters
  bool rootNoiseEnabled;
  double rootDirichletNoiseTotalConcentration; //Same as alpha * board size, to match alphazero this might be 0.03 * 361, total number of balls in the urn
  double rootDirichletNoiseWeight; //Policy at root is this weight * noise + (1 - this weight) * nn policy

  double rootPolicyTemperature; //At the root node, scale policy probs by this power
  double rootPolicyTemperatureEarly; //At the root node, scale policy probs by this power, early in the game
  double rootFpuReductionMax; //Same as fpuReductionMax, but at root
  double rootFpuLossProp; //Same as fpuLossProp, but at root
  int rootNumSymmetriesToSample; //For the root node, sample this many random symmetries (WITHOUT replacement) and average the results together.
  bool rootSymmetryPruning; //For the root node, search only one copy of each symmetrically equivalent move.
  //We use the min of these two together, and also excess visits get pruned if the value turns out bad.
  double rootDesiredPerChildVisitsCoeff; //Funnel sqrt(this * policy prob * total visits) down any given child that receives any visits at all at the root

  double rootPolicyOptimism; //Interpolate geometrically between raw policy and optimistic policy

  //Parameters for choosing the move to play
  double chosenMoveTemperature; //Make move roughly proportional to visit count ** (1/chosenMoveTemperature)
  double chosenMoveTemperatureEarly; //Temperature at start of game
  double chosenMoveTemperatureHalflife; //Halflife of decay from early temperature to temperature for the rest of the game, scales for board sizes other than 19.
  double chosenMoveTemperatureOnlyBelowProb; //chosenMoveTemperature only begins dampening moves that pre-temperature are less likely than this.
  double chosenMoveSubtract; //Try to subtract this many visits from every move prior to applying temperature
  double chosenMovePrune; //Outright prune moves that have fewer than this many visits

  bool useLcbForSelection; //Using LCB for move selection?
  double lcbStdevs; //How many stdevs a move needs to be better than another for LCB selection
  double minVisitPropForLCB; //Only use LCB override when a move has this proportion of visits as the top move
  bool useNonBuggyLcb; //LCB was very minorly buggy as of pre-v1.8. Set to true to fix.

  //Mild behavior hackery
  double rootEndingBonusPoints; //Extra bonus (or penalty) to encourage good passing behavior at the end of the game.
  bool rootPruneUselessMoves; //Prune moves that are entirely useless moves that prolong the game.
  bool conservativePass; //Never assume one's own pass will end the game.
  bool fillDameBeforePass; //When territory scoring, heuristically discourage passing before filling the dame.
  Player avoidMYTDaggerHackPla; //Hacky hack to avoid a particular pattern that gives some KG nets some trouble. Should become unnecessary in the future.
  double wideRootNoise; //Explore at the root more widely
  bool enablePassingHacks; //Enable some hacks that mitigate rare instances when passing messes up deeper searches.
  bool enableMorePassingHacks; //Always weightless search passing and non passing moves when a pass would end the phase after a few visits.

  double playoutDoublingAdvantage; //Play as if we have this many doublings of playouts vs the opponent
  Player playoutDoublingAdvantagePla; //Negate playoutDoublingAdvantage when making a move for the opponent of this player. If empty, opponent of the root player.

  double avoidRepeatedPatternUtility; //Have the root player avoid repeating similar shapes, penalizing this much utility per instance.

  float nnPolicyTemperature; //Scale neural net policy probabilities by this temperature, applies everywhere in the tree
  bool antiMirror; //Enable anti-mirroring logic

  //Ignore history prior to the root of the search. This is enforced strictly only for the root node of the
  //search. Deeper nodes may see history prior to the root of the search if searches were performed from earlier positions
  //and those gamestates were also reached by those earlier searches with the nn evals cached.
  //This is true even without tree reuse.
  bool ignorePreRootHistory;
  bool ignoreAllHistory; //Always ignore history entirely

  double subtreeValueBiasFactor; //Dynamically adjust neural net utilties based on empirical stats about their errors in search
  int32_t subtreeValueBiasTableNumShards; //Number of shards for subtreeValueBiasFactor for initial hash lookup and mutexing
  double subtreeValueBiasFreeProp; //When a node is no longer part of the relevant search tree, only decay this proportion of the weight.
  double subtreeValueBiasWeightExponent; //When computing empiricial bias, weight subtree results by childvisits to this power.

  //Threading-related
  int nodeTableShardsPowerOfTwo; //Controls number of shards of node table for graph search transposition lookup
  double numVirtualLossesPerThread; //Number of virtual losses for one thread to add

  //Asyncbot
  int numThreads; //Number of threads
  double minPlayoutsPerThread; //If the number of playouts to perform per thread is smaller than this, cap the number of threads used.
  int64_t maxVisits; //Max number of playouts from the root to think for, counting earlier playouts from tree reuse
  int64_t maxPlayouts; //Max number of playouts from the root to think for, not counting earlier playouts from tree reuse
  double maxTime; //Max number of seconds to think for

  //Same caps but when pondering
  int64_t maxVisitsPondering;
  int64_t maxPlayoutsPondering;
  double maxTimePondering;

  //Amount of time to reserve for lag when using a time control
  double lagBuffer;

  //Human-friendliness
  double searchFactorAfterOnePass; //Multiply playouts and visits and time by this much after a pass by the opponent
  double searchFactorAfterTwoPass; //Multiply playouts and visits and time by this after two passes by the opponent

  //Time control
  double treeReuseCarryOverTimeFactor; //Assume we gain this much "time" on the next move purely from % tree preserved * time spend on that tree.
  double overallocateTimeFactor; //Prefer to think this factor longer than recommended by base level time control
  double midgameTimeFactor; //Think this factor longer in the midgame, proportional to midgame weight
  double midgameTurnPeakTime; //The turn considered to have midgame weight 1.0, rising up from 0.0 in the opening, for 19x19
  double endgameTurnTimeDecay; //The scale of exponential decay of midgame weight back to 1.0, for 19x19
  double obviousMovesTimeFactor; //Think up to this factor longer on obvious moves, weighted by obviousness
  double obviousMovesPolicyEntropyTolerance; //What entropy does the policy need to be at most to be (1/e) obvious?
  double obviousMovesPolicySurpriseTolerance; //What logits of surprise does the search result need to be at most to be (1/e) obvious?

  double futileVisitsThreshold; //If a move would not be able to match this proportion of the max visits move in the time or visit or playout cap remaining, prune it.

  //Human SL network
  SGFMetadata humanSLProfile;  //For any human SL model, make the net predict this rank / source / profile of player.
  double humanSLCpuctExploration;  //Use this cpuct for -human-model. This divided by sqrt(visits) is the utility diff to majorly affect the posterior.
  double humanSLCpuctPermanent;    //Same, but multiplied by sqrt(visits). This is the utility diff to majorly affect the posterior.

  //Probability that a playout selects a move using the human SL net instead of the main net.
  //Weightless - search the move to better judge it but do NOT weight that playout for the parent's value.
  //Weightful - search the move and DO weight that playout for the parent's value. (Note: consider disabling useNoisePruning, which uses katago's policy).
  double humanSLRootExploreProbWeightless;
  double humanSLRootExploreProbWeightful;
  double humanSLPlaExploreProbWeightless;
  double humanSLPlaExploreProbWeightful;
  double humanSLOppExploreProbWeightless;
  double humanSLOppExploreProbWeightful;

  //These three are PRIOR to the normal chosenMoveTemperature.
  double humanSLChosenMoveProp; //Proportion of final move selection probability using human SL policy
  bool humanSLChosenMoveIgnorePass; //If true, ignore human SL pass probability and use KataGo's passing logic
  double humanSLChosenMovePiklLambda; //Shift the final move selection significantly in response to utility differences this large.

  SearchParams();
  ~SearchParams();

  bool operator==(const SearchParams& other) const;
  bool operator!=(const SearchParams& other) const;

  nlohmann::json changeableParametersToJson() const;
  void printParams(std::ostream& out) const;

  //Params to use for testing, with some more recent values representative of more real use (as of Jan 2019)
  static SearchParams forTestsV1();
  //Params to use for testing, with some more recent values representative of more real use (as of Mar 2022)
  static SearchParams forTestsV2();

  static SearchParams basicDecentParams();

  static void failIfParamsDifferOnUnchangeableParameter(const SearchParams& initial, const SearchParams& dynamic);
};

#endif  // SEARCH_SEARCHPARAMS_H_

================
File: cpp/search/searchprint.cpp
================
#include "../search/searchprint.h"

================
File: cpp/search/searchprint.h
================
#ifndef SEARCH_SEARCHPRINT_H_
#define SEARCH_SEARCHPRINT_H_

#include "../game/board.h"

struct PrintTreeOptions {
  PrintTreeOptions();

  PrintTreeOptions maxDepth(int);
  PrintTreeOptions maxChildrenToShow(int);
  PrintTreeOptions minVisitsToShow(int64_t);
  PrintTreeOptions minVisitsToExpand(int64_t);
  PrintTreeOptions minVisitsPropToShow(double);
  PrintTreeOptions minVisitsPropToExpand(double);
  PrintTreeOptions printSqs(bool);
  PrintTreeOptions printAvgShorttermError(bool);
  PrintTreeOptions onlyBranch(const Board& board, const std::string& moves);
  PrintTreeOptions alsoBranch(const Board& board, const std::string& moves);

  int maxDepth_;
  int maxChildrenToShow_;
  int64_t minVisitsToShow_;
  int64_t minVisitsToExpand_;
  double minVisitsPropToShow_;
  double minVisitsPropToExpand_;
  int maxPVDepth_;
  bool printRawNN_;
  bool printSqs_;
  bool printAvgShorttermError_;
  std::vector<Loc> branch_;
  bool alsoBranch_;
};

inline PrintTreeOptions::PrintTreeOptions()
  :maxDepth_(1),
   maxChildrenToShow_(100000),
   minVisitsToShow_(0),
   minVisitsToExpand_(1),
   minVisitsPropToShow_(0.0),
   minVisitsPropToExpand_(0.0),
   maxPVDepth_(7),
   printRawNN_(false),
   printSqs_(false),
   printAvgShorttermError_(false),
   branch_(),
   alsoBranch_(false)
{}

inline PrintTreeOptions PrintTreeOptions::maxDepth(int d) { PrintTreeOptions other = *this; other.maxDepth_ = d; return other;}
inline PrintTreeOptions PrintTreeOptions::maxChildrenToShow(int c) { PrintTreeOptions other = *this; other.maxChildrenToShow_ = c; return other;}
inline PrintTreeOptions PrintTreeOptions::minVisitsToShow(int64_t v) { PrintTreeOptions other = *this; other.minVisitsToShow_ = v; return other;}
inline PrintTreeOptions PrintTreeOptions::minVisitsToExpand(int64_t v) { PrintTreeOptions other = *this; other.minVisitsToExpand_ = v; return other;}
inline PrintTreeOptions PrintTreeOptions::minVisitsPropToShow(double p) { PrintTreeOptions other = *this; other.minVisitsPropToShow_ = p; return other;}
inline PrintTreeOptions PrintTreeOptions::minVisitsPropToExpand(double p) { PrintTreeOptions other = *this; other.minVisitsPropToExpand_ = p; return other;}
inline PrintTreeOptions PrintTreeOptions::printSqs(bool b) { PrintTreeOptions other = *this; other.printSqs_ = b; return other;}
inline PrintTreeOptions PrintTreeOptions::printAvgShorttermError(bool b) { PrintTreeOptions other = *this; other.printAvgShorttermError_ = b; return other;}
inline PrintTreeOptions PrintTreeOptions::onlyBranch(const Board& board, const std::string& moves) {
  PrintTreeOptions other = *this; other.branch_ = Location::parseSequence(moves,board);
  return other;
}
inline PrintTreeOptions PrintTreeOptions::alsoBranch(const Board& board, const std::string& moves) {
  PrintTreeOptions other = *this; other.branch_ = Location::parseSequence(moves,board);
  other.alsoBranch_ = true;
  return other;
}

#endif  // SEARCH_SEARCHPRINT_H_

================
File: cpp/search/searchpuct.cpp
================
#include "../search/search.h"

#include "../search/searchnode.h"

//------------------------
#include "../core/using.h"
//------------------------

================
File: cpp/search/searchtimehelpers.cpp
================
#include "../search/search.h"

#include "../search/searchnode.h"

//------------------------
#include "../core/using.h"
//------------------------


double Search::numVisitsNeededToBeNonFutile(double maxVisitsMoveVisits) {
  double requiredVisits = searchParams.futileVisitsThreshold * maxVisitsMoveVisits;
  //In the case where we're playing high temperature, also require that we can't get to more than a 1:100 odds of playing the move.
  double chosenMoveTemperature = interpolateEarly(
    searchParams.chosenMoveTemperatureHalflife, searchParams.chosenMoveTemperatureEarly, searchParams.chosenMoveTemperature
  );
  if(chosenMoveTemperature < 1e-3)
    return requiredVisits;
  double requiredVisitsDueToTemp = maxVisitsMoveVisits * pow(0.01, chosenMoveTemperature);
  return std::min(requiredVisits, requiredVisitsDueToTemp);
}

double Search::computeUpperBoundVisitsLeftDueToTime(
  int64_t rootVisits, double timeUsed, double plannedTimeLimit
) {
  if(rootVisits <= 1)
    return 1e30;
  double timeThoughtSoFar = effectiveSearchTimeCarriedOver + timeUsed;
  double timeLeftPlanned = plannedTimeLimit - timeUsed;
  //Require at least a tenth of a second of search to begin to trust an estimate of visits/time.
  if(timeThoughtSoFar < 0.1)
    return 1e30;

  double proportionOfTimeThoughtLeft = timeLeftPlanned / timeThoughtSoFar;
  return ceil(proportionOfTimeThoughtLeft * rootVisits + searchParams.numThreads-1);
}

double Search::recomputeSearchTimeLimit(
  const TimeControls& tc, double timeUsed, double searchFactor, int64_t rootVisits
) {
  double tcMin;
  double tcRec;
  double tcMax;
  tc.getTime(rootBoard,rootHistory,searchParams.lagBuffer,tcMin,tcRec,tcMax);

  tcRec *= searchParams.overallocateTimeFactor;

  if(searchParams.midgameTimeFactor != 1.0) {
    double boardAreaScale = rootBoard.x_size * rootBoard.y_size / 361.0;
    double presumedTurnNumber = (double)rootHistory.getCurrentTurnNumber();
    if(presumedTurnNumber < 0) presumedTurnNumber = 0;

    double midGameWeight;
    if(presumedTurnNumber < searchParams.midgameTurnPeakTime * boardAreaScale)
      midGameWeight = presumedTurnNumber / (searchParams.midgameTurnPeakTime * boardAreaScale);
    else
      midGameWeight = exp(
        -(presumedTurnNumber - searchParams.midgameTurnPeakTime * boardAreaScale) /
        (searchParams.endgameTurnTimeDecay * boardAreaScale)
      );
    if(midGameWeight < 0)
      midGameWeight = 0;
    if(midGameWeight > 1)
      midGameWeight = 1;

    tcRec *= 1.0 + midGameWeight * (searchParams.midgameTimeFactor - 1.0);
  }

  if(searchParams.obviousMovesTimeFactor < 1.0) {
    double surprise = 0.0;
    double searchEntropy = 0.0;
    double policyEntropy = 0.0;
    bool suc = getPolicySurpriseAndEntropy(surprise, searchEntropy, policyEntropy);
    if(suc) {
      //If the original policy was confident and the surprise is low, then this is probably an "obvious" move.
      double obviousnessByEntropy = exp(-policyEntropy/searchParams.obviousMovesPolicyEntropyTolerance);
      double obviousnessBySurprise = exp(-surprise/searchParams.obviousMovesPolicySurpriseTolerance);
      double obviousnessWeight = std::min(obviousnessByEntropy, obviousnessBySurprise);
      tcRec *= 1.0 + obviousnessWeight * (searchParams.obviousMovesTimeFactor - 1.0);
    }
  }

  if(tcRec > 1e-20) {
    double remainingTimeNeeded = tcRec - effectiveSearchTimeCarriedOver;
    double remainingTimeNeededFactor = remainingTimeNeeded/tcRec;
    //TODO this is a bit conservative relative to old behavior, it might be of slightly detrimental value, needs testing.
    //Apply softplus so that we still do a tiny bit of search even in the presence of variable search time instead of instamoving,
    //there are some benefits from root-level search due to broader root exploration and the cost is small, also we may be over
    //counting the ponder benefit if search is faster on this node than on the previous turn.
    tcRec = tcRec * std::min(1.0, log(1.0+exp(remainingTimeNeededFactor * 6.0)) / 6.0);
  }

  //Make sure we're not wasting time
  tcRec = tc.roundUpTimeLimitIfNeeded(searchParams.lagBuffer,timeUsed,tcRec);
  if(tcRec > tcMax) tcRec = tcMax;

  //After rounding up time, check if with our planned rounded time, anything is futile to search
  if(searchParams.futileVisitsThreshold > 0) {
    double upperBoundVisitsLeftDueToTime = computeUpperBoundVisitsLeftDueToTime(rootVisits, timeUsed, tcRec);
    if(upperBoundVisitsLeftDueToTime < searchParams.futileVisitsThreshold * rootVisits) {
      vector<Loc> locs;
      vector<double> playSelectionValues;
      vector<double> visitCounts;
      bool suc = getPlaySelectionValues(locs, playSelectionValues, &visitCounts, 1.0);
      if(suc && playSelectionValues.size() > 0) {
        //This may fail to hold if we have no actual visits and play selections are being pulled from stuff like raw policy
        if(playSelectionValues.size() == visitCounts.size()) {
          int numMoves = (int)playSelectionValues.size();
          int maxVisitsIdx = 0;
          int bestMoveIdx = 0;
          for(int i = 1; i<numMoves; i++) {
            if(playSelectionValues[i] > playSelectionValues[bestMoveIdx])
              bestMoveIdx = i;
            if(visitCounts[i] > visitCounts[maxVisitsIdx])
              maxVisitsIdx = i;
          }
          if(maxVisitsIdx == bestMoveIdx) {
            double requiredVisits = numVisitsNeededToBeNonFutile(visitCounts[maxVisitsIdx]);
            bool foundPossibleAlternativeMove = false;
            for(int i = 0; i<numMoves; i++) {
              if(i == bestMoveIdx)
                continue;
              if(visitCounts[i] + upperBoundVisitsLeftDueToTime >= requiredVisits) {
                foundPossibleAlternativeMove = true;
                break;
              }
            }
            if(!foundPossibleAlternativeMove) {
              //We should stop search now - set our desired thinking to very slightly smaller than what we used.
              tcRec = timeUsed * (1.0 - (1e-10));
            }
          }
        }
      }
    }
  }

  //Make sure we're not wasting time, even after considering that we might want to stop early
  tcRec = tc.roundUpTimeLimitIfNeeded(searchParams.lagBuffer,timeUsed,tcRec);
  if(tcRec > tcMax) tcRec = tcMax;

  //Apply caps and search factor
  //Since searchFactor is mainly used for friendliness (like, play faster after many passes)
  //we allow it to violate the min time.
  if(tcRec < tcMin) tcRec = tcMin;
  tcRec *= searchFactor;
  if(tcRec > tcMax) tcRec = tcMax;

  return tcRec;
}

================
File: cpp/search/searchupdatehelpers.cpp
================
#include "../search/search.h"

#include "../search/searchnode.h"
#include "../search/distributiontable.h"

//------------------------
#include "../core/using.h"
//------------------------



void Search::addLeafValue(
  SearchNode& node,
  double winLossValue,
  double noResultValue,
  double scoreMean,
  double scoreMeanSq,
  double lead,
  double weight,
  bool isTerminal,
  bool assumeNoExistingWeight
) {
  double utility =
    getResultUtility(winLossValue, noResultValue)
    + getScoreUtility(scoreMean, scoreMeanSq);

  if(searchParams.subtreeValueBiasFactor != 0 && !isTerminal && node.subtreeValueBiasTableEntry != nullptr) {
    SubtreeValueBiasEntry& entry = *(node.subtreeValueBiasTableEntry);
    while(entry.entryLock.test_and_set(std::memory_order_acquire));
    double newEntryDeltaUtilitySum = entry.deltaUtilitySum;
    double newEntryWeightSum = entry.weightSum;
    entry.entryLock.clear(std::memory_order_release);
    //This is the amount of the direct evaluation of this node that we are going to bias towards the table entry
    const double biasFactor = searchParams.subtreeValueBiasFactor;
    if(newEntryWeightSum > 0.001)
      utility += biasFactor * newEntryDeltaUtilitySum / newEntryWeightSum;
  }

  utility += getPatternBonus(node.patternBonusHash,getOpp(node.nextPla));

  double utilitySq = utility * utility;
  double weightSq = weight * weight;

  if(assumeNoExistingWeight) {
    while(node.statsLock.test_and_set(std::memory_order_acquire));
    node.stats.winLossValueAvg.store(winLossValue,std::memory_order_release);
    node.stats.noResultValueAvg.store(noResultValue,std::memory_order_release);
    node.stats.scoreMeanAvg.store(scoreMean,std::memory_order_release);
    node.stats.scoreMeanSqAvg.store(scoreMeanSq,std::memory_order_release);
    node.stats.leadAvg.store(lead,std::memory_order_release);
    node.stats.utilityAvg.store(utility,std::memory_order_release);
    node.stats.utilitySqAvg.store(utilitySq,std::memory_order_release);
    node.stats.weightSqSum.store(weightSq,std::memory_order_release);
    node.stats.weightSum.store(weight,std::memory_order_release);
    int64_t oldVisits = node.stats.visits.fetch_add(1,std::memory_order_release);
    node.statsLock.clear(std::memory_order_release);
    // This should only be possible in the extremely rare case that we transpose to a terminal node from a non-terminal node probably due to
    // a hash collision, or that we have a graph history interaction that somehow changes whether a particular path ends the game or not, despite
    // our simpleRepetitionBoundGt logic... such that the node managed to get visits as a terminal node despite not having an nn eval. There's
    // nothing reasonable to do here once we have such a bad collision, so just at least don't crash.
    if(oldVisits != 0) {
      logger->write("WARNING: assumeNoExistingWeight for leaf but leaf already has visits");
    }
  }
  else {
    while(node.statsLock.test_and_set(std::memory_order_acquire));
    double oldWeightSum = node.stats.weightSum.load(std::memory_order_relaxed);
    double newWeightSum = oldWeightSum + weight;

    node.stats.winLossValueAvg.store((node.stats.winLossValueAvg.load(std::memory_order_relaxed) * oldWeightSum + winLossValue * weight)/newWeightSum,std::memory_order_release);
    node.stats.noResultValueAvg.store((node.stats.noResultValueAvg.load(std::memory_order_relaxed) * oldWeightSum + noResultValue * weight)/newWeightSum,std::memory_order_release);
    node.stats.scoreMeanAvg.store((node.stats.scoreMeanAvg.load(std::memory_order_relaxed) * oldWeightSum + scoreMean * weight)/newWeightSum,std::memory_order_release);
    node.stats.scoreMeanSqAvg.store((node.stats.scoreMeanSqAvg.load(std::memory_order_relaxed) * oldWeightSum + scoreMeanSq * weight)/newWeightSum,std::memory_order_release);
    node.stats.leadAvg.store((node.stats.leadAvg.load(std::memory_order_relaxed) * oldWeightSum + lead * weight)/newWeightSum,std::memory_order_release);
    node.stats.utilityAvg.store((node.stats.utilityAvg.load(std::memory_order_relaxed) * oldWeightSum + utility * weight)/newWeightSum,std::memory_order_release);
    node.stats.utilitySqAvg.store((node.stats.utilitySqAvg.load(std::memory_order_relaxed) * oldWeightSum + utilitySq * weight)/newWeightSum,std::memory_order_release);
    node.stats.weightSqSum.store(node.stats.weightSqSum.load(std::memory_order_relaxed) + weightSq,std::memory_order_release);
    node.stats.weightSum.store(newWeightSum,std::memory_order_release);
    node.stats.visits.fetch_add(1,std::memory_order_release);
    node.statsLock.clear(std::memory_order_release);
  }
}

void Search::addCurrentNNOutputAsLeafValue(SearchNode& node, bool assumeNoExistingWeight) {
  const NNOutput* nnOutput = node.getNNOutput();
  assert(nnOutput != NULL);
  //Values in the search are from the perspective of white positive always
  double winProb = (double)nnOutput->whiteWinProb;
  double lossProb = (double)nnOutput->whiteLossProb;
  double noResultProb = (double)nnOutput->whiteNoResultProb;
  double scoreMean = (double)nnOutput->whiteScoreMean;
  double scoreMeanSq = (double)nnOutput->whiteScoreMeanSq;
  double lead = (double)nnOutput->whiteLead;
  double weight = computeWeightFromNNOutput(nnOutput);
  addLeafValue(node,winProb-lossProb,noResultProb,scoreMean,scoreMeanSq,lead,weight,false,assumeNoExistingWeight);
}

double Search::computeWeightFromNNOutput(const NNOutput* nnOutput) const {
  if(!searchParams.useUncertainty)
    return 1.0;
  if(!nnEvaluator->supportsShorttermError())
    return 1.0;

  double scoreMean = (double)nnOutput->whiteScoreMean;
  double utilityUncertaintyWL = searchParams.winLossUtilityFactor * nnOutput->shorttermWinlossError;
  double utilityUncertaintyScore = getApproxScoreUtilityDerivative(scoreMean) * nnOutput->shorttermScoreError;
  double utilityUncertainty = utilityUncertaintyWL + utilityUncertaintyScore;

  double poweredUncertainty;
  if(searchParams.uncertaintyExponent == 1.0)
    poweredUncertainty = utilityUncertainty;
  else if(searchParams.uncertaintyExponent == 0.5)
    poweredUncertainty = sqrt(utilityUncertainty);
  else
    poweredUncertainty = pow(utilityUncertainty, searchParams.uncertaintyExponent);

  double baselineUncertainty = searchParams.uncertaintyCoeff / searchParams.uncertaintyMaxWeight;
  double weight = searchParams.uncertaintyCoeff / (poweredUncertainty + baselineUncertainty);
  return weight;
}


void Search::updateStatsAfterPlayout(SearchNode& node, SearchThread& thread, bool isRoot) {
  //The thread that grabs a 0 from this peforms the recomputation of stats.
  int32_t oldDirtyCounter = node.dirtyCounter.fetch_add(1,std::memory_order_acq_rel);
  assert(oldDirtyCounter >= 0);
  //If we atomically grab a nonzero, then we know another thread must already be doing the work, so we can skip the update ourselves.
  if(oldDirtyCounter > 0)
    return;
  int32_t numVisitsCompleted = 1;
  while(true) {
    //Perform update
    recomputeNodeStats(node,thread,numVisitsCompleted,isRoot);
    //Now attempt to undo the counter
    oldDirtyCounter = node.dirtyCounter.fetch_add(-numVisitsCompleted,std::memory_order_acq_rel);
    int32_t newDirtyCounter = oldDirtyCounter - numVisitsCompleted;
    //If no other threads incremented it in the meantime, so our decrement hits zero, we're done.
    if(newDirtyCounter <= 0) {
      assert(newDirtyCounter == 0);
      break;
    }
    //Otherwise, more threads incremented this more in the meantime. So we need to loop again and add their visits, recomputing again.
    numVisitsCompleted = newDirtyCounter;
    continue;
  }
}

//Recompute all the stats of this node based on its children, except its visits and virtual losses, which are not child-dependent and
//are updated in the manner specified.
//Assumes this node has an nnOutput
void Search::recomputeNodeStats(SearchNode& node, SearchThread& thread, int numVisitsToAdd, bool isRoot) {
  //Find all children and compute weighting of the children based on their values
  vector<MoreNodeStats>& statsBuf = thread.statsBuf;
  int numGoodChildren = 0;

  ConstSearchNodeChildrenReference children = node.getChildren();
  int childrenCapacity = children.getCapacity();
  double origTotalChildWeight = 0.0;
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    MoreNodeStats& stats = statsBuf[numGoodChildren];

    Loc moveLoc = childPointer.getMoveLocRelaxed();
    int64_t edgeVisits = childPointer.getEdgeVisits();
    stats.stats = NodeStats(child->stats);

    if(stats.stats.visits <= 0 || stats.stats.weightSum <= 0.0 || edgeVisits <= 0)
      continue;

    double childUtility = stats.stats.utilityAvg;
    stats.selfUtility = node.nextPla == P_WHITE ? childUtility : -childUtility;
    stats.weightAdjusted = stats.stats.getChildWeight(edgeVisits);
    stats.prevMoveLoc = moveLoc;

    origTotalChildWeight += stats.weightAdjusted;
    numGoodChildren++;
  }

  //Always tracks the sum of statsBuf[i].weightAdjusted across the children.
  double currentTotalChildWeight = origTotalChildWeight;

  if(searchParams.useNoisePruning && numGoodChildren > 0 && !(searchParams.antiMirror && mirroringPla != C_EMPTY)) {
    double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE];
    {
      const NNOutput* nnOutput = node.getNNOutput();
      assert(nnOutput != NULL);
      const float* policyProbs = nnOutput->getPolicyProbsMaybeNoised();
      for(int i = 0; i<numGoodChildren; i++)
        policyProbsBuf[i] = std::max(1e-30, (double)policyProbs[getPos(statsBuf[i].prevMoveLoc)]);
    }
    currentTotalChildWeight = pruneNoiseWeight(statsBuf, numGoodChildren, currentTotalChildWeight, policyProbsBuf);
  }

  {
    double amountToSubtract = 0.0;
    double amountToPrune = 0.0;
    if(isRoot && searchParams.rootNoiseEnabled && !searchParams.useNoisePruning) {
      double maxChildWeight = 0.0;
      for(int i = 0; i<numGoodChildren; i++) {
        if(statsBuf[i].weightAdjusted > maxChildWeight)
          maxChildWeight = statsBuf[i].weightAdjusted;
      }
      amountToSubtract = std::min(searchParams.chosenMoveSubtract, maxChildWeight/64.0);
      amountToPrune = std::min(searchParams.chosenMovePrune, maxChildWeight/64.0);
    }

    downweightBadChildrenAndNormalizeWeight(
      numGoodChildren, currentTotalChildWeight, currentTotalChildWeight,
      amountToSubtract, amountToPrune, statsBuf
    );
  }

  double winLossValueSum = 0.0;
  double noResultValueSum = 0.0;
  double scoreMeanSum = 0.0;
  double scoreMeanSqSum = 0.0;
  double leadSum = 0.0;
  double utilitySum = 0.0;
  double utilitySqSum = 0.0;
  double weightSqSum = 0.0;
  double weightSum = currentTotalChildWeight;
  for(int i = 0; i<numGoodChildren; i++) {
    const NodeStats& stats = statsBuf[i].stats;

    double desiredWeight = statsBuf[i].weightAdjusted;
    double weightScaling = desiredWeight / stats.weightSum;

    winLossValueSum += desiredWeight * stats.winLossValueAvg;
    noResultValueSum += desiredWeight * stats.noResultValueAvg;
    scoreMeanSum += desiredWeight * stats.scoreMeanAvg;
    scoreMeanSqSum += desiredWeight * stats.scoreMeanSqAvg;
    leadSum += desiredWeight * stats.leadAvg;
    utilitySum += desiredWeight * stats.utilityAvg;
    utilitySqSum += desiredWeight * stats.utilitySqAvg;
    weightSqSum += weightScaling * weightScaling * stats.weightSqSum;
  }

  //Also add in the direct evaluation of this node.
  {
    const NNOutput* nnOutput = node.getNNOutput();
    assert(nnOutput != NULL);
    double winProb = (double)nnOutput->whiteWinProb;
    double lossProb = (double)nnOutput->whiteLossProb;
    double noResultProb = (double)nnOutput->whiteNoResultProb;
    double scoreMean = (double)nnOutput->whiteScoreMean;
    double scoreMeanSq = (double)nnOutput->whiteScoreMeanSq;
    double lead = (double)nnOutput->whiteLead;
    double utility =
      getResultUtility(winProb-lossProb, noResultProb)
      + getScoreUtility(scoreMean, scoreMeanSq);

    if(searchParams.subtreeValueBiasFactor != 0 && node.subtreeValueBiasTableEntry != nullptr) {
      SubtreeValueBiasEntry& entry = *(node.subtreeValueBiasTableEntry);

      double newEntryDeltaUtilitySum;
      double newEntryWeightSum;

      if(currentTotalChildWeight > 1e-10) {
        double utilityChildren = utilitySum / currentTotalChildWeight;
        double subtreeValueBiasWeight = pow(origTotalChildWeight, searchParams.subtreeValueBiasWeightExponent);
        double subtreeValueBiasDeltaSum = (utilityChildren - utility) * subtreeValueBiasWeight;

        while(entry.entryLock.test_and_set(std::memory_order_acquire));
        entry.deltaUtilitySum += subtreeValueBiasDeltaSum - node.lastSubtreeValueBiasDeltaSum;
        entry.weightSum += subtreeValueBiasWeight - node.lastSubtreeValueBiasWeight;
        newEntryDeltaUtilitySum = entry.deltaUtilitySum;
        newEntryWeightSum = entry.weightSum;
        node.lastSubtreeValueBiasDeltaSum = subtreeValueBiasDeltaSum;
        node.lastSubtreeValueBiasWeight = subtreeValueBiasWeight;
        entry.entryLock.clear(std::memory_order_release);
      }
      else {
        while(entry.entryLock.test_and_set(std::memory_order_acquire));
        newEntryDeltaUtilitySum = entry.deltaUtilitySum;
        newEntryWeightSum = entry.weightSum;
        entry.entryLock.clear(std::memory_order_release);
      }

      //This is the amount of the direct evaluation of this node that we are going to bias towards the table entry
      const double biasFactor = searchParams.subtreeValueBiasFactor;
      if(newEntryWeightSum > 0.001)
        utility += biasFactor * newEntryDeltaUtilitySum / newEntryWeightSum;
      //This is the amount by which we need to scale desiredSelfWeight such that if the table entry were actually equal to
      //the current difference between the direct eval and the children, we would perform a no-op... unless a noop is actually impossible
      //Then we just take what we can get.
      //desiredSelfWeight *= weightSum / (1.0-biasFactor) / std::max(0.001, (weightSum + desiredSelfWeight - desiredSelfWeight / (1.0-biasFactor)));
    }

    double weight = computeWeightFromNNOutput(nnOutput);
    winLossValueSum += (winProb - lossProb) * weight;
    noResultValueSum += noResultProb * weight;
    scoreMeanSum += scoreMean * weight;
    scoreMeanSqSum += scoreMeanSq * weight;
    leadSum += lead * weight;
    utilitySum += utility * weight;
    utilitySqSum += utility * utility * weight;
    weightSqSum += weight * weight;
    weightSum += weight;
  }

  double winLossValueAvg = winLossValueSum / weightSum;
  double noResultValueAvg = noResultValueSum / weightSum;
  double scoreMeanAvg = scoreMeanSum / weightSum;
  double scoreMeanSqAvg = scoreMeanSqSum / weightSum;
  double leadAvg = leadSum / weightSum;
  double utilityAvg = utilitySum / weightSum;
  double utilitySqAvg = utilitySqSum / weightSum;

  double oldUtilityAvg = utilityAvg;
  utilityAvg += getPatternBonus(node.patternBonusHash,getOpp(node.nextPla));
  utilitySqAvg = utilitySqAvg + (utilityAvg * utilityAvg - oldUtilityAvg * oldUtilityAvg);

  //TODO statslock may be unnecessary now with the dirtyCounter mechanism?
  while(node.statsLock.test_and_set(std::memory_order_acquire));
  node.stats.winLossValueAvg.store(winLossValueAvg,std::memory_order_release);
  node.stats.noResultValueAvg.store(noResultValueAvg,std::memory_order_release);
  node.stats.scoreMeanAvg.store(scoreMeanAvg,std::memory_order_release);
  node.stats.scoreMeanSqAvg.store(scoreMeanSqAvg,std::memory_order_release);
  node.stats.leadAvg.store(leadAvg,std::memory_order_release);
  node.stats.utilityAvg.store(utilityAvg,std::memory_order_release);
  node.stats.utilitySqAvg.store(utilitySqAvg,std::memory_order_release);
  node.stats.weightSqSum.store(weightSqSum,std::memory_order_release);
  node.stats.weightSum.store(weightSum,std::memory_order_release);
  node.stats.visits.fetch_add(numVisitsToAdd,std::memory_order_release);
  node.statsLock.clear(std::memory_order_release);
}

void Search::downweightBadChildrenAndNormalizeWeight(
  int numChildren,
  double currentTotalWeight, //The current sum of statsBuf[i].weightAdjusted
  double desiredTotalWeight, //What statsBuf[i].weightAdjusted should sum up to after this function is done.
  double amountToSubtract,
  double amountToPrune,
  vector<MoreNodeStats>& statsBuf
) const {
  if(numChildren <= 0 || currentTotalWeight <= 0.0)
    return;

  if(searchParams.valueWeightExponent == 0 || mirroringPla != C_EMPTY) {
    for(int i = 0; i<numChildren; i++) {
      if(statsBuf[i].weightAdjusted < amountToPrune) {
        currentTotalWeight -= statsBuf[i].weightAdjusted;
        statsBuf[i].weightAdjusted = 0.0;
        continue;
      }
      double newWeight = statsBuf[i].weightAdjusted - amountToSubtract;
      if(newWeight <= 0) {
        currentTotalWeight -= statsBuf[i].weightAdjusted;
        statsBuf[i].weightAdjusted = 0.0;
      }
      else {
        currentTotalWeight -= amountToSubtract;
        statsBuf[i].weightAdjusted = newWeight;
      }
    }

    if(currentTotalWeight != desiredTotalWeight) {
      double factor = desiredTotalWeight / currentTotalWeight;
      for(int i = 0; i<numChildren; i++)
        statsBuf[i].weightAdjusted *= factor;
    }
    return;
  }

  assert(numChildren <= NNPos::MAX_NN_POLICY_SIZE);
  double stdevs[NNPos::MAX_NN_POLICY_SIZE];
  double simpleValueSum = 0.0;
  for(int i = 0; i<numChildren; i++) {
    int64_t numVisits = statsBuf[i].stats.visits;
    assert(numVisits >= 0);
    if(numVisits == 0)
      continue;

    double weight = statsBuf[i].weightAdjusted;
    double precision = 1.5 * sqrt(weight);

    //Ensure some minimum variance for stability regardless of how we change the above formula
    static const double minVariance = 0.00000001;
    stdevs[i] = sqrt(minVariance + 1.0 / precision);
    simpleValueSum += statsBuf[i].selfUtility * weight;
  }

  double simpleValue = simpleValueSum / currentTotalWeight;

  double totalNewUnnormWeight = 0.0;
  for(int i = 0; i<numChildren; i++) {
    if(statsBuf[i].stats.visits == 0)
      continue;

    if(statsBuf[i].weightAdjusted < amountToPrune) {
      currentTotalWeight -= statsBuf[i].weightAdjusted;
      statsBuf[i].weightAdjusted = 0.0;
      continue;
    }
    double newWeight = statsBuf[i].weightAdjusted - amountToSubtract;
    if(newWeight <= 0) {
      currentTotalWeight -= statsBuf[i].weightAdjusted;
      statsBuf[i].weightAdjusted = 0.0;
    }
    else {
      currentTotalWeight -= amountToSubtract;
      statsBuf[i].weightAdjusted = newWeight;
    }

    double z = (statsBuf[i].selfUtility - simpleValue) / stdevs[i];
    //Also just for numeric sanity, make sure everything has some tiny minimum value.
    double p = valueWeightDistribution->getCdf(z) + 0.0001;
    statsBuf[i].weightAdjusted *= pow(p, searchParams.valueWeightExponent);
    totalNewUnnormWeight += statsBuf[i].weightAdjusted;
  }

  //Post-process and normalize to sum to the desired weight
  assert(totalNewUnnormWeight > 0.0);
  double factor = desiredTotalWeight / totalNewUnnormWeight;
  for(int i = 0; i<numChildren; i++)
    statsBuf[i].weightAdjusted *= factor;
}


//Returns the new sum of weightAdjusted
double Search::pruneNoiseWeight(vector<MoreNodeStats>& statsBuf, int numChildren, double totalChildWeight, const double* policyProbsBuf) const {
  if(numChildren <= 1 || totalChildWeight <= 0.00001)
    return totalChildWeight;

  // Children are normally sorted in policy order in KataGo.
  // But this is not guaranteed, because at the root, we might recompute the nnoutput, or when finding the best new child, we have hacks like antiMirror policy
  // and other adjustments. For simplicity, we just consider children in sorted order anyways for this pruning, since it will be close.

  // For any child, if its own utility is lower than the weighted average utility of the children before it, it's downweighted if it exceeds much more than a
  // raw-policy share of the weight.
  double utilitySumSoFar = 0;
  double weightSumSoFar = 0;
  //double rawPolicyUtilitySumSoFar = 0;
  double rawPolicySumSoFar = 0;
  for(int i = 0; i<numChildren; i++) {
    double utility = statsBuf[i].selfUtility;
    double oldWeight = statsBuf[i].weightAdjusted;
    double rawPolicy = policyProbsBuf[i];

    double newWeight = oldWeight;
    if(weightSumSoFar > 0 && rawPolicySumSoFar > 0) {
      double avgUtilitySoFar = utilitySumSoFar / weightSumSoFar;
      double utilityGap = avgUtilitySoFar - utility;
      if(utilityGap > 0) {
        double weightShareFromRawPolicy = weightSumSoFar * rawPolicy / rawPolicySumSoFar;
        //If the child is more than double its proper share of the weight
        double lenientWeightShareFromRawPolicy = 2.0 * weightShareFromRawPolicy;
        if(oldWeight > lenientWeightShareFromRawPolicy) {
          double excessWeight = oldWeight - lenientWeightShareFromRawPolicy;
          double weightToSubtract = excessWeight * (1.0 - exp(-utilityGap / searchParams.noisePruneUtilityScale));
          if(weightToSubtract > searchParams.noisePruningCap)
            weightToSubtract = searchParams.noisePruningCap;

          newWeight = oldWeight - weightToSubtract;
          statsBuf[i].weightAdjusted = newWeight;
        }
      }
    }
    utilitySumSoFar += utility * newWeight;
    weightSumSoFar += newWeight;
    //rawPolicyUtilitySumSoFar += utility * rawPolicy;
    rawPolicySumSoFar += rawPolicy;
  }
  return weightSumSoFar;
}

================
File: cpp/search/subtreevaluebiastable.cpp
================
#include "../search/subtreevaluebiastable.h"

#include "../core/rand.h"
#include "../search/localpattern.h"

static std::mutex initMutex;
static std::atomic<bool> isInited(false);
static LocalPatternHasher patternHasher;
static Hash128 ZOBRIST_MOVE_LOCS[Board::MAX_ARR_SIZE][2];
static Hash128 ZOBRIST_KO_BAN[Board::MAX_ARR_SIZE];

static void initIfNeeded() {
  if(isInited)
    return;
  std::lock_guard<std::mutex> lock(initMutex);
  if(isInited)
    return;
  Rand rand("ValueBiasTable ZOBRIST STUFF");
  patternHasher.init(5,5,rand);

  for(int i = 0; i<Board::MAX_ARR_SIZE; i++) {
    for(int j = 0; j<2; j++) {
      uint64_t h0 = rand.nextUInt64();
      uint64_t h1 = rand.nextUInt64();
      ZOBRIST_MOVE_LOCS[i][j] = Hash128(h0,h1);
    }
  }

  rand.init("Reseed ValueBiasTable zobrist so that zobrists don't change when Board::MAX_ARR_SIZE changes");
  for(int i = 0; i<Board::MAX_ARR_SIZE; i++) {
    uint64_t h0 = rand.nextUInt64();
    uint64_t h1 = rand.nextUInt64();
    ZOBRIST_KO_BAN[i] = Hash128(h0,h1);
  }
  isInited = true;
}

SubtreeValueBiasTable::SubtreeValueBiasTable(int32_t numShards) {
  initIfNeeded();
  mutexPool = new MutexPool(numShards);
  entries.resize(numShards);
}
SubtreeValueBiasTable::~SubtreeValueBiasTable() {
  delete mutexPool;
}

void SubtreeValueBiasTable::clearUnusedSynchronous() {
  for(size_t i = 0; i<entries.size(); i++) {
    std::map<Hash128,std::shared_ptr<SubtreeValueBiasEntry>>& submap = entries[i];
    for(auto iter = submap.begin(); iter != submap.end(); /* no incr */) {
      // Anything in this map NOT used by anyone else - clear
      if(iter->second.use_count() <= 1) {
        iter = submap.erase(iter);
      }
      else {
        ++iter;
      }
    }
  }
}

std::shared_ptr<SubtreeValueBiasEntry> SubtreeValueBiasTable::get(Player pla, Loc parentPrevMoveLoc, Loc prevMoveLoc, const Board& prevBoard) {
  Hash128 hash = ZOBRIST_MOVE_LOCS[parentPrevMoveLoc][0] ^ ZOBRIST_MOVE_LOCS[prevMoveLoc][1];

  hash ^= patternHasher.getHash(prevBoard,prevMoveLoc,pla);
  if(prevBoard.ko_loc != Board::NULL_LOC) {
    hash ^= ZOBRIST_KO_BAN[prevBoard.ko_loc];
  }

  uint32_t subMapIdx = (uint32_t)(hash.hash0 % entries.size());

  std::mutex& mutex = mutexPool->getMutex(subMapIdx);
  std::lock_guard<std::mutex> lock(mutex);
  std::shared_ptr<SubtreeValueBiasEntry>& slot = entries[subMapIdx][hash];
  if(slot == nullptr)
    slot = std::make_shared<SubtreeValueBiasEntry>();
  return slot;
}

================
File: cpp/search/subtreevaluebiastable.h
================
#ifndef SEARCH_SUBTREEVALUEBIASTABLE_H
#define SEARCH_SUBTREEVALUEBIASTABLE_H

#include "../core/global.h"
#include "../core/hash.h"
#include "../core/multithread.h"
#include "../game/board.h"
#include "../search/mutexpool.h"

struct SubtreeValueBiasEntry {
  double deltaUtilitySum = 0.0;
  double weightSum = 0.0;
  mutable std::atomic_flag entryLock = ATOMIC_FLAG_INIT;
};

struct SubtreeValueBiasTable {
  std::vector<std::map<Hash128,std::shared_ptr<SubtreeValueBiasEntry>>> entries;
  MutexPool* mutexPool;

  SubtreeValueBiasTable(int32_t numShards);
  ~SubtreeValueBiasTable();

  // ASSUMES there is no concurrent multithreading of this table or any of its entries,
  // and that all past mutations on this table or any of its entries are now visible to this thread.
  void clearUnusedSynchronous();

  // The board specified here is expected to be the board BEFORE the move is played.
  std::shared_ptr<SubtreeValueBiasEntry> get(Player pla, Loc parentPrevMoveLoc, Loc prevMoveLoc, const Board& prevBoard);
};

#endif

================
File: cpp/search/timecontrols.cpp
================
#include "../search/timecontrols.h"

#include <sstream>
#include <cmath>

TimeControls::TimeControls()
  :originalMainTime(TimeControls::UNLIMITED_TIME_DEFAULT),
   increment(0.0),
   mainTimeLimit(TimeControls::UNLIMITED_TIME_DEFAULT_LARGE),
   maxTimePerMove(TimeControls::UNLIMITED_TIME_DEFAULT_LARGE),
   originalNumPeriods(0),
   numStonesPerPeriod(0),
   perPeriodTime(0.0),

   mainTimeLeft(TimeControls::UNLIMITED_TIME_DEFAULT),
   inOvertime(false),
   numPeriodsLeftIncludingCurrent(0),
   numStonesLeftInPeriod(0),
   timeLeftInPeriod(0.0)
{}

TimeControls::~TimeControls()
{}

bool TimeControls::isEffectivelyUnlimitedTime() const {
  return
    (mainTimeLeft > TimeControls::UNLIMITED_TIME_THRESHOLD || (inOvertime && timeLeftInPeriod > TimeControls::UNLIMITED_TIME_THRESHOLD))
    && maxTimePerMove > TimeControls::UNLIMITED_TIME_THRESHOLD;
}

TimeControls TimeControls::absoluteTime(double mainTime) {
  TimeControls tc;
  tc.originalMainTime = mainTime;
  tc.increment = 0.0;
  tc.mainTimeLimit = TimeControls::UNLIMITED_TIME_DEFAULT_LARGE;
  tc.maxTimePerMove = TimeControls::UNLIMITED_TIME_DEFAULT_LARGE;
  tc.originalNumPeriods = 0;
  tc.numStonesPerPeriod = 0;
  tc.perPeriodTime = 0.0;
  tc.mainTimeLeft = mainTime;
  tc.inOvertime = false;
  tc.numPeriodsLeftIncludingCurrent = 0;
  tc.numStonesLeftInPeriod = 0;
  tc.timeLeftInPeriod = 0;
  return tc;
}

TimeControls TimeControls::fischerTime(double mainTime, double increment) {
  TimeControls tc;
  tc.originalMainTime = mainTime;
  tc.increment = increment;
  tc.mainTimeLimit = TimeControls::UNLIMITED_TIME_DEFAULT_LARGE;
  tc.maxTimePerMove = TimeControls::UNLIMITED_TIME_DEFAULT_LARGE;
  tc.originalNumPeriods = 0;
  tc.numStonesPerPeriod = 0;
  tc.perPeriodTime = 0.0;
  tc.mainTimeLeft = mainTime;
  tc.inOvertime = false;
  tc.numPeriodsLeftIncludingCurrent = 0;
  tc.numStonesLeftInPeriod = 0;
  tc.timeLeftInPeriod = 0;
  return tc;
}

TimeControls TimeControls::fischerCappedTime(double mainTime, double increment, double mainTimeLimit, double maxTimePerMove) {
  if(mainTimeLimit < mainTime)
    throw StringError("TimeControls: mainTimeLimit is smaller than mainTime");
  TimeControls tc;
  tc.originalMainTime = mainTime;
  tc.increment = increment;
  tc.mainTimeLimit = mainTimeLimit;
  tc.maxTimePerMove = maxTimePerMove;
  tc.originalNumPeriods = 0;
  tc.numStonesPerPeriod = 0;
  tc.perPeriodTime = 0.0;
  tc.mainTimeLeft = mainTime;
  tc.inOvertime = false;
  tc.numPeriodsLeftIncludingCurrent = 0;
  tc.numStonesLeftInPeriod = 0;
  tc.timeLeftInPeriod = 0;
  return tc;
}

TimeControls TimeControls::canadianOrByoYomiTime(
  double mainTime,
  double perPeriodTime,
  int numPeriods,
  int numStonesPerPeriod
) {
  TimeControls tc;
  tc.originalMainTime = mainTime;
  tc.increment = 0.0;
  tc.mainTimeLimit = TimeControls::UNLIMITED_TIME_DEFAULT_LARGE;
  tc.maxTimePerMove = TimeControls::UNLIMITED_TIME_DEFAULT_LARGE;
  tc.originalNumPeriods = numPeriods;
  tc.numStonesPerPeriod = numStonesPerPeriod;
  tc.perPeriodTime = perPeriodTime;
  tc.mainTimeLeft = mainTime;
  tc.inOvertime = false;
  tc.numPeriodsLeftIncludingCurrent = numPeriods;
  tc.numStonesLeftInPeriod = 0;
  tc.timeLeftInPeriod = 0;
  return tc;
}

std::string TimeControls::toDebugString(const Board& board, const BoardHistory& hist, double lagBuffer) const {
  std::ostringstream out;
  out << "originalMainTime " << originalMainTime;
  if(increment != 0)
    out << " increment " << increment;
  if(mainTimeLimit < TimeControls::UNLIMITED_TIME_THRESHOLD)
    out << " mainTimeLimit " << mainTimeLimit;
  if(maxTimePerMove < TimeControls::UNLIMITED_TIME_THRESHOLD)
    out << " maxTimePerMove " << maxTimePerMove;

  if(originalNumPeriods != 0)
    out << " originalNumPeriods " << originalNumPeriods;
  if(numStonesPerPeriod != 0)
    out << " numStonesPerPeriod " << numStonesPerPeriod;
  if(perPeriodTime != 0)
    out << " perPeriodTime " << perPeriodTime;
  out << " mainTimeLeft " << mainTimeLeft;
  out << " inOvertime " << inOvertime;
  if(numPeriodsLeftIncludingCurrent != 0)
    out << " numPeriodsLeftIncludingCurrent " << numPeriodsLeftIncludingCurrent;
  if(numStonesLeftInPeriod != 0)
    out << " numStonesLeftInPeriod " << numStonesLeftInPeriod;
  if(timeLeftInPeriod != 0)
    out << " timeLeftInPeriod " << timeLeftInPeriod;

  double minTime;
  double recommendedTime;
  double maxTime;
  getTime(board,hist,lagBuffer,minTime,recommendedTime,maxTime);
  out << " minRecMax " << minTime << " " << recommendedTime << " " << maxTime;

  //Rounded time limit recommendation at the start of search
  double rrec0 = roundUpTimeLimitIfNeeded(lagBuffer,0,recommendedTime);
  //Rounded time limit recommendation as we're just about to hit limit
  double rreclimit = roundUpTimeLimitIfNeeded(lagBuffer,recommendedTime-0.000001,recommendedTime);
  //Rounded time limit recommendation as we're just about to hit rounded limit
  double rreclimit2 = roundUpTimeLimitIfNeeded(lagBuffer,rreclimit-0.000001,rreclimit);
  out << " rrec0 " << rrec0 << " rreclimit " << rreclimit << " rreclimit2 " << rreclimit2;

  return out.str();
}

std::string TimeControls::toDebugString() const {
  std::ostringstream out;
  out << "originalMainTime " << originalMainTime;
  if(increment != 0)
    out << "increment " << increment;
  if(mainTimeLimit < TimeControls::UNLIMITED_TIME_THRESHOLD)
    out << " mainTimeLimit " << mainTimeLimit;
  if(maxTimePerMove < TimeControls::UNLIMITED_TIME_THRESHOLD)
    out << " maxTimePerMove " << maxTimePerMove;
  if(originalNumPeriods != 0)
    out << "originalNumPeriods " << originalNumPeriods;
  if(numStonesPerPeriod != 0)
    out << "numStonesPerPeriod " << numStonesPerPeriod;
  if(perPeriodTime != 0)
    out << "perPeriodTime " << perPeriodTime;
  out << "mainTimeLeft " << mainTimeLeft;
  out << "inOvertime " << inOvertime;
  if(numPeriodsLeftIncludingCurrent != 0)
    out << "numPeriodsLeftIncludingCurrent " << numPeriodsLeftIncludingCurrent;
  if(numStonesLeftInPeriod != 0)
    out << "numStonesLeftInPeriod " << numStonesLeftInPeriod;
  if(timeLeftInPeriod != 0)
    out << "timeLeftInPeriod " << timeLeftInPeriod;
  return out.str();
}


static double applyLagBuffer(double time, double lagBuffer) {
  if(time < 0)
    return time;
  else if(time < 2.0 * lagBuffer)
    return time * 0.5;
  else
    return time - lagBuffer;
}

void TimeControls::getTime(const Board& board, const BoardHistory& hist, double lagBuffer, double& minTime, double& recommendedTime, double& maxTime) const {
  (void)hist;

  int boardArea = board.x_size * board.y_size;
  int numStonesOnBoard = board.numStonesOnBoard();

  //Very crude way to estimate game progress
  double approxTurnsLeftAbsolute;
  double approxTurnsLeftIncrement; //Turns left in which we plan to spend our main time
  double approxTurnsLeftByoYomi;   //Turns left in which we plan to spend our main time
  {
    double typicalGameLengthToAllowForAbsolute = 0.95 * boardArea + 20.0;
    double typicalGameLengthToAllowForIncrement = 0.75 * boardArea + 15.0;
    double typicalGameLengthToAllowForByoYomi = 0.50 * boardArea + 10.0;

    double minApproxTurnsLeftAbsolute = 0.15 * boardArea + 30.0;
    double minApproxTurnsLeftIncrement = 0.10 * boardArea + 20.0;
    double minApproxTurnsLeftByoYomi = 0.02 * boardArea + 4.0;

    approxTurnsLeftAbsolute = std::max(typicalGameLengthToAllowForAbsolute - numStonesOnBoard, minApproxTurnsLeftAbsolute);
    approxTurnsLeftIncrement = std::max(typicalGameLengthToAllowForIncrement - numStonesOnBoard, minApproxTurnsLeftIncrement);
    approxTurnsLeftByoYomi = std::max(typicalGameLengthToAllowForByoYomi - numStonesOnBoard, minApproxTurnsLeftByoYomi);

    //Multiply by 0.5 since we only make half the moves
    approxTurnsLeftAbsolute *= 0.5;
    approxTurnsLeftIncrement *= 0.5;
    approxTurnsLeftByoYomi *= 0.5;
  }

  auto divideTimeEvenlyForGame = [approxTurnsLeftAbsolute,approxTurnsLeftIncrement,approxTurnsLeftByoYomi,this](double time, bool isIncrementOrAbs, bool isByoYomi) {
    double mainTimeToUseIfAbsolute = time / approxTurnsLeftAbsolute;

    if(isIncrementOrAbs) {
      double mainTimeToUse;
      if(time <= 0)
        mainTimeToUse = time;
      else {
        mainTimeToUse = time / approxTurnsLeftIncrement;
        //Make sure that if the increment is really very small, we don't choose a policy that is all that much more extreme than absolute time.
        mainTimeToUse = std::min(mainTimeToUse, mainTimeToUseIfAbsolute + 2.0 * increment);
      }
      return mainTimeToUse;
    }

    else if(isByoYomi) {
      double mainTimeToUse;
      if(perPeriodTime <= 0 || numStonesPerPeriod <= 0)
        mainTimeToUse = mainTimeToUseIfAbsolute;
      else {
        double byoYomiTimePerMove = perPeriodTime / numStonesPerPeriod;

        //Under the assumption that we spend a fixed amount of time per move and then when we run out of main time, we use our byo yomi time, and
        //strength is proportional to log(time spent), then the optimal policy is to use e * byoYomi time per move and running out in 1/e proportion
        //of the turns that we would if we spent only the byo yomi time per move.
        double theoreticalOptimalTurnsToSpendOurTime = (time / byoYomiTimePerMove) * exp(-1.0);
        double approxTurnsLeftToUse = theoreticalOptimalTurnsToSpendOurTime;

        //If our desired time is longer than optimal (because in reality saving time for deep enough in the midgame is more important)
        //then attempt to stretch it out to some degree.
        if(approxTurnsLeftByoYomi > theoreticalOptimalTurnsToSpendOurTime)
          approxTurnsLeftToUse = std::min(approxTurnsLeftByoYomi, theoreticalOptimalTurnsToSpendOurTime * 1.75);

        //If we'd be even slower than absolute time, then of course move as if absolute time.
        if(approxTurnsLeftToUse > approxTurnsLeftAbsolute)
          approxTurnsLeftToUse = approxTurnsLeftAbsolute;
        //Make sure that at the very end of our main time, we don't do silly things
        if(approxTurnsLeftToUse < 1)
          approxTurnsLeftToUse = 1;

        mainTimeToUse = time / approxTurnsLeftToUse;
        //Make sure that if the byo yomi is really very small, we don't choose a policy that is all that much more extreme than absolute time.
        mainTimeToUse = std::min(mainTimeToUse, mainTimeToUseIfAbsolute + 3.0 * byoYomiTimePerMove);
        //Make sure that we don't use less than the byo yomi time as our "basic" time. This can happen in the transition period
        //when main time left is not large
        if(mainTimeToUse < byoYomiTimePerMove)
          mainTimeToUse = byoYomiTimePerMove;
        //If we are using less than 1.5x the byoYomiTimePerMove and doing so would dip us into byo yomi, then go ahead and dip in.
        if(mainTimeToUse < byoYomiTimePerMove * 1.5 && time < byoYomiTimePerMove * 1.5)
          mainTimeToUse = time + byoYomiTimePerMove;
      }
      return mainTimeToUse;
    }

    return mainTimeToUseIfAbsolute;
  };

  //Initialize
  minTime = 0.0;
  recommendedTime = 0.0;
  maxTime = 0.0;

  double lagBufferToUse = lagBuffer;

  //Fischer or absolute time handling
  if(increment > 0 || numPeriodsLeftIncludingCurrent <= 0) {
    if(inOvertime)
      throw StringError("TimeControls: inOvertime with Fischer or absolute time, inconsistent time control?");
    if(numPeriodsLeftIncludingCurrent != 0)
      throw StringError("TimeControls: numPeriodsLeftIncludingCurrent != 0 with Fischer or absolute time, inconsistent time control?");
    if(mainTimeLimit < originalMainTime)
      throw StringError("TimeControls: mainTimeLimit is smaller than original mainTime");

    //Note that some GTP controllers might give us a negative mainTimeLeft in weird cases. We tolerate this and do the best we can.
    if(mainTimeLeft <= increment) {
      minTime = std::min(std::max(0.0, mainTimeLeft * 0.5), std::max(0.0, mainTimeLeft + increment - mainTimeLimit));
      //Apply lagbuffer an extra time to the mainTimeLeft, ensuring we get extra buffering
      recommendedTime = applyLagBuffer(mainTimeLeft, lagBufferToUse);
      maxTime = mainTimeLeft;
    }
    else {
      //Apply lagbuffer an extra time to the excessMainTime, ensuring we get extra buffering
      double excessMainTime = applyLagBuffer(mainTimeLeft - increment, lagBufferToUse);
      minTime = std::min(std::max(0.0, mainTimeLeft * 0.5), std::max(0.0, mainTimeLeft + increment - mainTimeLimit));
      recommendedTime = increment + divideTimeEvenlyForGame(excessMainTime,true,false);
      maxTime = std::min(mainTimeLeft, increment + excessMainTime / 5.0);
    }
  }
  //Byo yomi or canadian time handling
  else {
    if(mainTimeLimit < TimeControls::UNLIMITED_TIME_THRESHOLD)
      throw StringError("TimeControls: mainTimeLimit is used with byo-yomiish periods, inconsistent time control?");
    if(numStonesPerPeriod <= 0)
      throw StringError("TimeControls: numStonesPerPeriod <= 0 with byo-yomiish periods, inconsistent time control?");
    if(!inOvertime && numPeriodsLeftIncludingCurrent != originalNumPeriods)
      throw StringError("TimeControls: not in overtime, but numPeriodsLeftIncludingCurrent != originalNumPeriods");
    if(inOvertime && numStonesLeftInPeriod < 1)
      throw StringError("TimeControls: numStonesLeftInPeriod < 1 while in overtime, inconsistent time control?");

    double effectiveMainTimeLeft = mainTimeLeft;
    bool effectivelyInOvertime = inOvertime;
    int effectiveNumPeriodsLeftIncludingCurrent = numPeriodsLeftIncludingCurrent;
    double effectiveTimeLeftInPeriod = timeLeftInPeriod;
    int effectiveNumStonesLeftInPeriod = numStonesLeftInPeriod;

    //If somehow main time left is negative, then assume we've moved into byo yomi by the appropriate amount
    if(effectiveMainTimeLeft < 0 && !effectivelyInOvertime) {
      effectivelyInOvertime = true;
      effectiveTimeLeftInPeriod = effectiveMainTimeLeft + perPeriodTime;
      effectiveNumStonesLeftInPeriod = numStonesPerPeriod;
    }
    //Similarly handle it if byo yomi time left is negative, including if main time negative overflowed into byo yomi negative
    if(effectivelyInOvertime) {
      while(effectiveTimeLeftInPeriod < 0 && effectiveNumPeriodsLeftIncludingCurrent > 1) {
        effectiveNumPeriodsLeftIncludingCurrent -= 1;
        effectiveTimeLeftInPeriod += perPeriodTime;
      }
    }

    //Crudely treat all but the last 5 periods as main time.
    constexpr int NUM_RESERVED_PERIODS = 5;
    if(effectiveNumPeriodsLeftIncludingCurrent > NUM_RESERVED_PERIODS) {
      effectivelyInOvertime = false;
      if(!inOvertime) {
        effectiveMainTimeLeft += perPeriodTime * (effectiveNumPeriodsLeftIncludingCurrent - NUM_RESERVED_PERIODS);
      }
      else {
        effectiveMainTimeLeft += effectiveTimeLeftInPeriod + perPeriodTime * (effectiveNumPeriodsLeftIncludingCurrent - NUM_RESERVED_PERIODS - 1);
      }
    }

    if(!effectivelyInOvertime) {
      //The upper limit of what we'll tolerate for spending on a move in byo yomi
      double largeByoYomiTimePerMove = perPeriodTime / (0.75 * numStonesPerPeriod + 0.25);

      minTime = 0.0;
      recommendedTime = divideTimeEvenlyForGame(effectiveMainTimeLeft,false,true);
      maxTime = largeByoYomiTimePerMove + std::max(std::min(largeByoYomiTimePerMove * 1.75, effectiveMainTimeLeft), effectiveMainTimeLeft / 5.0);

      //If we're going into byo yomi, we might as well allow using the whole period
      if(maxTime > effectiveMainTimeLeft && maxTime < effectiveMainTimeLeft + largeByoYomiTimePerMove)
        maxTime = effectiveMainTimeLeft + largeByoYomiTimePerMove;

      //Increase the lagbuffer a little if upon entering byo yomi we're actually on the last byo yomi (i.e. running out actually kills us)
      if(maxTime > effectiveMainTimeLeft && effectiveNumPeriodsLeftIncludingCurrent <= 1 && numStonesPerPeriod <= 1)
        lagBufferToUse *= 2.0;
    }
    else {
      if(effectiveNumStonesLeftInPeriod < 1)
        throw StringError("TimeControls: effectiveNumStonesLeftInPeriod < 1 while in overtime, inconsistent time control?");

      //If we're somehow lagging or reconnected so that we ended up very far in the period, and we have some periods left, then
      //go ahead and use a period so that we get more thinking time.
      if(
        effectiveNumPeriodsLeftIncludingCurrent > 1 &&
        //TODO this should take into account previous-turn thinking time, if we have enough we should be willing to insta-move to
        //save a period
          applyLagBuffer(effectiveTimeLeftInPeriod,lagBufferToUse) <
          applyLagBuffer(0.5 * perPeriodTime,lagBufferToUse) * (effectiveNumPeriodsLeftIncludingCurrent-1) / (NUM_RESERVED_PERIODS-1)
      ) {
        effectiveNumPeriodsLeftIncludingCurrent -= 1;
        effectiveTimeLeftInPeriod += perPeriodTime;
      }

      minTime = (effectiveNumStonesLeftInPeriod <= 1) ? effectiveTimeLeftInPeriod : 0.0;
      recommendedTime = effectiveTimeLeftInPeriod / effectiveNumStonesLeftInPeriod;
      maxTime = effectiveTimeLeftInPeriod / (0.75 * effectiveNumStonesLeftInPeriod + 0.25);

      //Increase the lagbuffer a little if we're actually on the last stone of the last byo yomi (i.e. running out actually kills us)
      if(effectiveNumPeriodsLeftIncludingCurrent <= 1 && effectiveNumStonesLeftInPeriod <= 1)
        lagBufferToUse *= 2.0;
    }
  }

  maxTime = std::min(maxTime, maxTimePerMove);

  //Lag buffer
  minTime = applyLagBuffer(minTime,lagBufferToUse);
  recommendedTime = applyLagBuffer(recommendedTime,lagBufferToUse);
  maxTime = applyLagBuffer(maxTime,lagBufferToUse);

  //Just in case
  if(maxTime < 0)
    maxTime = 0;
  if(minTime < 0)
    minTime = 0;
  if(recommendedTime < 0)
    recommendedTime = 0;
  if(minTime > maxTime)
    minTime = maxTime;
  if(recommendedTime > maxTime)
    recommendedTime = maxTime;
}

double TimeControls::roundUpTimeLimitIfNeeded(double lagBuffer, double timeUsed, double timeLimit) const {
  if(increment > 0 || numPeriodsLeftIncludingCurrent <= 0)
    return timeLimit;

  double effectiveMainTimeLeft = mainTimeLeft;
  bool effectivelyInOvertime = inOvertime;
  int effectiveNumPeriodsLeftIncludingCurrent = numPeriodsLeftIncludingCurrent;
  double effectiveTimeLeftInPeriod = timeLeftInPeriod;
  double effectiveNumStonesLeftInPeriod = numStonesLeftInPeriod;

  //Scroll up to where we are based on time used
  if(!effectivelyInOvertime)
    effectiveMainTimeLeft -= timeUsed;
  else
    effectiveTimeLeftInPeriod -= timeUsed;

  //Roll from main time into overtime
  if(effectiveMainTimeLeft < 0 && !effectivelyInOvertime) {
    effectivelyInOvertime = true;
    effectiveTimeLeftInPeriod = effectiveMainTimeLeft + perPeriodTime;
    effectiveNumStonesLeftInPeriod = numStonesPerPeriod;
  }

  //Roll through any ends of periods
  if(effectivelyInOvertime) {
    while(effectiveTimeLeftInPeriod < 0 && effectiveNumPeriodsLeftIncludingCurrent > 1) {
      effectiveNumPeriodsLeftIncludingCurrent -= 1;
      effectiveTimeLeftInPeriod += perPeriodTime;
    }
  }

  double roundedUpTimeUsage = timeUsed;
  double byoYomiTimePerMove = perPeriodTime / numStonesPerPeriod;
  double byoYomiTimePerMoveBuffered = applyLagBuffer(perPeriodTime / numStonesPerPeriod, lagBuffer);

  //Basically like lagbuffer, but bounded away from zero and capped at byoYomiTimePerMoveBuffered
  double bitOfTime = std::min(std::max(lagBuffer, byoYomiTimePerMoveBuffered * 0.01), byoYomiTimePerMoveBuffered);

  //Still in main time
  if(!effectivelyInOvertime) {
    //If we have very little main time left, then we might as well use it all up
    if(effectiveMainTimeLeft < byoYomiTimePerMove * 0.5) {
      //Japanese - use it up, plus the whole period, so we don't waste it.
      if(numStonesPerPeriod <= 1)
        roundedUpTimeUsage = timeUsed + effectiveMainTimeLeft + byoYomiTimePerMoveBuffered;
      //Canadian - use it up, plus at least make sure we get a bit into our overtime period
      //We might reevaluate once we actually get in to overtime.
      else
        roundedUpTimeUsage = timeUsed + effectiveMainTimeLeft + bitOfTime;
    }
    else
      return timeLimit;
  }
  //Overtime
  else {
    //We probably lost on time! Just keep the limit the same and do what we would have done without rounding
    if(effectiveTimeLeftInPeriod <= 0)
      return timeLimit;
    //If we have multiple stones left, then make sure we use at least a little fraction of our per-move time of the period
    //if we entered into main time this turn, so we don't lose time by accidentally submitting our move before finishing
    //our main time! We want to make sure to count 1 stone played in the new period.
    if(effectiveNumStonesLeftInPeriod > 1) {
      //So, if we were not in overtime at the start of this move, but we used only a tiny bit of time in the overtime...
      if(!inOvertime && (perPeriodTime - effectiveTimeLeftInPeriod) < bitOfTime)
        roundedUpTimeUsage = timeUsed + bitOfTime - (perPeriodTime - effectiveTimeLeftInPeriod);
      //If we have multiple stones left, there's no other situation where we want to artifically spend more time, we won't lose any.
      else
        return timeLimit;
    }
    //If we have one stone left, time would in fact be wasted, so then we do want to round up.
    else {
      roundedUpTimeUsage = applyLagBuffer(timeUsed + effectiveTimeLeftInPeriod, lagBuffer);
    }
  }

  if(roundedUpTimeUsage < timeUsed)
    return timeLimit;

  if(timeLimit < roundedUpTimeUsage)
    timeLimit = roundedUpTimeUsage;
  return timeLimit;

}

================
File: cpp/search/timecontrols.h
================
#ifndef SEARCH_TIMECONTROLS_H
#define SEARCH_TIMECONTROLS_H

#include "../core/global.h"
#include "../game/board.h"
#include "../game/boardhistory.h"

struct TimeControls {
  /*
    Supported time controls are Fischer or generalized byo-yomi.
    Nonzero increment together with byo yomi is not supported.

    Fisher: Always in main time. After every move, increment is added.

    Byoyomi: Either in main time, or in overtime. In overtime, we have numPeriodsLeft many periods,
    each one of perPeriodTime long, and does not get used up if we play numStonesPerPeriod stones during
    that period. numPeriodsLeft
  */
  double originalMainTime;
  double increment;
  double mainTimeLimit;
  double maxTimePerMove;
  int originalNumPeriods;
  int numStonesPerPeriod;
  double perPeriodTime;

  double mainTimeLeft;
  bool inOvertime;
  int numPeriodsLeftIncludingCurrent;
  int numStonesLeftInPeriod;
  double timeLeftInPeriod;

  //Construct a TimeControls with unlimited main time and otherwise zero initialized.
  TimeControls();
  ~TimeControls();

  //The threshold at which we consider time allowed to be unlimited
  static constexpr double UNLIMITED_TIME_THRESHOLD = 1e20;
  //The max time we tolerate a user inputting
  static constexpr double MAX_USER_INPUT_TIME = 1e25;
  //The value that fields default to when unset and need to be unlimited by default
  static constexpr double UNLIMITED_TIME_DEFAULT = 1e30;
  //The value that fields default to when unset and need to be unlimited by default and larger than other things
  static constexpr double UNLIMITED_TIME_DEFAULT_LARGE = 1e40;

  static TimeControls absoluteTime(double mainTime);
  static TimeControls fischerTime(double mainTime, double increment);
  static TimeControls fischerCappedTime(double mainTime, double increment, double mainTimeLimit, double maxTimePerMove);
  static TimeControls canadianOrByoYomiTime(
    double mainTime,
    double perPeriodTime,
    int numPeriods,
    int numStonesPerPeriod
  );

  bool isEffectivelyUnlimitedTime() const;

  //minTime - if you use less than this, you are wasting time that will not be reclaimed
  //recommendedTime - recommended mean time to search
  //maxTime - very bad to go over this time, possibly immediately losing
  void getTime(const Board& board, const BoardHistory& hist, double lagBuffer, double& minTime, double& recommendedTime, double& maxTime) const;

  //If we'd think for a given time limit and actually it would lose time to stop at this limit, then bump the limit up
  //This is used for not partial-wasting byo yomi periods.
  double roundUpTimeLimitIfNeeded(double lagBuffer, double timeUsed, double timeLimit) const;

  std::string toDebugString() const;
  std::string toDebugString(const Board& board, const BoardHistory& hist, double lagBuffer) const;
};

#endif  // SEARCH_TIMECONTROLS_H_

================
File: cpp/search/asyncbot.cpp
================
#include "../search/asyncbot.h"

#include "../core/timer.h"

using namespace std;

static void searchThreadLoop(AsyncBot* asyncBot, Logger* logger) {
  try {
    asyncBot->internalSearchThreadLoop();
  }
  catch(const exception& e) {
    logger->write(string("ERROR: Async bot thread failed: ") + e.what());
  }
  catch(const string& e) {
    logger->write("ERROR: Async bot thread failed: " + e);
  }
  catch(...) {
    logger->write("ERROR: Async bot thread failed with unexpected throw");
  }
}

AsyncBot::AsyncBot(
  SearchParams params,
  NNEvaluator* nnEval,
  Logger* l,
  const string& randSeed
)
  :search(NULL),
   controlMutex(),threadWaitingToSearch(),userWaitingForStop(),searchThread(),
   isRunning(false),isPondering(false),isKilled(false),shouldStopNow(false),
   queuedSearchId(0),queuedOnMove(),timeControls(),searchFactor(1.0),
   analyzeCallbackPeriod(-1),
   analyzeFirstCallbackAfter(-1),
   analyzeCallback(),
   searchBegunCallback()
{
  search = new Search(params,nnEval,l,randSeed);
  searchThread = std::thread(searchThreadLoop,this,l);
}

AsyncBot::AsyncBot(
  SearchParams params,
  NNEvaluator* nnEval,
  NNEvaluator* humanEval,
  Logger* l,
  const string& randSeed
)
  :search(NULL),
   controlMutex(),threadWaitingToSearch(),userWaitingForStop(),searchThread(),
   isRunning(false),isPondering(false),isKilled(false),shouldStopNow(false),
   queuedSearchId(0),queuedOnMove(),timeControls(),searchFactor(1.0),
   analyzeCallbackPeriod(-1),
   analyzeFirstCallbackAfter(-1),
   analyzeCallback(),
   searchBegunCallback()
{
  search = new Search(params,nnEval,humanEval,l,randSeed);
  searchThread = std::thread(searchThreadLoop,this,l);
}

AsyncBot::~AsyncBot() {
  stopAndWait();
  assert(!isRunning);
  {
    lock_guard<std::mutex> lock(controlMutex);
    isKilled = true;
  }
  threadWaitingToSearch.notify_all();
  searchThread.join();
  delete search;
}


const Board& AsyncBot::getRootBoard() const {
  return search->rootBoard;
}
const BoardHistory& AsyncBot::getRootHist() const {
  return search->rootHistory;
}
Player AsyncBot::getRootPla() const {
  return search->rootPla;
}
Player AsyncBot::getPlayoutDoublingAdvantagePla() const {
  return search->getPlayoutDoublingAdvantagePla();
}

Search* AsyncBot::getSearchStopAndWait() {
  stopAndWait();
  return search;
}
const Search* AsyncBot::getSearch() const {
  return search;
}
const SearchParams& AsyncBot::getParams() const {
  return search->searchParams;
}

void AsyncBot::setPosition(Player pla, const Board& board, const BoardHistory& history) {
  stopAndWait();
  search->setPosition(pla,board,history);
}
void AsyncBot::setPlayerAndClearHistory(Player pla) {
  stopAndWait();
  search->setPlayerAndClearHistory(pla);
}
void AsyncBot::setPlayerIfNew(Player pla) {
  stopAndWait();
  search->setPlayerIfNew(pla);
}
void AsyncBot::setKomiIfNew(float newKomi) {
  stopAndWait();
  search->setKomiIfNew(newKomi);
}
void AsyncBot::setAvoidMoveUntilByLoc(const std::vector<int>& bVec, const std::vector<int>& wVec) {
  stopAndWait();
  search->setAvoidMoveUntilByLoc(bVec,wVec);
}
void AsyncBot::setAvoidMoveUntilRescaleRoot(bool b) {
  stopAndWait();
  search->setAvoidMoveUntilRescaleRoot(b);
}
void AsyncBot::setRootHintLoc(Loc loc) {
  stopAndWait();
  search->setRootHintLoc(loc);
}
void AsyncBot::setAlwaysIncludeOwnerMap(bool b) {
  stopAndWait();
  search->setAlwaysIncludeOwnerMap(b);
}
void AsyncBot::setParams(SearchParams params) {
  stopAndWait();
  search->setParams(params);
}
void AsyncBot::setParamsNoClearing(SearchParams params) {
  stopAndWait();
  search->setParamsNoClearing(params);
}
void AsyncBot::setExternalPatternBonusTable(std::unique_ptr<PatternBonusTable>&& table) {
  stopAndWait();
  search->setExternalPatternBonusTable(std::move(table));
}
void AsyncBot::setCopyOfExternalPatternBonusTable(const std::unique_ptr<PatternBonusTable>& table) {
  stopAndWait();
  search->setCopyOfExternalPatternBonusTable(table);
}
void AsyncBot::clearSearch() {
  stopAndWait();
  search->clearSearch();
}

bool AsyncBot::makeMove(Loc moveLoc, Player movePla) {
  stopAndWait();
  return search->makeMove(moveLoc,movePla);
}
bool AsyncBot::makeMove(Loc moveLoc, Player movePla, bool preventEncore) {
  stopAndWait();
  return search->makeMove(moveLoc,movePla,preventEncore);
}

bool AsyncBot::isLegalTolerant(Loc moveLoc, Player movePla) const {
  return search->isLegalTolerant(moveLoc,movePla);
}
bool AsyncBot::isLegalStrict(Loc moveLoc, Player movePla) const {
  return search->isLegalStrict(moveLoc,movePla);
}

void AsyncBot::genMoveAsync(Player movePla, int searchId, const TimeControls& tc, const std::function<void(Loc,int,Search*)>& onMove) {
  genMoveAsync(movePla,searchId,tc,1.0,onMove,nullptr);
}

void AsyncBot::genMoveAsync(Player movePla, int searchId, const TimeControls& tc, double sf, const std::function<void(Loc,int,Search*)>& onMove) {
  genMoveAsync(movePla,searchId,tc,sf,onMove,nullptr);
}

void AsyncBot::genMoveAsync(Player movePla, int searchId, const TimeControls& tc, double sf, const std::function<void(Loc,int,Search*)>& onMove, const std::function<void()>& onSearchBegun) {
  std::unique_lock<std::mutex> lock(controlMutex);
  stopAndWaitAlreadyLocked(lock);
  assert(!isRunning);
  if(isKilled)
    return;

  if(movePla != search->rootPla)
    search->setPlayerAndClearHistory(movePla);

  queuedSearchId = searchId;
  queuedOnMove = onMove;
  isRunning = true;
  isPondering = false;
  shouldStopNow = false;
  timeControls = tc;
  searchFactor = sf;
  analyzeCallbackPeriod = -1;
  analyzeFirstCallbackAfter = -1;
  analyzeCallback = nullptr;
  searchBegunCallback = onSearchBegun;
  lock.unlock();
  threadWaitingToSearch.notify_all();
}

Loc AsyncBot::genMoveSynchronous(Player movePla, const TimeControls& tc) {
  return genMoveSynchronous(movePla,tc,1.0,nullptr);
}

Loc AsyncBot::genMoveSynchronous(Player movePla, const TimeControls& tc, double sf) {
  return genMoveSynchronous(movePla,tc,sf,nullptr);
}

Loc AsyncBot::genMoveSynchronous(Player movePla, const TimeControls& tc, double sf, const std::function<void()>& onSearchBegun) {
  Loc moveLoc = Board::NULL_LOC;
  std::function<void(Loc,int,Search*)> onMove = [&moveLoc](Loc loc, int searchId, Search* s) noexcept {
    assert(searchId == 0);
    (void)searchId; //avoid warning when asserts disabled
    (void)s;
    moveLoc = loc;
  };
  genMoveAsync(movePla,0,tc,sf,onMove,onSearchBegun);
  waitForSearchToEnd();
  return moveLoc;
}

void AsyncBot::ponder() {
  ponder(1.0);
}

void AsyncBot::ponder(double sf) {
  std::unique_lock<std::mutex> lock(controlMutex);
  if(isRunning)
    return;
  if(isKilled)
    return;

  queuedSearchId = 0;
  queuedOnMove = nullptr;
  isRunning = true;
  isPondering = true; //True - we are searching on the opponent's turn "for" the opponent's opponent
  shouldStopNow = false;
  timeControls = TimeControls(); //Blank time controls since opponent's clock is running, not ours, so no cap other than searchFactor
  searchFactor = sf;
  analyzeCallbackPeriod = -1;
  analyzeFirstCallbackAfter = -1;
  analyzeCallback = nullptr;
  searchBegunCallback = nullptr;
  lock.unlock();
  threadWaitingToSearch.notify_all();
}
void AsyncBot::analyzeAsync(
  Player movePla,
  double sf,
  double callbackPeriod,
  double firstCallbackAfter,
  const std::function<void(const Search* search)>& callback
) {
  std::unique_lock<std::mutex> lock(controlMutex);
  stopAndWaitAlreadyLocked(lock);
  assert(!isRunning);
  if(isKilled)
    return;

  if(movePla != search->rootPla)
    search->setPlayerAndClearHistory(movePla);

  queuedSearchId = 0;
  queuedOnMove = nullptr;
  isRunning = true;
  isPondering = false; //This should indeed be false because we are searching for the current player, not the last player we did a regular search for.
  shouldStopNow = false;
  timeControls = TimeControls(); //Blank time controls since no clock is not running, we don't cap search time other than through searchFactor.
  searchFactor = sf;
  analyzeCallbackPeriod = callbackPeriod;
  analyzeFirstCallbackAfter = firstCallbackAfter;
  analyzeCallback = callback;
  searchBegunCallback = nullptr;
  lock.unlock();
  threadWaitingToSearch.notify_all();
}

void AsyncBot::genMoveAsyncAnalyze(
  Player movePla,
  int searchId,
  const TimeControls& tc,
  double sf,
  const std::function<void(Loc,int,Search*)>& onMove,
  double callbackPeriod,
  double firstCallbackAfter,
  const std::function<void(const Search* search)>& callback
) {
  genMoveAsyncAnalyze(movePla, searchId, tc, sf, onMove, callbackPeriod, firstCallbackAfter, callback, nullptr);
}

void AsyncBot::genMoveAsyncAnalyze(
  Player movePla,
  int searchId,
  const TimeControls& tc,
  double sf,
  const std::function<void(Loc,int,Search*)>& onMove,
  double callbackPeriod,
  double firstCallbackAfter,
  const std::function<void(const Search* search)>& callback,
  const std::function<void()>& onSearchBegun
) {
  std::unique_lock<std::mutex> lock(controlMutex);
  stopAndWaitAlreadyLocked(lock);
  assert(!isRunning);
  if(isKilled)
    return;

  if(movePla != search->rootPla)
    search->setPlayerAndClearHistory(movePla);

  queuedSearchId = searchId;
  queuedOnMove = onMove;
  isRunning = true;
  isPondering = false;
  shouldStopNow = false;
  timeControls = tc;
  searchFactor = sf;
  analyzeCallbackPeriod = callbackPeriod;
  analyzeFirstCallbackAfter = firstCallbackAfter;
  analyzeCallback = callback;
  searchBegunCallback = onSearchBegun;
  lock.unlock();
  threadWaitingToSearch.notify_all();
}

Loc AsyncBot::genMoveSynchronousAnalyze(
  Player movePla,
  const TimeControls& tc,
  double sf,
  double callbackPeriod,
  double firstCallbackAfter,
  const std::function<void(const Search* search)>& callback
) {
  return genMoveSynchronousAnalyze(movePla, tc, sf, callbackPeriod, firstCallbackAfter, callback, nullptr);
}

Loc AsyncBot::genMoveSynchronousAnalyze(
  Player movePla,
  const TimeControls& tc,
  double sf,
  double callbackPeriod,
  double firstCallbackAfter,
  const std::function<void(const Search* search)>& callback,
  const std::function<void()>& onSearchBegun
) {
  Loc moveLoc = Board::NULL_LOC;
  std::function<void(Loc,int,Search*)> onMove = [&moveLoc](Loc loc, int searchId, Search* s) noexcept {
    assert(searchId == 0);
    (void)searchId; //avoid warning when asserts disabled
    (void)s;
    moveLoc = loc;
  };
  genMoveAsyncAnalyze(movePla,0,tc,sf,onMove,callbackPeriod,firstCallbackAfter,callback,onSearchBegun);
  waitForSearchToEnd();
  return moveLoc;
}

void AsyncBot::stopWithoutWait() {
  shouldStopNow.store(true);
}

void AsyncBot::setKilled() {
  lock_guard<std::mutex> lock(controlMutex);
  isKilled = true;
  shouldStopNow.store(true);
  threadWaitingToSearch.notify_all();
}

void AsyncBot::stopAndWait() {
  shouldStopNow.store(true);
  waitForSearchToEnd();
}

void AsyncBot::stopAndWaitAlreadyLocked(std::unique_lock<std::mutex>& lock) {
  shouldStopNow.store(true);
  waitForSearchToEndAlreadyLocked(lock);
}

void AsyncBot::waitForSearchToEnd() {
  std::unique_lock<std::mutex> lock(controlMutex);
  while(isRunning)
    userWaitingForStop.wait(lock);
}

void AsyncBot::waitForSearchToEndAlreadyLocked(std::unique_lock<std::mutex>& lock) {
  while(isRunning)
    userWaitingForStop.wait(lock);
}

void AsyncBot::internalSearchThreadLoop() {
  std::unique_lock<std::mutex> lock(controlMutex);
  while(true) {
    while(!isRunning && !isKilled)
      threadWaitingToSearch.wait(lock);
    if(isKilled) {
      isRunning = false;
      isPondering = false;
      userWaitingForStop.notify_all();
      break;
    }

    const bool pondering = isPondering;
    const TimeControls tc = timeControls;
    double callbackPeriod = analyzeCallbackPeriod;
    double firstCallbackAfter = analyzeFirstCallbackAfter;
    //Make local copies just in case, to simplify thread reasoning for the member fields
    std::function<void(const Search*)> analyzeCallbackLocal = analyzeCallback;
    std::function<void()> searchBegunCallbackLocal = searchBegunCallback;
    lock.unlock();

    //Make sure we don't feed in absurdly large numbers, this seems to cause wait_for to hang.
    //For a long period, just don't do callbacks.
    if(callbackPeriod >= 10000000)
      callbackPeriod = -1;
    if(firstCallbackAfter >= 10000000) {
      firstCallbackAfter = -1;
      callbackPeriod = -1;
    }

    //Handle search begun and analysis callback loops
    const bool usingCallbackLoop = (firstCallbackAfter >= 0 || callbackPeriod >= 0) && analyzeCallbackLocal;

    bool isSearchBegun = false;
    condition_variable callbackLoopWaitingForSearchBegun;
    std::function<void()> searchBegun = [this,usingCallbackLoop,&isSearchBegun,&searchBegunCallbackLocal,&callbackLoopWaitingForSearchBegun]() {
      if(searchBegunCallbackLocal)
        searchBegunCallbackLocal();
      if(usingCallbackLoop) {
        std::lock_guard<std::mutex> callbackLock(controlMutex);
        isSearchBegun = true;
        callbackLoopWaitingForSearchBegun.notify_all();
      }
    };

    condition_variable callbackLoopWaiting;
    atomic<bool> callbackLoopShouldStop(false);
    auto callbackLoop = [this,&isSearchBegun,&callbackLoopWaitingForSearchBegun,callbackPeriod,firstCallbackAfter,&analyzeCallbackLocal,&callbackLoopWaiting,&callbackLoopShouldStop]() {
      std::unique_lock<std::mutex> callbackLock(controlMutex);
      while(!isSearchBegun) {
        callbackLoopWaitingForSearchBegun.wait(callbackLock);
        if(callbackLoopShouldStop.load())
          return;
      }

      bool isFirstLoop = true;
      while(true) {
        double periodToWait = isFirstLoop ? firstCallbackAfter : callbackPeriod;
        if(periodToWait < 0)
          return;
        isFirstLoop = false;

        callbackLoopWaiting.wait_for(
          callbackLock,
          std::chrono::duration<double>(periodToWait),
          [&callbackLoopShouldStop](){return callbackLoopShouldStop.load();}
        );
        if(callbackLoopShouldStop.load())
          return;
        callbackLock.unlock();
        analyzeCallbackLocal(search);
        callbackLock.lock();
      }
    };

    std::thread callbackLoopThread;
    if(usingCallbackLoop) {
      callbackLoopThread = std::thread(callbackLoop);
    }

    search->runWholeSearch(shouldStopNow,&searchBegun,pondering,tc,searchFactor);
    Loc moveLoc = search->getChosenMoveLoc();

    if(usingCallbackLoop) {
      lock.lock();
      callbackLoopShouldStop.store(true);
      callbackLoopWaitingForSearchBegun.notify_all();
      callbackLoopWaiting.notify_all();
      lock.unlock();
      callbackLoopThread.join();
    }

    lock.lock();
    //Call queuedOnMove under the lock just in case
    if(queuedOnMove)
      queuedOnMove(moveLoc,queuedSearchId,search);
    isRunning = false;
    isPondering = false;
    userWaitingForStop.notify_all();
  }
}

================
File: cpp/search/searchexplorehelpers.cpp
================
#include "../search/search.h"

#include "../search/searchnode.h"

//------------------------
#include "../core/using.h"
//------------------------

static double cpuctExploration(double totalChildWeight, const SearchParams& searchParams) {
  return searchParams.cpuctExploration +
    searchParams.cpuctExplorationLog * log((totalChildWeight + searchParams.cpuctExplorationBase) / searchParams.cpuctExplorationBase);
}

static double cpuctExplorationHuman(double totalChildWeight, const SearchParams& searchParams) {
  return searchParams.humanSLCpuctExploration + searchParams.humanSLCpuctPermanent * sqrt(totalChildWeight);
}

//Tiny constant to add to numerator of puct formula to make it positive
//even when visits = 0.
static constexpr double TOTALCHILDWEIGHT_PUCT_OFFSET = 0.01;

double Search::getExploreScaling(
  double totalChildWeight, double parentUtilityStdevFactor
) const {
  return
    cpuctExploration(totalChildWeight, searchParams)
    * sqrt(totalChildWeight + TOTALCHILDWEIGHT_PUCT_OFFSET)
    * parentUtilityStdevFactor;
}
double Search::getExploreScalingHuman(
  double totalChildWeight
) const {
  return
    cpuctExplorationHuman(totalChildWeight, searchParams)
    * sqrt(totalChildWeight + TOTALCHILDWEIGHT_PUCT_OFFSET);
}

double Search::getExploreSelectionValue(
  double exploreScaling,
  double nnPolicyProb,
  double childWeight,
  double childUtility,
  Player pla
) const {
  if(nnPolicyProb < 0)
    return POLICY_ILLEGAL_SELECTION_VALUE;

  double exploreComponent = exploreScaling * nnPolicyProb / (1.0 + childWeight);

  //At the last moment, adjust value to be from the player's perspective, so that players prefer values in their favor
  //rather than in white's favor
  double valueComponent = pla == P_WHITE ? childUtility : -childUtility;
  return exploreComponent + valueComponent;
}

//Return the childWeight that would make Search::getExploreSelectionValue return the given explore selection value.
//Or return 0, if it would be less than 0.
double Search::getExploreSelectionValueInverse(
  double exploreSelectionValue,
  double exploreScaling,
  double nnPolicyProb,
  double childUtility,
  Player pla
) const {
  if(nnPolicyProb < 0)
    return 0;
  double valueComponent = pla == P_WHITE ? childUtility : -childUtility;

  double exploreComponent = exploreSelectionValue - valueComponent;
  double exploreComponentScaling = exploreScaling * nnPolicyProb;

  //Guard against float weirdness
  if(exploreComponent <= 0)
    return 1e100;

  double childWeight = exploreComponentScaling / exploreComponent - 1;
  if(childWeight < 0)
    childWeight = 0;
  return childWeight;
}

static void maybeApplyWideRootNoise(
  double& childUtility,
  float& nnPolicyProb,
  const SearchParams& searchParams,
  SearchThread* thread,
  const SearchNode& parent
) {
  //For very large wideRootNoise, go ahead and also smooth out the policy
  nnPolicyProb = (float)pow(nnPolicyProb, 1.0 / (4.0*searchParams.wideRootNoise + 1.0));
  if(thread->rand.nextBool(0.5)) {
    double bonus = searchParams.wideRootNoise * std::fabs(thread->rand.nextGaussian());
    if(parent.nextPla == P_WHITE)
      childUtility += bonus;
    else
      childUtility -= bonus;
  }
}


double Search::getExploreSelectionValueOfChild(
  const SearchNode& parent, const float* parentPolicyProbs, const SearchNode* child,
  Loc moveLoc,
  double exploreScaling,
  double totalChildWeight, int64_t childEdgeVisits, double fpuValue,
  double parentUtility, double parentWeightPerVisit,
  bool isDuringSearch, bool antiMirror, double maxChildWeight,
  bool countEdgeVisit,
  SearchThread* thread
) const {
  (void)parentUtility;
  int movePos = getPos(moveLoc);
  float nnPolicyProb = parentPolicyProbs[movePos];

  int32_t childVirtualLosses = child->virtualLosses.load(std::memory_order_acquire);
  int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
  double utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);
  double scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
  double scoreMeanSqAvg = child->stats.scoreMeanSqAvg.load(std::memory_order_acquire);
  double childWeight;
  if(countEdgeVisit)
    childWeight = child->stats.getChildWeight(childEdgeVisits,childVisits);
  else
    childWeight = child->stats.weightSum.load(std::memory_order_acquire);

  //It's possible that childVisits is actually 0 here with multithreading because we're visiting this node while a child has
  //been expanded but its thread not yet finished its first visit.
  //It's also possible that we observe childWeight <= 0 even though childVisits >= due to multithreading, the two could
  //be out of sync briefly since they are separate atomics.
  double childUtility;
  if(childVisits <= 0 || childWeight <= 0.0)
    childUtility = fpuValue;
  else {
    childUtility = utilityAvg;

    //Tiny adjustment for passing
    double endingScoreBonus = getEndingWhiteScoreBonus(parent,moveLoc);
    if(endingScoreBonus != 0)
      childUtility += getScoreUtilityDiff(scoreMeanAvg, scoreMeanSqAvg, endingScoreBonus);
  }

  //Virtual losses to direct threads down different paths
  if(childVirtualLosses > 0) {
    double virtualLossWeight = childVirtualLosses * searchParams.numVirtualLossesPerThread;

    double utilityRadius = searchParams.winLossUtilityFactor + searchParams.staticScoreUtilityFactor + searchParams.dynamicScoreUtilityFactor;
    double virtualLossUtility = (parent.nextPla == P_WHITE ? -utilityRadius : utilityRadius);
    double virtualLossWeightFrac = (double)virtualLossWeight / (virtualLossWeight + std::max(0.25,childWeight));
    childUtility = childUtility + (virtualLossUtility - childUtility) * virtualLossWeightFrac;
    childWeight += virtualLossWeight;
  }

  if(isDuringSearch && (&parent == rootNode) && countEdgeVisit) {
    //Futile visits pruning - skip this move if the amount of time we have left to search is too small, assuming
    //its average weight per visit is maintained.
    //We use childVisits rather than childEdgeVisits for the final estimate since when childEdgeVisits < childVisits, adding new visits is instant.
    if(searchParams.futileVisitsThreshold > 0) {
      double requiredWeight = searchParams.futileVisitsThreshold * maxChildWeight;
      //Avoid divide by 0 by adding a prior equal to the parent's weight per visit
      double averageVisitsPerWeight = (childEdgeVisits + 1.0) / (childWeight + parentWeightPerVisit);
      double estimatedRequiredVisits = requiredWeight * averageVisitsPerWeight;
      if(childVisits + thread->upperBoundVisitsLeft < estimatedRequiredVisits)
        return FUTILE_VISITS_PRUNE_VALUE;
    }
    //Hack to get the root to funnel more visits down child branches
    if(searchParams.rootDesiredPerChildVisitsCoeff > 0.0) {
      if(nnPolicyProb > 0 && childWeight < sqrt(nnPolicyProb * totalChildWeight * searchParams.rootDesiredPerChildVisitsCoeff)) {
        return 1e20;
      }
    }
    //Hack for hintloc - must search this move almost as often as the most searched move
    if(rootHintLoc != Board::NULL_LOC && moveLoc == rootHintLoc) {
      double averageWeightPerVisit = (childWeight + parentWeightPerVisit) / (childVisits + 1.0);
      ConstSearchNodeChildrenReference children = parent.getChildren();
      int childrenCapacity = children.getCapacity();
      for(int i = 0; i<childrenCapacity; i++) {
        const SearchChildPointer& childPointer = children[i];
        const SearchNode* c = childPointer.getIfAllocated();
        if(c == NULL)
          break;
        int64_t cEdgeVisits = childPointer.getEdgeVisits();
        double cWeight = c->stats.getChildWeight(cEdgeVisits);
        if(childWeight + averageWeightPerVisit < cWeight * 0.8)
          return 1e20;
      }
    }

    if(searchParams.wideRootNoise > 0.0 && nnPolicyProb >= 0) {
      maybeApplyWideRootNoise(childUtility, nnPolicyProb, searchParams, thread, parent);
    }
  }
  if(isDuringSearch && antiMirror && nnPolicyProb >= 0 && countEdgeVisit) {
    maybeApplyAntiMirrorPolicy(nnPolicyProb, moveLoc, parentPolicyProbs, parent.nextPla, thread);
    maybeApplyAntiMirrorForcedExplore(childUtility, parentUtility, moveLoc, parentPolicyProbs, childWeight, totalChildWeight, parent.nextPla, thread, parent);
  }

  return getExploreSelectionValue(exploreScaling,nnPolicyProb,childWeight,childUtility,parent.nextPla);
}

double Search::getNewExploreSelectionValue(
  const SearchNode& parent,
  double exploreScaling,
  float nnPolicyProb,
  double fpuValue,
  double parentWeightPerVisit,
  double maxChildWeight,
  bool countEdgeVisit,
  SearchThread* thread
) const {
  double childWeight = 0;
  double childUtility = fpuValue;
  if(&parent == rootNode && countEdgeVisit) {
    //Futile visits pruning - skip this move if the amount of time we have left to search is too small
    if(searchParams.futileVisitsThreshold > 0) {
      //Avoid divide by 0 by adding a prior equal to the parent's weight per visit
      double averageVisitsPerWeight = 1.0 / parentWeightPerVisit;
      double requiredWeight = searchParams.futileVisitsThreshold * maxChildWeight;
      double estimatedRequiredVisits = requiredWeight * averageVisitsPerWeight;
      if(thread->upperBoundVisitsLeft < estimatedRequiredVisits)
        return FUTILE_VISITS_PRUNE_VALUE;
    }
    if(searchParams.wideRootNoise > 0.0) {
      maybeApplyWideRootNoise(childUtility, nnPolicyProb, searchParams, thread, parent);
    }
  }
  return getExploreSelectionValue(exploreScaling,nnPolicyProb,childWeight,childUtility,parent.nextPla);
}

double Search::getReducedPlaySelectionWeight(
  const SearchNode& parent, const float* parentPolicyProbs, const SearchNode* child,
  Loc moveLoc,
  double exploreScaling,
  int64_t childEdgeVisits,
  double bestChildExploreSelectionValue
) const {
  assert(&parent == rootNode);
  int movePos = getPos(moveLoc);
  float nnPolicyProb = parentPolicyProbs[movePos];

  int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
  double scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
  double scoreMeanSqAvg = child->stats.scoreMeanSqAvg.load(std::memory_order_acquire);
  double utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);
  double childWeight = child->stats.getChildWeight(childEdgeVisits,childVisits);

  //Child visits may be 0 if this function is called in a multithreaded context, such as during live analysis
  //Child weight may also be 0 if it's out of sync.
  if(childVisits <= 0 || childWeight <= 0.0)
    return 0;

  //Tiny adjustment for passing
  double endingScoreBonus = getEndingWhiteScoreBonus(parent,moveLoc);
  double childUtility = utilityAvg;
  if(endingScoreBonus != 0)
    childUtility += getScoreUtilityDiff(scoreMeanAvg, scoreMeanSqAvg, endingScoreBonus);

  double childWeightWeRetrospectivelyWanted = getExploreSelectionValueInverse(
    bestChildExploreSelectionValue, exploreScaling, nnPolicyProb, childUtility, parent.nextPla
  );
  if(childWeight > childWeightWeRetrospectivelyWanted)
    return childWeightWeRetrospectivelyWanted;
  return childWeight;
}

double Search::getFpuValueForChildrenAssumeVisited(
  const SearchNode& node, Player pla, bool isRoot, double policyProbMassVisited,
  double& parentUtility, double& parentWeightPerVisit, double& parentUtilityStdevFactor
) const {
  int64_t visits = node.stats.visits.load(std::memory_order_acquire);
  double weightSum = node.stats.weightSum.load(std::memory_order_acquire);
  double utilityAvg = node.stats.utilityAvg.load(std::memory_order_acquire);
  double utilitySqAvg = node.stats.utilitySqAvg.load(std::memory_order_acquire);

  assert(visits > 0);
  assert(weightSum > 0.0);
  parentWeightPerVisit = weightSum / visits;
  parentUtility = utilityAvg;
  double variancePrior = searchParams.cpuctUtilityStdevPrior * searchParams.cpuctUtilityStdevPrior;
  double variancePriorWeight = searchParams.cpuctUtilityStdevPriorWeight;
  double parentUtilityStdev;
  if(visits <= 0 || weightSum <= 1)
    parentUtilityStdev = searchParams.cpuctUtilityStdevPrior;
  else {
    double utilitySq = parentUtility * parentUtility;
    //Make sure we're robust to numerical precision issues or threading desync of these values, so we don't observe negative variance
    if(utilitySqAvg < utilitySq)
      utilitySqAvg = utilitySq;
    parentUtilityStdev = sqrt(
      std::max(
        0.0,
        ((utilitySq + variancePrior) * variancePriorWeight + utilitySqAvg * weightSum)
        / (variancePriorWeight + weightSum - 1.0)
        - utilitySq
      )
    );
  }
  parentUtilityStdevFactor = 1.0 + searchParams.cpuctUtilityStdevScale * (parentUtilityStdev / searchParams.cpuctUtilityStdevPrior - 1.0);

  double parentUtilityForFPU = parentUtility;
  if(searchParams.fpuParentWeightByVisitedPolicy) {
    double avgWeight = std::min(1.0, pow(policyProbMassVisited, searchParams.fpuParentWeightByVisitedPolicyPow));
    parentUtilityForFPU = avgWeight * parentUtility + (1.0 - avgWeight) * getUtilityFromNN(*(node.getNNOutput()));
  }
  else if(searchParams.fpuParentWeight > 0.0) {
    parentUtilityForFPU = searchParams.fpuParentWeight * getUtilityFromNN(*(node.getNNOutput())) + (1.0 - searchParams.fpuParentWeight) * parentUtility;
  }

  double fpuValue;
  {
    double fpuReductionMax = isRoot ? searchParams.rootFpuReductionMax : searchParams.fpuReductionMax;
    double fpuLossProp = isRoot ? searchParams.rootFpuLossProp : searchParams.fpuLossProp;
    double utilityRadius = searchParams.winLossUtilityFactor + searchParams.staticScoreUtilityFactor + searchParams.dynamicScoreUtilityFactor;

    double reduction = fpuReductionMax * sqrt(policyProbMassVisited);
    fpuValue = pla == P_WHITE ? parentUtilityForFPU - reduction : parentUtilityForFPU + reduction;
    double lossValue = pla == P_WHITE ? -utilityRadius : utilityRadius;
    fpuValue = fpuValue + (lossValue - fpuValue) * fpuLossProp;
  }

  return fpuValue;
}


void Search::selectBestChildToDescend(
  SearchThread& thread, const SearchNode& node, SearchNodeState nodeState,
  int& numChildrenFound, int& bestChildIdx, Loc& bestChildMoveLoc, bool& countEdgeVisit,
  bool isRoot) const
{
  assert(thread.pla == node.nextPla);

  double maxSelectionValue = POLICY_ILLEGAL_SELECTION_VALUE;
  bestChildIdx = -1;
  bestChildMoveLoc = Board::NULL_LOC;
  countEdgeVisit = true;

  ConstSearchNodeChildrenReference children = node.getChildren(nodeState);
  int childrenCapacity = children.getCapacity();

  double policyProbMassVisited = 0.0;
  double maxChildWeight = 0.0;
  double totalChildWeight = 0.0;
  int64_t totalChildEdgeVisits = 0;
  const NNOutput* nnOutput = node.getNNOutput();
  assert(nnOutput != NULL);
  const float* policyProbs = nnOutput->getPolicyProbsMaybeNoised();
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    Loc moveLoc = childPointer.getMoveLocRelaxed();
    int movePos = getPos(moveLoc);
    float nnPolicyProb = policyProbs[movePos];
    if(nnPolicyProb < 0)
      continue;
    policyProbMassVisited += nnPolicyProb;

    int64_t edgeVisits = childPointer.getEdgeVisits();
    double childWeight = child->stats.getChildWeight(edgeVisits);

    totalChildWeight += childWeight;
    if(childWeight > maxChildWeight)
      maxChildWeight = childWeight;
    totalChildEdgeVisits += edgeVisits;
  }

  bool useHumanSL = false;
  if(humanEvaluator != NULL &&
     (searchParams.humanSLProfile.initialized || !humanEvaluator->requiresSGFMetadata())
  ) {
    const NNOutput* humanOutput = node.getHumanOutput();
    if(humanOutput != NULL) {
      double weightlessProb;
      double weightfulProb;
      if(isRoot) {
        weightlessProb = searchParams.humanSLRootExploreProbWeightless;
        weightfulProb = searchParams.humanSLRootExploreProbWeightful;
      }
      else if(thread.pla == rootPla) {
        weightlessProb = searchParams.humanSLPlaExploreProbWeightless;
        weightfulProb = searchParams.humanSLPlaExploreProbWeightful;
      }
      else {
        weightlessProb = searchParams.humanSLOppExploreProbWeightless;
        weightfulProb = searchParams.humanSLOppExploreProbWeightful;
      }

      double totalHumanProb = weightlessProb + weightfulProb;
      if(totalHumanProb > 0.0) {
        double r = thread.rand.nextDouble();
        if(r < weightlessProb) {
          useHumanSL = true;
          countEdgeVisit = false;
        }
        else if(r < totalHumanProb) {
          useHumanSL = true;
        }
      }
    }

    // Swap out policy and also recompute policy prob mass visited
    if(useHumanSL) {
      nnOutput = humanOutput;
      policyProbMassVisited = 0.0;
      assert(nnOutput != NULL);
      policyProbs = nnOutput->getPolicyProbsMaybeNoised();
      for(int i = 0; i<childrenCapacity; i++) {
        const SearchChildPointer& childPointer = children[i];
        const SearchNode* child = childPointer.getIfAllocated();
        if(child == NULL)
          break;
        Loc moveLoc = childPointer.getMoveLocRelaxed();
        int movePos = getPos(moveLoc);
        float nnPolicyProb = policyProbs[movePos];
        if(nnPolicyProb < 0)
          continue;
        policyProbMassVisited += nnPolicyProb;
      }
    }
  }

  //Probability mass should not sum to more than 1, giving a generous allowance
  //for floating point error.
  assert(policyProbMassVisited <= 1.0001);

  //If we're doing a weightless visit, then we should redo PUCT to operate on child node weight, not child edge weight
  if(!countEdgeVisit) {
    totalChildWeight = 0.0;
    maxChildWeight = 0.0;
    for(int i = 0; i<childrenCapacity; i++) {
      const SearchChildPointer& childPointer = children[i];
      const SearchNode* child = childPointer.getIfAllocated();
      if(child == NULL)
        break;
      double childWeight = child->stats.weightSum.load(std::memory_order_acquire);
      totalChildWeight += childWeight;
      if(childWeight > maxChildWeight)
        maxChildWeight = childWeight;
    }
  }

  //First play urgency
  double parentUtility;
  double parentWeightPerVisit;
  double parentUtilityStdevFactor;
  double fpuValue = getFpuValueForChildrenAssumeVisited(
    node, thread.pla, isRoot, policyProbMassVisited,
    parentUtility, parentWeightPerVisit, parentUtilityStdevFactor
  );

  bool posesWithChildBuf[NNPos::MAX_NN_POLICY_SIZE] = { }; // Initialize all to false
  bool antiMirror = searchParams.antiMirror && mirroringPla != C_EMPTY && isMirroringSinceSearchStart(thread.history,0);

  double exploreScaling;
  if(useHumanSL)
    exploreScaling = getExploreScalingHuman(totalChildWeight);
  else
    exploreScaling = getExploreScaling(totalChildWeight, parentUtilityStdevFactor);

  //Try all existing children
  //Also count how many children we actually find
  numChildrenFound = 0;
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    numChildrenFound++;
    int64_t childEdgeVisits = childPointer.getEdgeVisits();

    Loc moveLoc = childPointer.getMoveLocRelaxed();
    bool isDuringSearch = true;
    double selectionValue = getExploreSelectionValueOfChild(
      node,policyProbs,child,
      moveLoc,
      exploreScaling,
      totalChildWeight,childEdgeVisits,fpuValue,
      parentUtility,parentWeightPerVisit,
      isDuringSearch,antiMirror,maxChildWeight,
      countEdgeVisit,
      &thread
    );
    if(selectionValue > maxSelectionValue) {
      // if(child->state.load(std::memory_order_seq_cst) == SearchNode::STATE_EVALUATING) {
      //   selectionValue -= EVALUATING_SELECTION_VALUE_PENALTY;
      //   if(isRoot && child->prevMoveLoc == Location::ofString("K4",thread.board)) {
      //     out << "ouch" << "\n";
      //   }
      // }
      maxSelectionValue = selectionValue;
      bestChildIdx = i;
      bestChildMoveLoc = moveLoc;
    }

    posesWithChildBuf[getPos(moveLoc)] = true;
  }

  const std::vector<int>& avoidMoveUntilByLoc = thread.pla == P_BLACK ? avoidMoveUntilByLocBlack : avoidMoveUntilByLocWhite;

  //Try the new child with the best policy value
  Loc bestNewMoveLoc = Board::NULL_LOC;
  float bestNewNNPolicyProb = -1.0f;
  for(int movePos = 0; movePos<policySize; movePos++) {
    bool alreadyTried = posesWithChildBuf[movePos];
    if(alreadyTried)
      continue;

    Loc moveLoc = NNPos::posToLoc(movePos,thread.board.x_size,thread.board.y_size,nnXLen,nnYLen);
    if(moveLoc == Board::NULL_LOC)
      continue;

    //Special logic for the root
    if(isRoot) {
      assert(thread.board.pos_hash == rootBoard.pos_hash);
      assert(thread.pla == rootPla);
      if(!isAllowedRootMove(moveLoc))
        continue;
    }
    if(avoidMoveUntilByLoc.size() > 0) {
      assert(avoidMoveUntilByLoc.size() >= Board::MAX_ARR_SIZE);
      int untilDepth = avoidMoveUntilByLoc[moveLoc];
      if(thread.history.moveHistory.size() - rootHistory.moveHistory.size() < untilDepth)
        continue;
    }

    //Quit immediately for illegal moves
    float nnPolicyProb = policyProbs[movePos];
    if(nnPolicyProb < 0)
      continue;

    if(antiMirror) {
      maybeApplyAntiMirrorPolicy(nnPolicyProb, moveLoc, policyProbs, node.nextPla, &thread);
    }

    if(nnPolicyProb > bestNewNNPolicyProb) {
      bestNewNNPolicyProb = nnPolicyProb;
      bestNewMoveLoc = moveLoc;
    }
  }
  if(bestNewMoveLoc != Board::NULL_LOC) {
    double selectionValue = getNewExploreSelectionValue(
      node,
      exploreScaling,
      bestNewNNPolicyProb,fpuValue,
      parentWeightPerVisit,
      maxChildWeight,
      countEdgeVisit,
      &thread
    );
    if(selectionValue > maxSelectionValue) {
      maxSelectionValue = selectionValue;
      bestChildIdx = numChildrenFound;
      bestChildMoveLoc = bestNewMoveLoc;
    }
  }

  if(totalChildEdgeVisits >= 2 && searchParams.enableMorePassingHacks && thread.history.passWouldEndPhase(thread.board,thread.pla)) {
    bool hasPassMove = false;
    bool hasNonPassMove = false;
    for(int i = 0; i<childrenCapacity; i++) {
      const SearchChildPointer& childPointer = children[i];
      const SearchNode* child = childPointer.getIfAllocated();
      if(child == NULL)
        break;
      Loc moveLoc = childPointer.getMoveLocRelaxed();
      if(moveLoc == Board::PASS_LOC)
        hasPassMove = true;
      else
        hasNonPassMove = true;
    }
    if(!hasPassMove && bestChildMoveLoc != Board::PASS_LOC) {
      bestChildIdx = numChildrenFound;
      bestChildMoveLoc = Board::PASS_LOC;
      countEdgeVisit = false;
      // Specifically for these special extra-pass search playouts, we don't count them for the purpose of visit/playout limits.
      thread.shouldCountPlayout = false;
    }
    else if(!hasNonPassMove && bestChildMoveLoc == Board::PASS_LOC && bestNewMoveLoc != Board::PASS_LOC) {
      bestChildIdx = numChildrenFound;
      bestChildMoveLoc = bestNewMoveLoc;
      countEdgeVisit = false;
      // Specifically for these special extra-pass search playouts, we don't count them for the purpose of visit/playout limits.
      thread.shouldCountPlayout = false;
    }
  }

}

================
File: cpp/search/searchmultithreadhelpers.cpp
================
#include "../search/search.h"

#include "../search/searchnode.h"

//------------------------
#include "../core/using.h"
//------------------------

static void threadTaskLoop(Search* search, int threadIdx) {
  while(true) {
    std::function<void(int)>* task;
    bool suc = search->threadTasks[threadIdx-1].waitPop(task);
    if(!suc)
      return;

    try {
      (*task)(threadIdx);
      //Don't delete task, the convention is tasks are owned by the joining thread
    }
    catch(const std::exception& e) {
      search->logger->write(string("ERROR: Search thread failed: ") + e.what());
      search->threadTasksRemaining->add(-1);
      throw;
    }
    catch(const string& e) {
      search->logger->write("ERROR: Search thread failed: " + e);
      search->threadTasksRemaining->add(-1);
      throw;
    }
    catch(...) {
      search->logger->write("ERROR: Search thread failed with unexpected throw");
      search->threadTasksRemaining->add(-1);
      throw;
    }
    search->threadTasksRemaining->add(-1);
  }
}

int Search::numAdditionalThreadsToUseForTasks() const {
  return searchParams.numThreads-1;
}

void Search::spawnThreadsIfNeeded() {
  int desiredNumAdditionalThreads = numAdditionalThreadsToUseForTasks();
  if(numThreadsSpawned >= desiredNumAdditionalThreads)
    return;
  killThreads();
  threadTasks = new ThreadSafeQueue<std::function<void(int)>*>[desiredNumAdditionalThreads];
  threadTasksRemaining = new ThreadSafeCounter();
  threads = new std::thread[desiredNumAdditionalThreads];
  for(int i = 0; i<desiredNumAdditionalThreads; i++)
    threads[i] = std::thread(threadTaskLoop,this,i+1);
  numThreadsSpawned = desiredNumAdditionalThreads;
}

void Search::killThreads() {
  if(numThreadsSpawned <= 0)
    return;
  for(int i = 0; i<numThreadsSpawned; i++)
    threadTasks[i].close();
  for(int i = 0; i<numThreadsSpawned; i++)
    threads[i].join();
  delete[] threadTasks;
  delete threadTasksRemaining;
  delete[] threads;
  threadTasks = NULL;
  threadTasksRemaining = NULL;
  threads = NULL;
  numThreadsSpawned = 0;
}

void Search::respawnThreads() {
  killThreads();
  spawnThreadsIfNeeded();
}

void Search::performTaskWithThreads(std::function<void(int)>* task, int capThreads) {
  spawnThreadsIfNeeded();
  int numAdditionalThreadsToUse = std::min(capThreads-1, numAdditionalThreadsToUseForTasks());
  if(numAdditionalThreadsToUse <= 0) {
    (*task)(0);
  }
  else {
    assert(numAdditionalThreadsToUse <= numThreadsSpawned);
    threadTasksRemaining->add(numAdditionalThreadsToUse);
    for(int i = 0; i<numAdditionalThreadsToUse; i++)
      threadTasks[i].forcePush(task);
    (*task)(0);
    threadTasksRemaining->waitUntilZero();
  }
}


static void maybeAppendShuffledIntRange(int cap, PCG32* rand, std::vector<int>& randBuf) {
  if(rand != NULL) {
    size_t randBufStart = randBuf.size();
    for(int i = 0; i<cap; i++)
      randBuf.push_back(i);
    for(int i = 1; i<cap; i++) {
      int r = (int)(rand->nextUInt() % (uint32_t)(i+1));
      int tmp = randBuf[randBufStart+i];
      randBuf[randBufStart+i] = randBuf[randBufStart+r];
      randBuf[randBufStart+r] = tmp;
    }
  }
}

//Walk over all nodes and their children recursively and call f, children first.
//Assumes that only other instances of this function are running - in particular, the tree
//is not being mutated by something else. It's okay if f mutates nodes, so long as it only mutates
//nodes that will no longer be iterated over (namely, only stuff at the node or within its subtree).
//As a side effect, nodeAge == searchNodeAge will be true only for the nodes walked over.
void Search::applyRecursivelyPostOrderMulithreaded(const vector<SearchNode*>& nodes, std::function<void(SearchNode*,int)>* f) {
  //We invalidate all node ages so we can use them as a marker for what's done.
  searchNodeAge += 1;

  //Simple cheap RNGs so that we can get the different threads into different parts of the tree and not clash.
  int numAdditionalThreads = numAdditionalThreadsToUseForTasks();
  std::vector<PCG32*> rands(numAdditionalThreads+1, NULL);
  for(int threadIdx = 1; threadIdx<numAdditionalThreads+1; threadIdx++)
    rands[threadIdx] = new PCG32(nonSearchRand.nextUInt64());

  int numChildren = (int)nodes.size();
  std::function<void(int)> g = [&](int threadIdx) {
    assert(threadIdx >= 0 && threadIdx < rands.size());
    PCG32* rand = rands[threadIdx];
    std::unordered_set<SearchNode*> nodeBuf;
    std::vector<int> randBuf;

    size_t randBufStart = randBuf.size();
    maybeAppendShuffledIntRange(numChildren, rand, randBuf);
    for(int i = 0; i<numChildren; i++) {
      int childIdx = rand != NULL ? randBuf[randBufStart+i] : i;
      applyRecursivelyPostOrderMulithreadedHelper(nodes[childIdx],threadIdx,rand,nodeBuf,randBuf,f);
    }
    randBuf.resize(randBufStart);
  };
  performTaskWithThreads(&g, 0x3fffFFFF);
  for(int threadIdx = 1; threadIdx<numAdditionalThreads+1; threadIdx++)
    delete rands[threadIdx];
}

void Search::applyRecursivelyPostOrderMulithreadedHelper(
  SearchNode* node, int threadIdx, PCG32* rand, std::unordered_set<SearchNode*>& nodeBuf, std::vector<int>& randBuf, std::function<void(SearchNode*,int)>* f
) {
  //nodeAge == searchNodeAge means that the node is done.
  if(node->nodeAge.load(std::memory_order_acquire) == searchNodeAge)
    return;
  //Cycle! Just consider this node "done" and return.
  if(nodeBuf.find(node) != nodeBuf.end())
    return;

  //Recurse on all children
  SearchNodeChildrenReference children = node->getChildren();
  int numChildren = children.iterateAndCountChildren();

  if(numChildren > 0) {
    size_t randBufStart = randBuf.size();
    maybeAppendShuffledIntRange(numChildren, rand, randBuf);

    nodeBuf.insert(node);
    for(int i = 0; i<numChildren; i++) {
      int childIdx = rand != NULL ? randBuf[randBufStart+i] : i;
      applyRecursivelyPostOrderMulithreadedHelper(children[childIdx].getIfAllocated(), threadIdx, rand, nodeBuf, randBuf, f);
    }
    randBuf.resize(randBufStart);
    nodeBuf.erase(node);
  }

  //Now call postorder function, protected by lock
  std::lock_guard<std::mutex> lock(mutexPool->getMutex(node->mutexIdx));
  //Make sure another node didn't get there first.
  if(node->nodeAge.load(std::memory_order_acquire) == searchNodeAge)
    return;
  if(f != NULL)
    (*f)(node,threadIdx);
  node->nodeAge.store(searchNodeAge,std::memory_order_release);
}


//Walk over all nodes and their children recursively and call f. No order guarantee, but does guarantee that f is called only once per node.
//Assumes that only other instances of this function are running - in particular, the tree
//is not being mutated by something else. It's okay if f mutates nodes.
//As a side effect, nodeAge == searchNodeAge will be true only for the nodes walked over.
void Search::applyRecursivelyAnyOrderMulithreaded(const vector<SearchNode*>& nodes, std::function<void(SearchNode*,int)>* f) {
  //We invalidate all node ages so we can use them as a marker for what's done.
  searchNodeAge += 1;

  //Simple cheap RNGs so that we can get the different threads into different parts of the tree and not clash.
  int numAdditionalThreads = numAdditionalThreadsToUseForTasks();
  std::vector<PCG32*> rands(numAdditionalThreads+1, NULL);
  for(int threadIdx = 1; threadIdx<numAdditionalThreads+1; threadIdx++)
    rands[threadIdx] = new PCG32(nonSearchRand.nextUInt64());

  int numChildren = (int)nodes.size();
  std::function<void(int)> g = [&](int threadIdx) {
    assert(threadIdx >= 0 && threadIdx < rands.size());
    PCG32* rand = rands[threadIdx];
    std::unordered_set<SearchNode*> nodeBuf;
    std::vector<int> randBuf;

    size_t randBufStart = randBuf.size();
    maybeAppendShuffledIntRange(numChildren, rand, randBuf);
    for(int i = 0; i<numChildren; i++) {
      int childIdx = rand != NULL ? randBuf[randBufStart+i] : i;
      applyRecursivelyAnyOrderMulithreadedHelper(nodes[childIdx],threadIdx,rand,nodeBuf,randBuf,f);
    }
    randBuf.resize(randBufStart);
  };
  performTaskWithThreads(&g, 0x3fffFFFF);
  for(int threadIdx = 1; threadIdx<numAdditionalThreads+1; threadIdx++)
    delete rands[threadIdx];
}

void Search::applyRecursivelyAnyOrderMulithreadedHelper(
  SearchNode* node, int threadIdx, PCG32* rand, std::unordered_set<SearchNode*>& nodeBuf, std::vector<int>& randBuf, std::function<void(SearchNode*,int)>* f
) {
  //nodeAge == searchNodeAge means that the node is done.
  if(node->nodeAge.load(std::memory_order_acquire) == searchNodeAge)
    return;
  //Cycle! Just consider this node "done" and return.
  if(nodeBuf.find(node) != nodeBuf.end())
    return;

  //Recurse on all children
  SearchNodeChildrenReference children = node->getChildren();
  int numChildren = children.iterateAndCountChildren();

  if(numChildren > 0) {
    size_t randBufStart = randBuf.size();
    maybeAppendShuffledIntRange(numChildren, rand, randBuf);

    nodeBuf.insert(node);
    for(int i = 0; i<numChildren; i++) {
      int childIdx = rand != NULL ? randBuf[randBufStart+i] : i;
      applyRecursivelyAnyOrderMulithreadedHelper(children[childIdx].getIfAllocated(), threadIdx, rand, nodeBuf, randBuf, f);
    }
    randBuf.resize(randBufStart);
    nodeBuf.erase(node);
  }

  //The thread that is first to update it wins and does the action.
  uint32_t oldAge = node->nodeAge.exchange(searchNodeAge,std::memory_order_acq_rel);
  if(oldAge == searchNodeAge)
    return;
  if(f != NULL)
    (*f)(node,threadIdx);
}

//Mainly for testing
std::vector<SearchNode*> Search::enumerateTreePostOrder() {
  std::atomic<int64_t> sizeCounter(0);
  std::function<void(SearchNode*,int)> f = [&](SearchNode* node, int threadIdx) noexcept {
    (void)node;
    (void)threadIdx;
    sizeCounter.fetch_add(1,std::memory_order_relaxed);
  };
  applyRecursivelyPostOrderMulithreaded({rootNode},&f);

  int64_t size = sizeCounter.load(std::memory_order_relaxed);
  std::vector<SearchNode*> nodes(size,NULL);
  std::atomic<int64_t> indexCounter(0);
  std::function<void(SearchNode*,int)> g = [&](SearchNode* node, int threadIdx) noexcept {
    (void)threadIdx;
    int64_t index = indexCounter.fetch_add(1,std::memory_order_relaxed);
    assert(index >= 0 && index < size);
    nodes[index] = node;
  };
  applyRecursivelyPostOrderMulithreaded({rootNode},&g);
  assert(indexCounter.load(std::memory_order_relaxed) == size);
  return nodes;
}

================
File: cpp/search/searchresults.cpp
================
//-------------------------------------------------------------------------------------
//This file contains various functions for extracting stats and results from the search, choosing a move, etc
//-------------------------------------------------------------------------------------

#include "../search/search.h"

#include <cinttypes>

#include "../program/playutils.h"
#include "../search/searchnode.h"

using namespace std;
using nlohmann::json;

int64_t Search::getRootVisits() const {
  if(rootNode == NULL)
    return 0;
  int64_t n = rootNode->stats.visits.load(std::memory_order_acquire);
  return n;
}

bool Search::getPlaySelectionValues(
  vector<Loc>& locs, vector<double>& playSelectionValues, double scaleMaxToAtLeast
) const {
  if(rootNode == NULL) {
    locs.clear();
    playSelectionValues.clear();
    return false;
  }
  const bool allowDirectPolicyMoves = true;
  return getPlaySelectionValues(*rootNode, locs, playSelectionValues, NULL, scaleMaxToAtLeast, allowDirectPolicyMoves);
}

bool Search::getPlaySelectionValues(
  vector<Loc>& locs, vector<double>& playSelectionValues, vector<double>* retVisitCounts, double scaleMaxToAtLeast
) const {
  if(rootNode == NULL) {
    locs.clear();
    playSelectionValues.clear();
    if(retVisitCounts != NULL)
      retVisitCounts->clear();
    return false;
  }
  const bool allowDirectPolicyMoves = true;
  return getPlaySelectionValues(*rootNode, locs, playSelectionValues, retVisitCounts, scaleMaxToAtLeast, allowDirectPolicyMoves);
}

bool Search::getPlaySelectionValues(
  const SearchNode& node,
  vector<Loc>& locs, vector<double>& playSelectionValues, vector<double>* retVisitCounts, double scaleMaxToAtLeast,
  bool allowDirectPolicyMoves
) const {
  double lcbBuf[NNPos::MAX_NN_POLICY_SIZE];
  double radiusBuf[NNPos::MAX_NN_POLICY_SIZE];
  bool result = getPlaySelectionValues(
    node,locs,playSelectionValues,retVisitCounts,scaleMaxToAtLeast,allowDirectPolicyMoves,
    false,false,lcbBuf,radiusBuf
  );
  return result;
}

bool Search::getPlaySelectionValues(
  const SearchNode& node,
  vector<Loc>& locs, vector<double>& playSelectionValues, vector<double>* retVisitCounts, double scaleMaxToAtLeast,
  bool allowDirectPolicyMoves, bool alwaysComputeLcb, bool neverUseLcb,
  //Note: lcbBuf is signed from the player to move's perspective
  double lcbBuf[NNPos::MAX_NN_POLICY_SIZE], double radiusBuf[NNPos::MAX_NN_POLICY_SIZE]
) const {
  locs.clear();
  playSelectionValues.clear();
  if(retVisitCounts != NULL)
    retVisitCounts->clear();

  const NNOutput* nnOutput = node.getNNOutput();
  const float* policyProbs = nnOutput != NULL ? nnOutput->getPolicyProbsMaybeNoised() : NULL;

  double totalChildWeight = 0.0;
  const bool suppressPass = shouldSuppressPass(&node);

  //Store up basic weights
  ConstSearchNodeChildrenReference children = node.getChildren();
  const int childrenCapacity = children.getCapacity();
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    Loc moveLoc = childPointer.getMoveLocRelaxed();

    int64_t edgeVisits = childPointer.getEdgeVisits();
    double childWeight = child->stats.getChildWeight(edgeVisits);

    locs.push_back(moveLoc);
    totalChildWeight += childWeight;

    // If the move appears to be outright illegal in policy probs, zero out the selection value.
    // Also if we're suppressing passes.
    // We always push a value on to playSelectionValues even if that value is 0,
    // because some callers rely on this to line up with the raw indices in the children array of the node.
    if((suppressPass && moveLoc == Board::PASS_LOC) || policyProbs[getPos(moveLoc)] < 0) {
      playSelectionValues.push_back(0.0);
      if(retVisitCounts != NULL)
        (*retVisitCounts).push_back(0.0);
    }
    else {
      playSelectionValues.push_back((double)childWeight);
      if(retVisitCounts != NULL)
        (*retVisitCounts).push_back((double)edgeVisits);
    }
  }

  int numChildren = (int)playSelectionValues.size();

  //Find the best child before LCB for pruning. Intended to be the most stably explored child.
  //This is the most weighted child, except with a tiny adjustment so that
  //at very low playouts, variable child weights and discretization doesn't do crazy things.
  int nonLCBBestIdx = 0;
  double nonLCBBestChildWeight = -1e30;
  {
    double maxGoodness = -1e30;
    for(int i = 0; i<numChildren; i++) {
      double weight = playSelectionValues[i];
      const SearchChildPointer& childPointer = children[i];
      double edgeVisits = childPointer.getEdgeVisits();
      Loc moveLoc = childPointer.getMoveLocRelaxed();
      double policyProb = policyProbs[getPos(moveLoc)];

      //Small weight on raw policy, and discount one visit's worth of weight since the most recent
      //visit could be overweighted.
      double g = weight * std::max(0.0,edgeVisits-1.0) / std::max(1.0, edgeVisits) + 2.0 * policyProb;
      if(g > maxGoodness) {
        maxGoodness = g;
        nonLCBBestChildWeight = weight;
        nonLCBBestIdx = i;
      }
    }
  }

  //Possibly reduce weight on children that we spend too many visits on in retrospect
  if(&node == rootNode && numChildren > 0) {
    const SearchChildPointer& bestChildPointer = children[nonLCBBestIdx];
    const SearchNode* bestChild = bestChildPointer.getIfAllocated();
    int64_t bestChildEdgeVisits = bestChildPointer.getEdgeVisits();
    Loc bestMoveLoc = bestChildPointer.getMoveLocRelaxed();
    assert(bestChild != NULL);
    const bool isRoot = true;
    const double policyProbMassVisited = 1.0; //doesn't matter, since fpu value computed from it isn't used here
    double parentUtility;
    double parentWeightPerVisit;
    double parentUtilityStdevFactor;
    double fpuValue = getFpuValueForChildrenAssumeVisited(
      node, rootPla, isRoot, policyProbMassVisited,
      parentUtility, parentWeightPerVisit, parentUtilityStdevFactor
    );

    bool isDuringSearch = false;

    double exploreScaling = getExploreScaling(totalChildWeight, parentUtilityStdevFactor);

    assert(nnOutput != NULL);
    const bool countEdgeVisit = true;
    double bestChildExploreSelectionValue = getExploreSelectionValueOfChild(
      node,policyProbs,bestChild,
      bestMoveLoc,
      exploreScaling,
      totalChildWeight,bestChildEdgeVisits,fpuValue,
      parentUtility,parentWeightPerVisit,
      isDuringSearch,false,nonLCBBestChildWeight,
      countEdgeVisit,
      NULL
    );

    for(int i = 0; i<numChildren; i++) {
      const SearchChildPointer& childPointer = children[i];
      const SearchNode* child = childPointer.getIfAllocated();
      Loc moveLoc = childPointer.getMoveLocRelaxed();
      if(suppressPass && moveLoc == Board::PASS_LOC) {
        playSelectionValues[i] = 0;
        continue;
      }
      if(i != nonLCBBestIdx) {
        int64_t edgeVisits = childPointer.getEdgeVisits();
        double reduced = getReducedPlaySelectionWeight(
          node, policyProbs, child,
          moveLoc,
          exploreScaling,
          edgeVisits,
          bestChildExploreSelectionValue
        );
        playSelectionValues[i] = ceil(reduced);
      }
    }
  }

  //Now compute play selection values taking into account LCB
  if(!neverUseLcb && (alwaysComputeLcb || (searchParams.useLcbForSelection && numChildren > 0))) {
    double bestLcb = -1e10;
    int bestLcbIndex = -1;
    for(int i = 0; i<numChildren; i++) {
      const SearchChildPointer& childPointer = children[i];
      const SearchNode* child = childPointer.getIfAllocated();
      int64_t edgeVisits = childPointer.getEdgeVisits();
      Loc moveLoc = childPointer.getMoveLocRelaxed();
      getSelfUtilityLCBAndRadius(node,child,edgeVisits,moveLoc,lcbBuf[i],radiusBuf[i]);
      //Check if this node is eligible to be considered for best LCB
      double weight = playSelectionValues[i];
      if(weight > 0 && weight >= searchParams.minVisitPropForLCB * nonLCBBestChildWeight) {
        if(lcbBuf[i] > bestLcb) {
          bestLcb = lcbBuf[i];
          bestLcbIndex = i;
        }
      }
    }

    if(searchParams.useLcbForSelection && numChildren > 0 && (searchParams.useNonBuggyLcb ? (bestLcbIndex >= 0) : (bestLcbIndex > 0))) {
      //Best LCB move gets a bonus that ensures it is large enough relative to every other child
      double adjustedWeight = playSelectionValues[bestLcbIndex];
      for(int i = 0; i<numChildren; i++) {
        if(i != bestLcbIndex) {
          double excessValue = bestLcb - lcbBuf[i];
          //This move is actually worse lcb than some other move, it's just that the other
          //move failed its checks for having enough minimum weight. So don't actually
          //try to compute how much better this one is than that one, because it's not better.
          if(excessValue < 0)
            continue;

          double radius = radiusBuf[i];
          //How many times wider would the radius have to be before the lcb would be worse?
          //Add adjust the denom so that we cannot possibly gain more than a factor of 5, just as a guard
          double radiusFactor = (radius + excessValue) / (radius + 0.20 * excessValue);

          //That factor, squared, is the number of "weight" more that we should pretend we have, for
          //the purpose of selection, since normally stdev is proportional to 1/weight^2.
          double lbound = radiusFactor * radiusFactor * playSelectionValues[i];
          if(lbound > adjustedWeight)
            adjustedWeight = lbound;
        }
      }
      playSelectionValues[bestLcbIndex] = adjustedWeight;
    }
  }

  auto isOkayRawPolicyMoveAtRoot = [&](Loc moveLoc, double policyProb, bool obeyAllowedRootMove) {
    if(!rootHistory.isLegal(rootBoard,moveLoc,rootPla) || policyProb < 0 || (obeyAllowedRootMove && !isAllowedRootMove(moveLoc)))
      return false;
    const std::vector<int>& avoidMoveUntilByLoc = rootPla == P_BLACK ? avoidMoveUntilByLocBlack : avoidMoveUntilByLocWhite;
    if(avoidMoveUntilByLoc.size() > 0) {
      assert(avoidMoveUntilByLoc.size() >= Board::MAX_ARR_SIZE);
      int untilDepth = avoidMoveUntilByLoc[moveLoc];
      if(untilDepth > 0)
        return false;
    }
    return true;
  };

  //If we have no children, then use the policy net directly. Only for the root, though, if calling this on any subtree
  //then just require that we have children, for implementation simplicity (since it requires that we have a board and a boardhistory too)
  //(and we also use isAllowedRootMove and avoidMoveUntilByLoc)
  if(numChildren == 0) {
    if(nnOutput == NULL || &node != rootNode || !allowDirectPolicyMoves)
      return false;

    bool obeyAllowedRootMove = true;
    while(true) {
      for(int movePos = 0; movePos<policySize; movePos++) {
        Loc moveLoc = NNPos::posToLoc(movePos,rootBoard.x_size,rootBoard.y_size,nnXLen,nnYLen);
        double policyProb = policyProbs[movePos];
        if(!isOkayRawPolicyMoveAtRoot(moveLoc,policyProb,obeyAllowedRootMove))
          continue;
        if(suppressPass && moveLoc == Board::PASS_LOC)
          policyProb = 0.0;
        locs.push_back(moveLoc);
        playSelectionValues.push_back(policyProb);
        numChildren++;
      }
      //Still no children? Then at this point just ignore isAllowedRootMove.
      if(numChildren == 0 && obeyAllowedRootMove) {
        obeyAllowedRootMove = false;
        continue;
      }
      break;
    }
  }

  //Might happen absurdly rarely if we both have no children and don't properly have an nnOutput
  //but have a hash collision or something so we "found" an nnOutput anyways.
  //Could also happen if we have avoidMoveUntilByLoc pruning all the allowed moves.
  if(numChildren == 0)
    return false;

  double maxValue = 0.0;
  for(int i = 0; i<numChildren; i++) {
    if(playSelectionValues[i] > maxValue)
      maxValue = playSelectionValues[i];
  }

  if(maxValue <= 1e-50) {
    //If we reached this point we have nonzero many children but the children are all weightless.
    //In that case, at least set each one to be weighted by its policy.
    for(int i = 0; i<numChildren; i++) {
      playSelectionValues[i] = std::max(0.0,(double)policyProbs[getPos(locs[i])]);
    }
    //Recompute max
    for(int i = 0; i<numChildren; i++) {
      if(playSelectionValues[i] > maxValue)
        maxValue = playSelectionValues[i];
    }
    if(maxValue <= 1e-50) {
      return false;
    }
  }

  //Sanity check - if somehow we had more than this, something must have overflowed or gone wrong
  assert(maxValue < 1e40);

  double amountToSubtract = std::min(searchParams.chosenMoveSubtract, maxValue/64.0);
  double amountToPrune = std::min(searchParams.chosenMovePrune, maxValue/64.0);
  for(int i = 0; i<numChildren; i++) {
    if(playSelectionValues[i] < amountToPrune)
      playSelectionValues[i] = 0.0;
    else {
      playSelectionValues[i] -= amountToSubtract;
      if(playSelectionValues[i] <= 0.0)
        playSelectionValues[i] = 0.0;
    }
  }

  // Average in human policy
  if(humanEvaluator != NULL &&
     (searchParams.humanSLProfile.initialized || !humanEvaluator->requiresSGFMetadata()) &&
     searchParams.humanSLChosenMoveProp > 0.0
  ) {
    const NNOutput* humanOutput = node.getHumanOutput();
    const float* humanProbs = humanOutput != NULL ? humanOutput->getPolicyProbsMaybeNoised() : NULL;
    if(humanProbs != NULL) {
      // First, take a pass to just fill out all the legal/allowed moves into the play selection values, if allowed, and if at root.
      if(&node == rootNode && allowDirectPolicyMoves) {
        std::set<Loc> locsSet(locs.begin(),locs.end());
        for(int movePos = 0; movePos<policySize; movePos++) {
          Loc moveLoc = NNPos::posToLoc(movePos,rootBoard.x_size,rootBoard.y_size,nnXLen,nnYLen);
          double humanProb = humanProbs[movePos];
          const bool obeyAllowedRootMove = true;
          if(!isOkayRawPolicyMoveAtRoot(moveLoc,humanProb,obeyAllowedRootMove))
            continue;
          if(contains(locsSet,moveLoc))
            continue;
          locs.push_back(moveLoc);
          locsSet.insert(moveLoc);
          playSelectionValues.push_back(0.0); // Pushing zeros since we're just filling in
          numChildren++;
        }
      }

      // Grab utility on the moves we have utilities for.
      std::map<Loc,double> shiftedPolicy;
      std::map<Loc,double> selfUtilities;
      double selfUtilityMax = -1e10;
      double selfUtilitySum = 0.0;
      for(int i = 0; i<childrenCapacity; i++) {
        const SearchChildPointer& childPointer = children[i];
        const SearchNode* child = childPointer.getIfAllocated();
        if(child == NULL)
          break;
        Loc moveLoc = childPointer.getMoveLocRelaxed();
        double humanProb = humanProbs[getPos(moveLoc)];
        if((suppressPass && moveLoc == Board::PASS_LOC) || humanProb < 0)
          humanProb = 0.0;

        shiftedPolicy[moveLoc] = humanProb;
        selfUtilities[moveLoc] = (rootPla == P_WHITE ? 1 : -1) * child->stats.utilityAvg.load(std::memory_order_acquire);
        selfUtilityMax = std::max(selfUtilityMax, selfUtilities[moveLoc]);
        selfUtilitySum += selfUtilities[moveLoc];
      }
      // Straight linear average. Use this to complete the remaining utilities, i.e. for fpu
      double selfUtilityAvg = selfUtilitySum / std::max((size_t)1, selfUtilities.size());
      selfUtilityMax = std::max(selfUtilityMax, selfUtilityAvg); // In case of 0 size
      for(Loc loc: locs) {
        if(!contains(shiftedPolicy,loc)) {
          double humanProb = humanProbs[getPos(loc)];
          if((suppressPass && loc == Board::PASS_LOC) || humanProb < 0)
            humanProb = 0.0;
          shiftedPolicy[loc] = humanProb;
          selfUtilities[loc] = selfUtilityAvg;
        }
      }
      // Perform shift
      for(Loc loc: locs)
        shiftedPolicy[loc] *= exp((selfUtilities[loc] - selfUtilityMax)/searchParams.humanSLChosenMovePiklLambda);

      double shiftedPolicySum = 0.0;
      for(Loc loc: locs)
        shiftedPolicySum += shiftedPolicy[loc];

      // Renormalize and average in to current play selection values, scaling up to the current sum scale of playSelectionValues.
      if(shiftedPolicySum > 0.0) {
        for(Loc loc: locs)
          shiftedPolicy[loc] /= shiftedPolicySum;

        double playSelectionValueSum = 0.0;
        double playSelectionValueNonPassSum = 0.0;
        for(int i = 0; i<numChildren; i++) {
          playSelectionValueSum += playSelectionValues[i];
          if(locs[i] != Board::PASS_LOC)
            playSelectionValueNonPassSum += playSelectionValues[i];
        }

        if(searchParams.humanSLChosenMoveIgnorePass) {
          double shiftedPolicyNonPassSum = 0.0;
          for(Loc loc: locs) {
            if(loc != Board::PASS_LOC)
              shiftedPolicyNonPassSum += shiftedPolicy[loc];
          }
          if(shiftedPolicyNonPassSum > 0.0) {
            for(Loc loc: locs) {
              if(loc != Board::PASS_LOC)
                shiftedPolicy[loc] = shiftedPolicy[loc] / shiftedPolicyNonPassSum * playSelectionValueNonPassSum / playSelectionValueSum;
              else
                shiftedPolicy[loc] = (playSelectionValueSum - playSelectionValueNonPassSum) / playSelectionValueSum;
            }
          }
        }

        for(int i = 0; i<numChildren; i++) {
          playSelectionValues[i] += searchParams.humanSLChosenMoveProp * (playSelectionValueSum * shiftedPolicy[locs[i]] - playSelectionValues[i]);
        }
      }
    }
  }

  maxValue = 0.0;
  for(int i = 0; i<numChildren; i++) {
    if(playSelectionValues[i] > maxValue)
      maxValue = playSelectionValues[i];
  }
  assert(maxValue > 0.0);
  if(maxValue < scaleMaxToAtLeast) {
    for(int i = 0; i<numChildren; i++) {
      playSelectionValues[i] *= scaleMaxToAtLeast / maxValue;
    }
  }

  return true;
}


bool Search::getRootValues(ReportedSearchValues& values) const {
  return getNodeValues(rootNode,values);
}

ReportedSearchValues Search::getRootValuesRequireSuccess() const {
  ReportedSearchValues values;
  if(rootNode == NULL)
    throw StringError("Bug? Bot search root was null");
  bool success = getNodeValues(rootNode,values);
  if(!success)
    throw StringError("Bug? Bot search returned no root values");
  return values;
}

bool Search::getRootRawNNValues(ReportedSearchValues& values) const {
  if(rootNode == NULL)
    return false;
  return getNodeRawNNValues(*rootNode,values);
}

ReportedSearchValues Search::getRootRawNNValuesRequireSuccess() const {
  ReportedSearchValues values;
  if(rootNode == NULL)
    throw StringError("Bug? Bot search root was null");
  bool success = getNodeRawNNValues(*rootNode,values);
  if(!success)
    throw StringError("Bug? Bot search returned no root values");
  return values;
}

bool Search::getNodeRawNNValues(const SearchNode& node, ReportedSearchValues& values) const {
  const NNOutput* nnOutput = node.getNNOutput();
  if(nnOutput == NULL)
    return false;

  values.winValue = nnOutput->whiteWinProb;
  values.lossValue = nnOutput->whiteLossProb;
  values.noResultValue = nnOutput->whiteNoResultProb;

  double scoreMean = nnOutput->whiteScoreMean;
  double scoreMeanSq = nnOutput->whiteScoreMeanSq;
  double scoreStdev = ScoreValue::getScoreStdev(scoreMean,scoreMeanSq);
  double sqrtBoardArea = rootBoard.sqrtBoardArea();
  values.staticScoreValue = ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,0.0,2.0, sqrtBoardArea);
  values.dynamicScoreValue = ScoreValue::expectedWhiteScoreValue(scoreMean,scoreStdev,recentScoreCenter,searchParams.dynamicScoreCenterScale, sqrtBoardArea);
  values.expectedScore = scoreMean;
  values.expectedScoreStdev = scoreStdev;
  values.lead = nnOutput->whiteLead;

  //Sanity check
  assert(values.winValue >= 0.0);
  assert(values.lossValue >= 0.0);
  assert(values.noResultValue >= 0.0);
  assert(values.winValue + values.lossValue + values.noResultValue < 1.001);

  double winLossValue = values.winValue - values.lossValue;
  if(winLossValue > 1.0) winLossValue = 1.0;
  if(winLossValue < -1.0) winLossValue = -1.0;
  values.winLossValue = winLossValue;

  values.weight = computeWeightFromNNOutput(nnOutput);
  values.visits = 1;

  return true;
}


bool Search::getNodeValues(const SearchNode* node, ReportedSearchValues& values) const {
  if(node == NULL)
    return false;
  int64_t visits = node->stats.visits.load(std::memory_order_acquire);
  double weightSum = node->stats.weightSum.load(std::memory_order_acquire);
  double winLossValueAvg = node->stats.winLossValueAvg.load(std::memory_order_acquire);
  double noResultValueAvg = node->stats.noResultValueAvg.load(std::memory_order_acquire);
  double scoreMeanAvg = node->stats.scoreMeanAvg.load(std::memory_order_acquire);
  double scoreMeanSqAvg = node->stats.scoreMeanSqAvg.load(std::memory_order_acquire);
  double leadAvg = node->stats.leadAvg.load(std::memory_order_acquire);
  double utilityAvg = node->stats.utilityAvg.load(std::memory_order_acquire);

  if(weightSum <= 0.0)
    return false;
  assert(visits >= 0);
  if(node == rootNode) {
    //For terminal nodes, we may have no nnoutput and yet we have legitimate visits and terminal evals.
    //But for the root, the root is never treated as a terminal node and always gets an nneval, so if
    //it has visits and weight, it has an nnoutput unless something has gone wrong.
    const NNOutput* nnOutput = node->getNNOutput();
    assert(nnOutput != NULL);
    (void)nnOutput;
  }

  values = ReportedSearchValues(
    *this,
    winLossValueAvg,
    noResultValueAvg,
    scoreMeanAvg,
    scoreMeanSqAvg,
    leadAvg,
    utilityAvg,
    weightSum,
    visits
  );
  return true;
}

const SearchNode* Search::getRootNode() const {
  return rootNode;
}
const SearchNode* Search::getChildForMove(const SearchNode* node, Loc moveLoc) const {
  if(node == NULL)
    return NULL;
  ConstSearchNodeChildrenReference children = node->getChildren();
  int childrenCapacity = children.getCapacity();
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    Loc childMoveLoc = childPointer.getMoveLocRelaxed();
    if(moveLoc == childMoveLoc)
      return child;
  }
  return NULL;
}

Loc Search::getChosenMoveLoc() {
  if(rootNode == NULL)
    return Board::NULL_LOC;

  vector<Loc> locs;
  vector<double> playSelectionValues;
  bool suc = getPlaySelectionValues(locs,playSelectionValues,0.0);
  if(!suc)
    return Board::NULL_LOC;

  assert(locs.size() == playSelectionValues.size());

  double temperature = interpolateEarly(
    searchParams.chosenMoveTemperatureHalflife, searchParams.chosenMoveTemperatureEarly, searchParams.chosenMoveTemperature
  );

  uint32_t idxChosen = chooseIndexWithTemperature(
    nonSearchRand,
    playSelectionValues.data(),
    (int)playSelectionValues.size(),
    temperature,
    searchParams.chosenMoveTemperatureOnlyBelowProb,
    NULL
  );
  return locs[idxChosen];
}


bool Search::getPolicy(float policyProbs[NNPos::MAX_NN_POLICY_SIZE]) const {
  return getPolicy(rootNode, policyProbs);
}
bool Search::getPolicy(const SearchNode* node, float policyProbs[NNPos::MAX_NN_POLICY_SIZE]) const {
  if(node == NULL)
    return false;
  const NNOutput* nnOutput = node->getNNOutput();
  if(nnOutput == NULL)
    return false;

  std::copy(nnOutput->policyProbs, nnOutput->policyProbs+NNPos::MAX_NN_POLICY_SIZE, policyProbs);
  return true;
}


//Safe to call concurrently with search
double Search::getPolicySurprise() const {
  double surprise = 0.0;
  double searchEntropy = 0.0;
  double policyEntropy = 0.0;
  if(getPolicySurpriseAndEntropy(surprise,searchEntropy,policyEntropy))
    return surprise;
  return 0.0;
}

bool Search::getPolicySurpriseAndEntropy(double& surpriseRet, double& searchEntropyRet, double& policyEntropyRet) const {
  return getPolicySurpriseAndEntropy(surpriseRet, searchEntropyRet, policyEntropyRet, rootNode);
}

//Safe to call concurrently with search
bool Search::getPolicySurpriseAndEntropy(double& surpriseRet, double& searchEntropyRet, double& policyEntropyRet, const SearchNode* node) const {
  if(node == NULL)
    return false;
  const NNOutput* nnOutput = node->getNNOutput();
  if(nnOutput == NULL)
    return false;

  vector<Loc> locs;
  vector<double> playSelectionValues;
  const bool allowDirectPolicyMoves = true;
  const bool alwaysComputeLcb = false;
  double lcbBuf[NNPos::MAX_NN_POLICY_SIZE];
  double radiusBuf[NNPos::MAX_NN_POLICY_SIZE];
  bool suc = getPlaySelectionValues(
    *node,locs,playSelectionValues,NULL,1.0,allowDirectPolicyMoves,alwaysComputeLcb,false,lcbBuf,radiusBuf
  );
  if(!suc)
    return false;

  float policyProbsFromNNBuf[NNPos::MAX_NN_POLICY_SIZE];
  {
    const float* policyProbsFromNN = nnOutput->getPolicyProbsMaybeNoised();
    std::copy(policyProbsFromNN, policyProbsFromNN+NNPos::MAX_NN_POLICY_SIZE, policyProbsFromNNBuf);
  }

  double sumPlaySelectionValues = 0.0;
  for(size_t i = 0; i < playSelectionValues.size(); i++)
    sumPlaySelectionValues += playSelectionValues[i];

  double surprise = 0.0;
  double searchEntropy = 0.0;
  for(size_t i = 0; i < playSelectionValues.size(); i++) {
    int pos = getPos(locs[i]);
    double policy = std::max((double)policyProbsFromNNBuf[pos],1e-100);
    double target = playSelectionValues[i] / sumPlaySelectionValues;
    if(target > 1e-100) {
      double logTarget = log(target);
      double logPolicy = log(policy);
      surprise += target * (logTarget - logPolicy);
      searchEntropy += -target * logTarget;
    }
  }

  double policyEntropy = 0.0;
  for(int pos = 0; pos<NNPos::MAX_NN_POLICY_SIZE; pos++) {
    double policy = policyProbsFromNNBuf[pos];
    if(policy > 1e-100) {
      policyEntropy += -policy * log(policy);
    }
  }

  //Just in case, guard against float imprecision
  if(surprise < 0.0)
    surprise = 0.0;
  if(searchEntropy < 0.0)
    searchEntropy = 0.0;
  if(policyEntropy < 0.0)
    policyEntropy = 0.0;

  surpriseRet = surprise;
  searchEntropyRet = searchEntropy;
  policyEntropyRet = policyEntropy;

  return true;
}

void Search::printRootOwnershipMap(ostream& out, Player perspective) const {
  if(rootNode == NULL)
    return;
  const NNOutput* nnOutput = rootNode->getNNOutput();
  if(nnOutput == NULL)
    return;
  if(nnOutput->whiteOwnerMap == NULL)
    return;

  Player perspectiveToUse = (perspective != P_BLACK && perspective != P_WHITE) ? rootPla : perspective;
  double perspectiveFactor = perspectiveToUse == P_BLACK ? -1.0 : 1.0;

  for(int y = 0; y<rootBoard.y_size; y++) {
    for(int x = 0; x<rootBoard.x_size; x++) {
      int pos = NNPos::xyToPos(x,y,nnOutput->nnXLen);
      out << Global::strprintf("%6.1f ", perspectiveFactor * nnOutput->whiteOwnerMap[pos]*100);
    }
    out << endl;
  }
  out << endl;
}

void Search::printRootPolicyMap(ostream& out) const {
  if(rootNode == NULL)
    return;
  const NNOutput* nnOutput = rootNode->getNNOutput();
  if(nnOutput == NULL)
    return;

  const float* policyProbs = nnOutput->getPolicyProbsMaybeNoised();
  for(int y = 0; y<rootBoard.y_size; y++) {
    for(int x = 0; x<rootBoard.x_size; x++) {
      int pos = NNPos::xyToPos(x,y,nnOutput->nnXLen);
      out << Global::strprintf("%6.1f ", policyProbs[pos]*100);
    }
    out << endl;
  }
  out << endl;
}

void Search::printRootEndingScoreValueBonus(ostream& out) const {
  if(rootNode == NULL)
    return;
  const NNOutput* nnOutput = rootNode->getNNOutput();
  if(nnOutput == NULL)
    return;
  if(nnOutput->whiteOwnerMap == NULL)
    return;

  ConstSearchNodeChildrenReference children = rootNode->getChildren();
  int childrenCapacity = children.getCapacity();
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchNode* child = children[i].getIfAllocated();
    if(child == NULL)
      break;

    int64_t edgeVisits = children[i].getEdgeVisits();
    Loc moveLoc = children[i].getMoveLocRelaxed();
    int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
    double scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
    double scoreMeanSqAvg = child->stats.scoreMeanSqAvg.load(std::memory_order_acquire);
    double utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);

    double utilityNoBonus = utilityAvg;
    double endingScoreBonus = getEndingWhiteScoreBonus(*rootNode,moveLoc);
    double utilityDiff = getScoreUtilityDiff(scoreMeanAvg, scoreMeanSqAvg, endingScoreBonus);
    double utilityWithBonus = utilityNoBonus + utilityDiff;

    out << Location::toString(moveLoc,rootBoard) << " " << Global::strprintf(
      "visits %d edgeVisits %d utilityNoBonus %.2fc utilityWithBonus %.2fc endingScoreBonus %.2f",
      childVisits, edgeVisits, utilityNoBonus*100, utilityWithBonus*100, endingScoreBonus
    );
    out << endl;
  }
}

void Search::appendPV(
  vector<Loc>& buf,
  vector<int64_t>& visitsBuf,
  vector<int64_t>& edgeVisitsBuf,
  vector<Loc>& scratchLocs,
  vector<double>& scratchValues,
  const SearchNode* node,
  int maxDepth
) const {
  appendPVForMove(buf,visitsBuf,edgeVisitsBuf,scratchLocs,scratchValues,node,Board::NULL_LOC,maxDepth);
}

void Search::appendPVForMove(
  vector<Loc>& buf,
  vector<int64_t>& visitsBuf,
  vector<int64_t>& edgeVisitsBuf,
  vector<Loc>& scratchLocs,
  vector<double>& scratchValues,
  const SearchNode* node,
  Loc move,
  int maxDepth
) const {
  if(node == NULL)
    return;

  for(int depth = 0; depth < maxDepth; depth++) {
    const bool allowDirectPolicyMoves = true;
    bool success = getPlaySelectionValues(*node, scratchLocs, scratchValues, NULL, 1.0, allowDirectPolicyMoves);
    if(!success)
      return;

    double maxSelectionValue = POLICY_ILLEGAL_SELECTION_VALUE;
    int bestChildIdx = -1;
    Loc bestChildMoveLoc = Board::NULL_LOC;

    for(int i = 0; i<scratchValues.size(); i++) {
      Loc moveLoc = scratchLocs[i];
      double selectionValue = scratchValues[i];

      if(depth == 0 && moveLoc == move) {
        maxSelectionValue = selectionValue;
        bestChildIdx = i;
        bestChildMoveLoc = moveLoc;
        break;
      }

      if(selectionValue > maxSelectionValue) {
        maxSelectionValue = selectionValue;
        bestChildIdx = i;
        bestChildMoveLoc = moveLoc;
      }
    }

    if(bestChildIdx < 0 || bestChildMoveLoc == Board::NULL_LOC)
      return;
    if(depth == 0 && move != Board::NULL_LOC && bestChildMoveLoc != move)
      return;

    ConstSearchNodeChildrenReference children = node->getChildren();
    int childrenCapacity = children.getCapacity();
    //Direct policy move
    if(bestChildIdx >= childrenCapacity) {
      buf.push_back(bestChildMoveLoc);
      visitsBuf.push_back(0);
      edgeVisitsBuf.push_back(0);
      return;
    }
    const SearchNode* child = children[bestChildIdx].getIfAllocated();
    //Direct policy move
    if(child == NULL) {
      buf.push_back(bestChildMoveLoc);
      visitsBuf.push_back(0);
      edgeVisitsBuf.push_back(0);
      return;
    }

    node = child;

    int64_t visits = node->stats.visits.load(std::memory_order_acquire);
    int64_t edgeVisits = children[bestChildIdx].getEdgeVisits();

    buf.push_back(bestChildMoveLoc);
    visitsBuf.push_back(visits);
    edgeVisitsBuf.push_back(edgeVisits);
  }
}


void Search::printPV(ostream& out, const SearchNode* n, int maxDepth) const {
  vector<Loc> buf;
  vector<int64_t> visitsBuf;
  vector<int64_t> edgeVisitsBuf;
  vector<Loc> scratchLocs;
  vector<double> scratchValues;
  appendPV(buf,visitsBuf,edgeVisitsBuf,scratchLocs,scratchValues,n,maxDepth);
  printPV(out,buf);
}

void Search::printPV(ostream& out, const vector<Loc>& buf) const {
  bool printedAnything = false;
  for(int i = 0; i<buf.size(); i++) {
    if(printedAnything)
      out << " ";
    if(buf[i] == Board::NULL_LOC)
      continue;
    out << Location::toString(buf[i],rootBoard);
    printedAnything = true;
  }
}

//Child should NOT be locked.
AnalysisData Search::getAnalysisDataOfSingleChild(
  const SearchNode* child, int64_t edgeVisits, vector<Loc>& scratchLocs, vector<double>& scratchValues,
  Loc move, double policyProb, double fpuValue, double parentUtility, double parentWinLossValue,
  double parentScoreMean, double parentScoreStdev, double parentLead, int maxPVDepth
) const {
  int64_t childVisits = 0;
  double winLossValueAvg = 0.0;
  double noResultValueAvg = 0.0;
  double scoreMeanAvg = 0.0;
  double scoreMeanSqAvg = 0.0;
  double leadAvg = 0.0;
  double utilityAvg = 0.0;
  double utilitySqAvg = 0.0;
  double weightSum = 0.0;
  double weightSqSum = 0.0;
  double childWeightSum = 0.0;

  if(child != NULL) {
    childVisits = child->stats.visits.load(std::memory_order_acquire);
    winLossValueAvg = child->stats.winLossValueAvg.load(std::memory_order_acquire);
    noResultValueAvg = child->stats.noResultValueAvg.load(std::memory_order_acquire);
    scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
    scoreMeanSqAvg = child->stats.scoreMeanSqAvg.load(std::memory_order_acquire);
    leadAvg = child->stats.leadAvg.load(std::memory_order_acquire);
    utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);
    utilitySqAvg = child->stats.utilitySqAvg.load(std::memory_order_acquire);
    weightSum = child->stats.getChildWeight(edgeVisits,childVisits);
    weightSqSum = child->stats.getChildWeightSq(edgeVisits,childVisits);
    childWeightSum = child->stats.weightSum.load(std::memory_order_acquire);
  }

  AnalysisData data;
  data.move = move;
  data.numVisits = edgeVisits;
  if(childVisits <= 0 || childWeightSum <= 1e-30) {
    data.utility = fpuValue;
    data.scoreUtility = getScoreUtility(parentScoreMean,parentScoreMean*parentScoreMean+parentScoreStdev*parentScoreStdev);
    data.resultUtility = fpuValue - data.scoreUtility;
    data.winLossValue = searchParams.winLossUtilityFactor == 1.0 ? parentWinLossValue + (fpuValue - parentUtility) : 0.0;
    // Make sure winloss values due to FPU don't go out of bounds for purposes of reporting to UI
    if(data.winLossValue < -1.0)
      data.winLossValue = -1.0;
    if(data.winLossValue > 1.0)
      data.winLossValue = 1.0;
    data.scoreMean = parentScoreMean;
    data.scoreStdev = parentScoreStdev;
    data.lead = parentLead;
    data.ess = 0.0;
    data.weightSum = 0.0;
    data.weightSqSum = 0.0;
    data.utilitySqAvg = data.utility * data.utility;
    data.scoreMeanSqAvg = parentScoreMean * parentScoreMean + parentScoreStdev * parentScoreStdev;
    data.childVisits = childVisits;
    data.childWeightSum = childWeightSum;
  }
  else {
    data.utility = utilityAvg;
    data.resultUtility = getResultUtility(winLossValueAvg, noResultValueAvg);
    data.scoreUtility = getScoreUtility(scoreMeanAvg, scoreMeanSqAvg);
    data.winLossValue = winLossValueAvg;
    data.scoreMean = scoreMeanAvg;
    data.scoreStdev = ScoreValue::getScoreStdev(scoreMeanAvg,scoreMeanSqAvg);
    data.lead = leadAvg;
    data.ess = weightSum * weightSum / std::max(1e-8,weightSqSum);
    data.weightSum = weightSum;
    data.weightSqSum = weightSqSum;
    data.utilitySqAvg = utilitySqAvg;
    data.scoreMeanSqAvg = scoreMeanSqAvg;
    data.childVisits = childVisits;
    data.childWeightSum = childWeightSum;
  }

  data.policyPrior = policyProb;
  data.order = 0;

  data.pv.clear();
  data.pv.push_back(move);
  data.pvVisits.clear();
  data.pvVisits.push_back(childVisits);
  data.pvEdgeVisits.clear();
  data.pvEdgeVisits.push_back(edgeVisits);
  appendPV(data.pv, data.pvVisits, data.pvEdgeVisits, scratchLocs, scratchValues, child, maxPVDepth);

  data.node = child;

  return data;
}

void Search::getAnalysisData(
  vector<AnalysisData>& buf,int minMovesToTryToGet, bool includeWeightFactors, int maxPVDepth, bool duplicateForSymmetries
) const {
  buf.clear();
  if(rootNode == NULL)
    return;
  getAnalysisData(*rootNode, buf, minMovesToTryToGet, includeWeightFactors, maxPVDepth, duplicateForSymmetries);
}

void Search::getAnalysisData(
  const SearchNode& node, vector<AnalysisData>& buf, int minMovesToTryToGet, bool includeWeightFactors, int maxPVDepth, bool duplicateForSymmetries
) const {
  buf.clear();
  vector<const SearchNode*> children;
  vector<int64_t> childrenEdgeVisits;
  vector<Loc> childrenMoveLocs;
  children.reserve(rootBoard.x_size * rootBoard.y_size + 1);
  childrenEdgeVisits.reserve(rootBoard.x_size * rootBoard.y_size + 1);
  childrenMoveLocs.reserve(rootBoard.x_size * rootBoard.y_size + 1);

  int numChildren;
  vector<Loc> scratchLocs;
  vector<double> scratchValues;
  double lcbBuf[NNPos::MAX_NN_POLICY_SIZE];
  double radiusBuf[NNPos::MAX_NN_POLICY_SIZE];
  float policyProbs[NNPos::MAX_NN_POLICY_SIZE];
  {
    ConstSearchNodeChildrenReference childrenArr = node.getChildren();
    int childrenCapacity = childrenArr.getCapacity();
    for(int i = 0; i<childrenCapacity; i++) {
      const SearchChildPointer& childPointer = childrenArr[i];
      const SearchNode* child = childPointer.getIfAllocated();
      if(child == NULL)
        break;
      children.push_back(child);
      childrenEdgeVisits.push_back(childPointer.getEdgeVisits());
      childrenMoveLocs.push_back(childPointer.getMoveLocRelaxed());
    }
    numChildren = (int)children.size();

    if(numChildren <= 0)
      return;
    assert(numChildren <= NNPos::MAX_NN_POLICY_SIZE);

    const bool alwaysComputeLcb = true;
    bool gotPlaySelectionValues = getPlaySelectionValues(node, scratchLocs, scratchValues, NULL, 1.0, false, alwaysComputeLcb, false, lcbBuf, radiusBuf);

    // No play selection values - then fill with values consistent with all 0 visits.
    // We want it to be possible to get analysis data even when all visits are weightless.
    if(!gotPlaySelectionValues) {
      for(int i = 0; i<numChildren; i++) {
        scratchLocs.push_back(childrenMoveLocs[i]);
        scratchValues.push_back(0.0);
      }
      double lcbBufValue;
      double radiusBufValue;
      getSelfUtilityLCBAndRadiusZeroVisits(lcbBufValue,radiusBufValue);
      std::fill(lcbBuf,lcbBuf+numChildren,lcbBufValue);
      std::fill(radiusBuf,radiusBuf+numChildren,radiusBufValue);
    }

    const NNOutput* nnOutput = node.getNNOutput();
    const float* policyProbsFromNN = nnOutput->getPolicyProbsMaybeNoised();
    for(int i = 0; i<NNPos::MAX_NN_POLICY_SIZE; i++)
      policyProbs[i] = policyProbsFromNN[i];
  }

  //Copy to make sure we keep these values so we can reuse scratch later for PV
  vector<double> playSelectionValues = scratchValues;

  double policyProbMassVisited = 0.0;
  {
    for(int i = 0; i<numChildren; i++) {
      policyProbMassVisited += std::max(0.0, (double)policyProbs[getPos(childrenMoveLocs[i])]);
    }
    //Probability mass should not sum to more than 1, giving a generous allowance
    //for floating point error.
    assert(policyProbMassVisited <= 1.0001);
  }

  double parentWinLossValue;
  double parentScoreMean;
  double parentScoreStdev;
  double parentLead;
  {
    double weightSum = node.stats.weightSum.load(std::memory_order_acquire);
    double winLossValueAvg = node.stats.winLossValueAvg.load(std::memory_order_acquire);
    double scoreMeanAvg = node.stats.scoreMeanAvg.load(std::memory_order_acquire);
    double scoreMeanSqAvg = node.stats.scoreMeanSqAvg.load(std::memory_order_acquire);
    double leadAvg = node.stats.leadAvg.load(std::memory_order_acquire);
    assert(weightSum > 0.0);

    parentWinLossValue = winLossValueAvg;
    parentScoreMean = scoreMeanAvg;
    parentScoreStdev = ScoreValue::getScoreStdev(parentScoreMean,scoreMeanSqAvg);
    parentLead = leadAvg;
  }

  double parentUtility;
  double parentWeightPerVisit;
  double parentUtilityStdevFactor;
  double fpuValue = getFpuValueForChildrenAssumeVisited(
    node, node.nextPla, true, policyProbMassVisited,
    parentUtility, parentWeightPerVisit, parentUtilityStdevFactor
  );

  vector<MoreNodeStats> statsBuf(numChildren);
  for(int i = 0; i<numChildren; i++) {
    const SearchNode* child = children[i];
    int64_t edgeVisits = childrenEdgeVisits[i];
    Loc moveLoc = childrenMoveLocs[i];
    double policyProb = policyProbs[getPos(moveLoc)];
    AnalysisData data = getAnalysisDataOfSingleChild(
      child, edgeVisits, scratchLocs, scratchValues, moveLoc, policyProb, fpuValue, parentUtility, parentWinLossValue,
      parentScoreMean, parentScoreStdev, parentLead, maxPVDepth
    );
    data.playSelectionValue = playSelectionValues[i];
    //Make sure data.lcb is from white's perspective, for consistency with everything else
    //In lcbBuf, it's from self perspective, unlike values at nodes.
    data.lcb = node.nextPla == P_BLACK ? -lcbBuf[i] : lcbBuf[i];
    data.radius = radiusBuf[i];
    buf.push_back(data);

    if(includeWeightFactors) {
      MoreNodeStats& stats = statsBuf[i];
      stats.stats = NodeStats(child->stats);
      stats.selfUtility = node.nextPla == P_WHITE ? data.utility : -data.utility;
      stats.weightAdjusted = stats.stats.getChildWeight(edgeVisits);
      stats.prevMoveLoc = moveLoc;
    }
  }

  //Find all children and compute weighting of the children based on their values
  if(includeWeightFactors) {
    double totalChildWeight = 0.0;
    for(int i = 0; i<numChildren; i++) {
      totalChildWeight += statsBuf[i].weightAdjusted;
    }
    if(searchParams.useNoisePruning) {
      double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE];
      for(int i = 0; i<numChildren; i++)
        policyProbsBuf[i] = std::max(1e-30, (double)policyProbs[getPos(statsBuf[i].prevMoveLoc)]);
      totalChildWeight = pruneNoiseWeight(statsBuf, numChildren, totalChildWeight, policyProbsBuf);
    }
    double amountToSubtract = 0.0;
    double amountToPrune = 0.0;
    downweightBadChildrenAndNormalizeWeight(
      numChildren, totalChildWeight, totalChildWeight,
      amountToSubtract, amountToPrune, statsBuf
    );
    for(int i = 0; i<numChildren; i++)
      buf[i].weightFactor = statsBuf[i].weightAdjusted;
  }

  //Fill the rest of the moves directly from policy
  if(numChildren < minMovesToTryToGet) {
    //A bit inefficient, but no big deal
    for(int i = 0; i<minMovesToTryToGet - numChildren; i++) {
      int bestPos = -1;
      double bestPolicy = -1.0;
      for(int pos = 0; pos<NNPos::MAX_NN_POLICY_SIZE; pos++) {
        if(policyProbs[pos] < bestPolicy)
          continue;

        bool alreadyUsed = false;
        for(int j = 0; j<buf.size(); j++) {
          if(getPos(buf[j].move) == pos) {
            alreadyUsed = true;
            break;
          }
        }
        if(alreadyUsed)
          continue;

        bestPos = pos;
        bestPolicy = policyProbs[pos];
      }
      if(bestPos < 0 || bestPolicy < 0.0)
        break;

      Loc bestMove = NNPos::posToLoc(bestPos,rootBoard.x_size,rootBoard.y_size,nnXLen,nnYLen);
      AnalysisData data = getAnalysisDataOfSingleChild(
        NULL, 0, scratchLocs, scratchValues, bestMove, bestPolicy, fpuValue, parentUtility, parentWinLossValue,
        parentScoreMean, parentScoreStdev, parentLead, maxPVDepth
      );
      buf.push_back(data);
    }
  }
  std::stable_sort(buf.begin(),buf.end());

  if(duplicateForSymmetries && searchParams.rootSymmetryPruning && rootSymmetries.size() > 1) {
    vector<AnalysisData> newBuf;
    std::set<Loc> isDone;
    for(int i = 0; i<buf.size(); i++) {
      const AnalysisData& data = buf[i];
      for(int symmetry : rootSymmetries) {
        Loc symMove = SymmetryHelpers::getSymLoc(data.move, rootBoard, symmetry);
        if(contains(isDone,symMove))
          continue;
        const std::vector<int>& avoidMoveUntilByLoc = rootPla == P_BLACK ? avoidMoveUntilByLocBlack : avoidMoveUntilByLocWhite;
        if(avoidMoveUntilByLoc.size() > 0 && avoidMoveUntilByLoc[symMove] > 0)
          continue;

        isDone.insert(symMove);
        newBuf.push_back(data);
        //Replace the fields that need to be adjusted for symmetry
        AnalysisData& newData = newBuf.back();
        newData.move = symMove;
        if(symmetry != 0)
          newData.isSymmetryOf = data.move;
        newData.symmetry = symmetry;
        for(int j = 0; j<newData.pv.size(); j++)
          newData.pv[j] = SymmetryHelpers::getSymLoc(newData.pv[j], rootBoard, symmetry);
      }
    }
    buf = std::move(newBuf);
  }

  for(int i = 0; i<buf.size(); i++)
    buf[i].order = i;
}

void Search::printPVForMove(ostream& out, const SearchNode* n, Loc move, int maxDepth) const {
  vector<Loc> buf;
  vector<int64_t> visitsBuf;
  vector<int64_t> edgeVisitsBuf;
  vector<Loc> scratchLocs;
  vector<double> scratchValues;
  appendPVForMove(buf,visitsBuf,edgeVisitsBuf,scratchLocs,scratchValues,n,move,maxDepth);
  for(int i = 0; i<buf.size(); i++) {
    if(i > 0)
      out << " ";
    out << Location::toString(buf[i],rootBoard);
  }
}

void Search::printTree(ostream& out, const SearchNode* node, PrintTreeOptions options, Player perspective) const {
  if(node == NULL)
    return;
  string prefix;
  AnalysisData data;
  {
    vector<Loc> scratchLocs;
    vector<double> scratchValues;
    //Use dummy values for parent
    double policyProb = NAN;
    double fpuValue = 0;
    double parentUtility = 0;
    double parentWinLossValue = 0;
    double parentScoreMean = 0;
    double parentScoreStdev = 0;
    double parentLead = 0;
    //Since we don't have an edge from another parent we are following, we just use the visits on the node itself as the edge visits.
    int64_t edgeVisits = node->stats.visits.load(std::memory_order_acquire);
    data = getAnalysisDataOfSingleChild(
      node, edgeVisits, scratchLocs, scratchValues,
      Board::NULL_LOC, policyProb, fpuValue, parentUtility, parentWinLossValue,
      parentScoreMean, parentScoreStdev, parentLead, options.maxPVDepth_
    );
    data.weightFactor = NAN;
  }
  perspective = (perspective != P_BLACK && perspective != P_WHITE) ? node->nextPla : perspective;
  printTreeHelper(out, node, options, prefix, 0, 0, data, perspective);
}

void Search::printTreeHelper(
  ostream& out, const SearchNode* n, const PrintTreeOptions& options,
  string& prefix, int64_t origVisits, int depth, const AnalysisData& data, Player perspective
) const {
  if(n == NULL)
    return;

  const SearchNode& node = *n;

  Player perspectiveToUse = (perspective != P_BLACK && perspective != P_WHITE) ? n->nextPla : perspective;
  double perspectiveFactor = perspectiveToUse == P_BLACK ? -1.0 : 1.0;

  if(depth == 0)
    origVisits = data.numVisits;

  //Output for this node
  {
    out << prefix;
    char buf[128];

    out << ": ";

    if(data.childVisits > 0) {
      sprintf(buf,"T %6.2fc ",(perspectiveFactor * data.utility * 100.0));
      out << buf;
      sprintf(buf,"W %6.2fc ",(perspectiveFactor * data.resultUtility * 100.0));
      out << buf;
      sprintf(buf,"S %6.2fc (%+5.1f L %+5.1f) ",
              perspectiveFactor * data.scoreUtility * 100.0,
              perspectiveFactor * data.scoreMean,
              perspectiveFactor * data.lead
      );
      out << buf;
    }

    // bool hasNNValue = false;
    // double nnResultValue;
    // double nnTotalValue;
    // lock.lock();
    // if(node.nnOutput != nullptr) {
    //   nnResultValue = getResultUtilityFromNN(*node.nnOutput);
    //   nnTotalValue = getUtilityFromNN(*node.nnOutput);
    //   hasNNValue = true;
    // }
    // lock.unlock();

    // if(hasNNValue) {
    //   sprintf(buf,"VW %6.2fc VS %6.2fc ", nnResultValue * 100.0, (nnTotalValue - nnResultValue) * 100.0);
    //   out << buf;
    // }
    // else {
    //   sprintf(buf,"VW ---.--c VS ---.--c ");
    //   out << buf;
    // }

    if(depth > 0 && !isnan(data.lcb)) {
      sprintf(buf,"LCB %7.2fc ", perspectiveFactor * data.lcb * 100.0);
      out << buf;
    }

    if(!isnan(data.policyPrior)) {
      sprintf(buf,"P %5.2f%% ", data.policyPrior * 100.0);
      out << buf;
    }
    if(!isnan(data.weightFactor)) {
      sprintf(buf,"WF %5.1f ", data.weightFactor);
      out << buf;
    }
    if(data.playSelectionValue >= 0 && depth > 0) {
      sprintf(buf,"PSV %7.0f ", data.playSelectionValue);
      out << buf;
    }

    if(options.printSqs_) {
      sprintf(buf,"SMSQ %5.1f USQ %7.5f W %6.2f WSQ %8.2f ", data.scoreMeanSqAvg, data.utilitySqAvg, data.weightSum, data.weightSqSum);
      out << buf;
    }

    if(options.printAvgShorttermError_) {
      std::pair<double,double> wlAndScoreError = getShallowAverageShorttermWLAndScoreError(&node);
      sprintf(buf,"STWL %6.2fc STS %5.1f ", wlAndScoreError.first * 100.0, wlAndScoreError.second);
      out << buf;
    }

    // Using child visits here instead of edge visits because edge visits is at least
    // semi-reflected in WF and PSV.
    sprintf(buf,"N %7" PRIu64 "  --  ", data.childVisits);
    out << buf;

    printPV(out, data.pv);
    out << endl;
  }

  if(depth >= options.branch_.size()) {
    if(depth >= options.maxDepth_ + options.branch_.size())
      return;
    if(data.numVisits < options.minVisitsToExpand_)
      return;
    if((double)data.numVisits < origVisits * options.minVisitsPropToExpand_)
      return;
  }
  if((options.alsoBranch_ && depth == 0) || (!options.alsoBranch_ && depth == options.branch_.size())) {
    out << "---" << PlayerIO::playerToString(node.nextPla) << "(" << (node.nextPla == perspectiveToUse ? "^" : "v") << ")---" << endl;
  }

  vector<AnalysisData> analysisData;
  bool duplicateForSymmetries = false;
  getAnalysisData(node,analysisData,0,true,options.maxPVDepth_,duplicateForSymmetries);

  int numChildren = (int)analysisData.size();

  //Apply filtering conditions, but include children that don't match the filtering condition
  //but where there are children afterward that do, in case we ever use something more complex
  //than plain visits as a filter criterion. Do this by finding the last child that we want as the threshold.
  int lastIdxWithEnoughVisits = numChildren-1;
  while(true) {
    if(lastIdxWithEnoughVisits <= 0)
      break;

    int64_t childVisits = analysisData[lastIdxWithEnoughVisits].numVisits;
    bool hasEnoughVisits = childVisits >= options.minVisitsToShow_
      && (double)childVisits >= origVisits * options.minVisitsPropToShow_;
    if(hasEnoughVisits)
      break;
    lastIdxWithEnoughVisits--;
  }

  int numChildrenToRecurseOn = numChildren;
  if(options.maxChildrenToShow_ < numChildrenToRecurseOn)
    numChildrenToRecurseOn = options.maxChildrenToShow_;
  if(lastIdxWithEnoughVisits+1 < numChildrenToRecurseOn)
    numChildrenToRecurseOn = lastIdxWithEnoughVisits+1;


  for(int i = 0; i<numChildren; i++) {
    const SearchNode* child = analysisData[i].node;
    Loc moveLoc = analysisData[i].move;

    if((depth >= options.branch_.size() && i < numChildrenToRecurseOn) ||
       (depth < options.branch_.size() && moveLoc == options.branch_[depth]) ||
       (depth < options.branch_.size() && options.alsoBranch_ && i < numChildrenToRecurseOn)
    ) {
      size_t oldLen = prefix.length();
      string locStr = Location::toString(moveLoc,rootBoard);
      if(locStr == "pass")
        prefix += "pss";
      else
        prefix += locStr;
      prefix += " ";
      while(prefix.length() < oldLen+4)
        prefix += " ";
      int nextDepth = depth+1;
      if(depth < options.branch_.size() && moveLoc != options.branch_[depth])
        nextDepth = (int)options.branch_.size() + 1;
      printTreeHelper(out,child,options,prefix,origVisits,nextDepth,analysisData[i], perspective);
      prefix.erase(oldLen);
    }
  }
}


std::pair<double,double> Search::getShallowAverageShorttermWLAndScoreError(const SearchNode* node) const {
  if(node == NULL)
    node = rootNode;
  if(node == NULL)
    return std::make_pair(0.0,0.0);
  if(!nnEvaluator->supportsShorttermError())
    return std::make_pair(-1.0,-1.0);
  std::unordered_set<const SearchNode*> graphPath;
  double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE];
  double wlError = 0.0;
  double scoreError = 0.0;
  // Stop deepening when we hit a node whose proportion in the final average would be less than this.
  // Sublinear in visits so that the cost of this grows more slowly than overall search depth.
  int64_t visits = node->stats.visits.load(std::memory_order_acquire);
  double minProp = 0.25 / pow(std::max(1.0,(double)visits),0.625);
  double desiredProp = 1.0;
  getShallowAverageShorttermWLAndScoreErrorHelper(
    node,
    graphPath,
    policyProbsBuf,
    minProp,
    desiredProp,
    wlError,
    scoreError
  );
  return std::make_pair(wlError,scoreError);
}

void Search::getShallowAverageShorttermWLAndScoreErrorHelper(
  const SearchNode* node,
  std::unordered_set<const SearchNode*>& graphPath,
  double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE],
  double minProp,
  double desiredProp,
  double& wlError,
  double& scoreError
) const {
  const NNOutput* nnOutput = node->getNNOutput();
  if(nnOutput == NULL) {
    // Accumulate nothing. This will be correct for terminal nodes, which have no uncertainty.
    // Not quite correct for multithreading, but no big deal, this value isn't used for anything critical
    // and currently isn't called while multithreaded. Yay code debt.
    return;
  }

  if(desiredProp < minProp) {
    // We don't track the average errors on nodes, so just use the error of this node's raw nn output.
    wlError += desiredProp * nnOutput->shorttermWinlossError;
    scoreError += desiredProp * nnOutput->shorttermScoreError;
    return;
  }

  std::pair<std::unordered_set<const SearchNode*>::iterator,bool> result = graphPath.insert(node);
  // No insertion, node was already there, this means we hit a cycle in the graph
  if(!result.second) {
    //Just treat it as base case and immediately terminate.
    wlError += desiredProp * nnOutput->shorttermWinlossError;
    scoreError += desiredProp * nnOutput->shorttermScoreError;
    return;
  }

  ConstSearchNodeChildrenReference children = node->getChildren();
  int childrenCapacity = children.getCapacity();

  vector<MoreNodeStats> statsBuf;
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    int64_t edgeVisits = childPointer.getEdgeVisits();
    Loc moveLoc = childPointer.getMoveLocRelaxed();
    MoreNodeStats stats;
    stats.stats = NodeStats(child->stats);
    stats.selfUtility = node->nextPla == P_WHITE ? stats.stats.utilityAvg : -stats.stats.utilityAvg;
    stats.weightAdjusted = stats.stats.getChildWeight(edgeVisits);
    stats.prevMoveLoc = moveLoc;
    statsBuf.push_back(stats);
  }
  int numChildren = (int)statsBuf.size();

  // Find all children and compute weighting of the children based on their values
  {
    double totalChildWeight = 0.0;
    for(int i = 0; i<numChildren; i++) {
      totalChildWeight += statsBuf[i].weightAdjusted;
    }
    const float* policyProbs = nnOutput->getPolicyProbsMaybeNoised();
    if(searchParams.useNoisePruning) {
      for(int i = 0; i<numChildren; i++)
        policyProbsBuf[i] = std::max(1e-30, (double)policyProbs[getPos(statsBuf[i].prevMoveLoc)]);
      totalChildWeight = pruneNoiseWeight(statsBuf, numChildren, totalChildWeight, policyProbsBuf);
    }
    double amountToSubtract = 0.0;
    double amountToPrune = 0.0;
    downweightBadChildrenAndNormalizeWeight(
      numChildren, totalChildWeight, totalChildWeight,
      amountToSubtract, amountToPrune, statsBuf
    );
  }

  //What we actually weight the children by for averaging
  double relativeChildrenWeightSum = 0.0;
  //What the weights of the children sum to from the search.
  double childrenWeightSum = 0;
  for(int i = 0; i<numChildren; i++) {
    double childWeight = statsBuf[i].weightAdjusted;
    relativeChildrenWeightSum += childWeight;
    childrenWeightSum += childWeight;
  }
  double parentNNWeight = computeWeightFromNNOutput(nnOutput);
  parentNNWeight = std::max(parentNNWeight,1e-10);
  double desiredPropFromChildren = desiredProp * childrenWeightSum / (childrenWeightSum + parentNNWeight);
  double selfProp = desiredProp * parentNNWeight / (childrenWeightSum + parentNNWeight);

  // In multithreading we may sometimes have children but with no weight at all yet, in that case just use parent alone.
  if(desiredPropFromChildren <= 0.0 || relativeChildrenWeightSum <= 0.0) {
    selfProp += desiredPropFromChildren;
  }
  else {

    for(int i = 0; i<numChildren; i++) {
      const SearchNode* child = children[i].getIfAllocated();
      assert(child != NULL);
      double childWeight = statsBuf[i].weightAdjusted;
      double desiredPropFromChild = childWeight / relativeChildrenWeightSum * desiredPropFromChildren;
      getShallowAverageShorttermWLAndScoreErrorHelper(child,graphPath,policyProbsBuf,minProp,desiredPropFromChild,wlError,scoreError);
    }
  }

  graphPath.erase(node);

  // Also add in the direct evaluation of this node.
  {
    wlError += selfProp * nnOutput->shorttermWinlossError;
    scoreError += selfProp * nnOutput->shorttermScoreError;
  }
}

bool Search::getSharpScore(const SearchNode* node, double& ret) const {
  if(node == NULL)
    node = rootNode;
  if(node == NULL)
    return false;

  int64_t visits = node->stats.visits.load(std::memory_order_acquire);
  // Stop deepening when we hit a node whose proportion in the final average would be less than this.
  // Sublinear in visits so that the cost of this grows more slowly than overall search depth.
  double minProp = 0.25 / pow(std::max(1.0,(double)visits),0.5);
  double desiredProp = 1.0;

  // Store initial value so we can start accumulating
  ret = 0.0;

  std::unordered_set<const SearchNode*> graphPath;

  double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE];
  if(node != rootNode) {
    return getSharpScoreHelper(node,graphPath,policyProbsBuf,minProp,desiredProp,ret);
  }

  const NNOutput* nnOutput = node->getNNOutput();
  if(nnOutput == NULL)
    return false;

  vector<double> playSelectionValues;
  vector<Loc> locs; // not used
  const bool allowDirectPolicyMoves = false;
  const bool alwaysComputeLcb = false;
  const bool neverUseLcb = true;
  bool suc = getPlaySelectionValues(*node,locs,playSelectionValues,NULL,1.0,allowDirectPolicyMoves,alwaysComputeLcb,neverUseLcb,NULL,NULL);
  // If there are no children, or otherwise values could not be computed, then fall back to the normal case
  if(!suc) {
    ReportedSearchValues values;
    if(getNodeValues(node,values)) {
      ret = values.expectedScore;
      return true;
    }
    return false;
  }

  int numChildren = (int)playSelectionValues.size();

  ConstSearchNodeChildrenReference children = node->getChildren();

  //What we actually weight the children by for averaging sharp score, sharper than the plain weight.
  double relativeChildrenWeightSum = 0.0;
  //What the weights of the children sum to from the search.
  double childrenWeightSum = 0;
  for(int i = 0; i<numChildren; i++) {
    double childWeight = playSelectionValues[i];
    relativeChildrenWeightSum += childWeight * childWeight * childWeight;
    childrenWeightSum += childWeight;
  }
  double parentNNWeight = computeWeightFromNNOutput(nnOutput);
  parentNNWeight = std::max(parentNNWeight,1e-10);
  double desiredPropFromChildren = desiredProp * childrenWeightSum / (childrenWeightSum + parentNNWeight);
  double selfProp = desiredProp * parentNNWeight / (childrenWeightSum + parentNNWeight);

  // In multithreading we may sometimes have children but with no weight at all yet, in that case just use parent alone.
  if(desiredPropFromChildren <= 0.0 || relativeChildrenWeightSum <= 0.0) {
    selfProp += desiredPropFromChildren;
  }
  else {

    graphPath.insert(node);

    for(int i = 0; i<numChildren; i++) {
      const SearchNode* child = children[i].getIfAllocated();
      assert(child != NULL);
      double childWeight = playSelectionValues[i];
      double desiredPropFromChild = childWeight * childWeight * childWeight / relativeChildrenWeightSum * desiredPropFromChildren;
      bool accumulated = getSharpScoreHelper(child,graphPath,policyProbsBuf,minProp,desiredPropFromChild,ret);
      if(!accumulated)
        selfProp += desiredPropFromChild;
    }

    graphPath.erase(node);

  }

  // Also add in the direct evaluation of this node.
  {
    double scoreMean = (double)nnOutput->whiteScoreMean;
    // cout << "Accumulating " << scoreMean << " " << selfProp << endl;
    ret += scoreMean * selfProp;
  }
  return true;
}


bool Search::getSharpScoreHelper(
  const SearchNode* node,
  std::unordered_set<const SearchNode*>& graphPath,
  double policyProbsBuf[NNPos::MAX_NN_POLICY_SIZE],
  double minProp,
  double desiredProp,
  double& ret
) const {
  const NNOutput* nnOutput = node->getNNOutput();
  if(nnOutput == NULL || desiredProp < minProp) {
    NodeStats stats = NodeStats(node->stats);
    if(stats.visits <= 0)
      return false;
    // cout << "Accumulating " << stats.scoreMeanAvg << " " << desiredProp << endl;
    ret += stats.scoreMeanAvg * desiredProp;
    return true;
  }

  ConstSearchNodeChildrenReference children = node->getChildren();
  int childrenCapacity = children.getCapacity();

  if(childrenCapacity <= 0) {
    double scoreMean = (double)nnOutput->whiteScoreMean;
    // cout << "Accumulating " << scoreMean << " " << desiredProp << endl;
    ret += scoreMean * desiredProp;
    return true;
  }

  std::pair<std::unordered_set<const SearchNode*>::iterator,bool> result = graphPath.insert(node);
  // No insertion, node was already there, this means we hit a cycle in the graph
  if(!result.second) {
    // Just treat it as base case and immediately terminate.
    double scoreMean = (double)nnOutput->whiteScoreMean;
    // cout << "Accumulating " << scoreMean << " " << desiredProp << endl;
    ret += scoreMean * desiredProp;
    return true;
  }

  vector<MoreNodeStats> statsBuf;
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    int64_t edgeVisits = childPointer.getEdgeVisits();
    Loc moveLoc = childPointer.getMoveLocRelaxed();
    MoreNodeStats stats;
    stats.stats = NodeStats(child->stats);
    stats.selfUtility = node->nextPla == P_WHITE ? stats.stats.utilityAvg : -stats.stats.utilityAvg;
    stats.weightAdjusted = stats.stats.getChildWeight(edgeVisits);
    stats.prevMoveLoc = moveLoc;
    statsBuf.push_back(stats);
  }
  int numChildren = (int)statsBuf.size();

  //Find all children and compute weighting of the children based on their values
  {
    double totalChildWeight = 0.0;
    for(int i = 0; i<numChildren; i++) {
      totalChildWeight += statsBuf[i].weightAdjusted;
    }
    const float* policyProbs = nnOutput->getPolicyProbsMaybeNoised();
    if(searchParams.useNoisePruning) {
      for(int i = 0; i<numChildren; i++)
        policyProbsBuf[i] = std::max(1e-30, (double)policyProbs[getPos(statsBuf[i].prevMoveLoc)]);
      totalChildWeight = pruneNoiseWeight(statsBuf, numChildren, totalChildWeight, policyProbsBuf);
    }
    double amountToSubtract = 0.0;
    double amountToPrune = 0.0;
    downweightBadChildrenAndNormalizeWeight(
      numChildren, totalChildWeight, totalChildWeight,
      amountToSubtract, amountToPrune, statsBuf
    );
  }

  // What we actually weight the children by for averaging sharp score, sharper than the plain weight.
  double relativeChildrenWeightSum = 0.0;
  // What the weights of the children sum to from the search.
  double childrenWeightSum = 0;
  for(int i = 0; i<numChildren; i++) {
    if(statsBuf[i].stats.visits <= 0)
      continue;
    double childWeight = statsBuf[i].weightAdjusted;
    relativeChildrenWeightSum += childWeight * childWeight * childWeight;
    childrenWeightSum += childWeight;
  }
  double parentNNWeight = computeWeightFromNNOutput(nnOutput);
  parentNNWeight = std::max(parentNNWeight,1e-10);
  double desiredPropFromChildren = desiredProp * childrenWeightSum / (childrenWeightSum + parentNNWeight);
  double selfProp = desiredProp * parentNNWeight / (childrenWeightSum + parentNNWeight);

  // In multithreading we may sometimes have children but with no weight at all yet, in that case just use parent alone.
  if(desiredPropFromChildren <= 0.0 || relativeChildrenWeightSum <= 0.0) {
    selfProp += desiredPropFromChildren;
  }
  else {

    for(int i = 0; i<numChildren; i++) {
      const SearchNode* child = children[i].getIfAllocated();
      assert(child != NULL);
      double childWeight = statsBuf[i].weightAdjusted;
      double desiredPropFromChild = childWeight * childWeight * childWeight / relativeChildrenWeightSum * desiredPropFromChildren;
      bool accumulated = getSharpScoreHelper(child,graphPath,policyProbsBuf,minProp,desiredPropFromChild,ret);
      if(!accumulated)
        selfProp += desiredPropFromChild;
    }
  }

  graphPath.erase(node);

  // Also add in the direct evaluation of this node.
  {
    double scoreMean = (double)nnOutput->whiteScoreMean;
    // cout << "Accumulating " << scoreMean << " " << selfProp << endl;
    ret += scoreMean * selfProp;
  }
  return true;
}

vector<double> Search::getAverageTreeOwnership(const SearchNode* node) const {
  if(node == NULL)
    node = rootNode;
  if(!alwaysIncludeOwnerMap)
    throw StringError("Called Search::getAverageTreeOwnership when alwaysIncludeOwnerMap is false");
  vector<double> vec(nnXLen*nnYLen,0.0);
  auto accumulate = [&vec,this](float* ownership, double selfProp){
    for (int pos = 0; pos < nnXLen*nnYLen; pos++)
      vec[pos] += selfProp * ownership[pos];
  };
  int64_t visits = node->stats.visits.load(std::memory_order_acquire);
  //Stop deepening when we hit a node whose proportion in the final average would be less than this.
  //Sublinear in visits so that the cost of this grows more slowly than overall search depth.
  double minProp = 0.5 / pow(std::max(1.0,(double)visits),0.75);
  //Entirely drop a node with weight less than this
  double pruneProp = minProp * 0.01;
  std::unordered_set<const SearchNode*> graphPath;
  traverseTreeForOwnership(minProp,pruneProp,1.0,node,graphPath,accumulate);
  return vec;
}

std::pair<vector<double>,vector<double>> Search::getAverageAndStandardDeviationTreeOwnership(const SearchNode* node) const {
  if(node == NULL)
    node = rootNode;
  vector<double> average(nnXLen*nnYLen,0.0);
  vector<double> stdev(nnXLen*nnYLen,0.0);
  auto accumulate = [&average,&stdev,this](float* ownership, double selfProp) {
    for (int pos = 0; pos < nnXLen*nnYLen; pos++) {
      const double value = ownership[pos];
      average[pos] += selfProp * value;
      stdev[pos] += selfProp * value * value;
    }
  };
  int64_t visits = node->stats.visits.load(std::memory_order_acquire);
  // Stop deepening when we hit a node whose proportion in the final average would be less than this.
  // Sublinear in visits so that the cost of this grows more slowly than overall search depth.
  double minProp = 0.5 / pow(std::max(1.0,(double)visits),0.75);
  // Entirely drop a node with weight less than this
  double pruneProp = minProp * 0.01;
  std::unordered_set<const SearchNode*> graphPath;
  traverseTreeForOwnership(minProp,pruneProp,1.0,node,graphPath,accumulate);
  for(int pos = 0; pos<nnXLen*nnYLen; pos++) {
    const double avg = average[pos];
    stdev[pos] = sqrt(max(stdev[pos] - avg * avg, 0.0));
  }
  return std::make_pair(average, stdev);
}

// Returns true if anything was accumulated, false otherwise.
template<typename Func>
bool Search::traverseTreeForOwnership(
  double minProp,
  double pruneProp,
  double desiredProp,
  const SearchNode* node,
  std::unordered_set<const SearchNode*>& graphPath,
  Func& accumulate
) const {
  if(node == NULL)
    return false;

  const NNOutput* nnOutput = node->getNNOutput();
  if(nnOutput == NULL)
    return false;

  // Base case
  if(desiredProp < minProp) {
    float* ownerMap = nnOutput->whiteOwnerMap;
    assert(ownerMap != NULL);
    accumulate(ownerMap, desiredProp);
    return true;
  }

  ConstSearchNodeChildrenReference children = node->getChildren();
  int childrenCapacity = children.getCapacity();

  if(childrenCapacity <= 0) {
    float* ownerMap = nnOutput->whiteOwnerMap;
    assert(ownerMap != NULL);
    accumulate(ownerMap, desiredProp);
    return true;
  }

  std::pair<std::unordered_set<const SearchNode*>::iterator,bool> result = graphPath.insert(node);
  // No insertion, node was already there, this means we hit a cycle in the graph
  if(!result.second) {
    //Just treat it as base case and immediately terminate.
    float* ownerMap = nnOutput->whiteOwnerMap;
    assert(ownerMap != NULL);
    accumulate(ownerMap, desiredProp);
    return true;
  }

  double selfProp;
  double parentNNWeight = computeWeightFromNNOutput(nnOutput);
  if(childrenCapacity <= SearchChildrenSizes::SIZE0TOTAL) {
    double childWeightBuf[SearchChildrenSizes::SIZE0TOTAL];
    selfProp = traverseTreeForOwnershipChildren(
      minProp, pruneProp, desiredProp, parentNNWeight, children, childWeightBuf, childrenCapacity, graphPath, accumulate
    );
  }
  else {
    vector<double> childWeightBuf(childrenCapacity);
    selfProp = traverseTreeForOwnershipChildren(
      minProp, pruneProp, desiredProp, parentNNWeight, children, &childWeightBuf[0], childrenCapacity, graphPath, accumulate
    );
  }

  graphPath.erase(node);

  float* ownerMap = nnOutput->whiteOwnerMap;
  assert(ownerMap != NULL);
  accumulate(ownerMap, selfProp);
  return true;
}

// Returns the prop that the parent node should be weighted.
// Not guaranteed to be <= the parent's weightsum due to multithreading.
template<typename Func>
double Search::traverseTreeForOwnershipChildren(
  double minProp,
  double pruneProp,
  double desiredProp,
  double parentNNWeight,
  ConstSearchNodeChildrenReference children,
  double* childWeightBuf,
  int childrenCapacity,
  std::unordered_set<const SearchNode*>& graphPath,
  Func& accumulate
) const {
  int numChildren = 0;
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    int64_t edgeVisits = childPointer.getEdgeVisits();
    double childWeight = child->stats.getChildWeight(edgeVisits);
    childWeightBuf[i] = childWeight;
    numChildren += 1;
  }

  // What we actually weight the children by for averaging ownership, sharper than the plain weight.
  double relativeChildrenWeightSum = 0.0;
  // What the weights of the children sum to from the search.
  double childrenWeightSum = 0;
  for(int i = 0; i<numChildren; i++) {
    double childWeight = childWeightBuf[i];
    relativeChildrenWeightSum += (double)childWeight * childWeight;
    childrenWeightSum += childWeight;
  }

  // Just in case
  parentNNWeight = std::max(parentNNWeight,1e-10);
  double desiredPropFromChildren = desiredProp * childrenWeightSum / (childrenWeightSum + parentNNWeight);
  double selfProp = desiredProp * parentNNWeight / (childrenWeightSum + parentNNWeight);

  // Recurse
  // In multithreading we may sometimes have children but with no weight at all yet, in that case just use parent alone.
  if(desiredPropFromChildren <= 0.0 || relativeChildrenWeightSum <= 0.0) {
    selfProp += desiredPropFromChildren;
  }
  else {
    for(int i = 0; i<numChildren; i++) {
      double childWeight = childWeightBuf[i];
      const SearchNode* child = children[i].getIfAllocated();
      assert(child != NULL);
      double desiredPropFromChild = (double)childWeight * childWeight / relativeChildrenWeightSum * desiredPropFromChildren;
      if(desiredPropFromChild < pruneProp)
        selfProp += desiredPropFromChild;
      else {
        bool accumulated = traverseTreeForOwnership(minProp,pruneProp,desiredPropFromChild,child,graphPath,accumulate);
        if(!accumulated)
          selfProp += desiredPropFromChild;
      }
    }
  }

  return selfProp;
}

std::vector<double> Search::getAverageTreeOwnership(
  const Player perspective,
  const SearchNode* node,
  int symmetry
) const {
  const vector<double> ownership = getAverageTreeOwnership(node);
  const Board& board = rootBoard;
  vector<double> ownershipToOutput(board.y_size * board.x_size, 0.0);

  for(int y = 0; y < board.y_size; y++) {
    for(int x = 0; x < board.x_size; x++) {
      int pos = NNPos::xyToPos(x, y, nnXLen);
      Loc symLoc = SymmetryHelpers::getSymLoc(x, y, board, symmetry);
      int symPos = Location::getY(symLoc, board.x_size) * board.x_size + Location::getX(symLoc, board.x_size);
      assert(symPos >= 0 && symPos < board.y_size * board.x_size);

      double o;
      if(perspective == P_BLACK || (perspective != P_BLACK && perspective != P_WHITE && rootPla == P_BLACK))
        o = -ownership[pos];
      else
        o = ownership[pos];
      // Round to 10^-6 to limit the size of output.
      // No guarantees that the serializer actually outputs something of this length rather than longer due to float wonkiness, but it should usually be true.
      ownershipToOutput[symPos] = Global::roundStatic(o, 1000000.0);
    }
  }
  return ownershipToOutput;
}

std::pair<std::vector<double>,std::vector<double>> Search::getAverageAndStandardDeviationTreeOwnership(
  const Player perspective,
  const SearchNode* node,
  int symmetry
) const {
  const std::pair<vector<double>,vector<double>> ownershipAverageAndStdev = getAverageAndStandardDeviationTreeOwnership(node);
  const Board& board = rootBoard;
  const vector<double>& ownership = std::get<0>(ownershipAverageAndStdev);
  const vector<double>& ownershipStdev = std::get<1>(ownershipAverageAndStdev);
  vector<double> ownershipToOutput(board.y_size * board.x_size, 0.0);
  vector<double> ownershipStdevToOutput(board.y_size * board.x_size, 0.0);

  for(int y = 0; y < board.y_size; y++) {
    for(int x = 0; x < board.x_size; x++) {
      int pos = NNPos::xyToPos(x, y, nnXLen);
      Loc symLoc = SymmetryHelpers::getSymLoc(x, y, board, symmetry);
      int symPos = Location::getY(symLoc, board.x_size) * board.x_size + Location::getX(symLoc, board.x_size);
      assert(symPos >= 0 && symPos < board.y_size * board.x_size);

      double o;
      if(perspective == P_BLACK || (perspective != P_BLACK && perspective != P_WHITE && rootPla == P_BLACK))
        o = -ownership[pos];
      else
        o = ownership[pos];
      // Round to 10^-6 to limit the size of output.
      // No guarantees that the serializer actually outputs something of this length rather than longer due to float wonkiness, but it should usually be true.
      ownershipToOutput[symPos] = Global::roundStatic(o, 1000000.0);
      ownershipStdevToOutput[symPos] = Global::roundStatic(ownershipStdev[pos], 1000000.0);
    }
  }
  return std::make_pair(ownershipToOutput, ownershipStdevToOutput);
}


bool Search::getAnalysisJson(
  const Player perspective,
  int analysisPVLen,
  bool preventEncore,
  bool includePolicy,
  bool includeOwnership,
  bool includeOwnershipStdev,
  bool includeMovesOwnership,
  bool includeMovesOwnershipStdev,
  bool includePVVisits,
  json& ret
) const {
  vector<AnalysisData> buf;
  static constexpr int minMoves = 0;
  static constexpr int OUTPUT_PRECISION = 8;

  const Board& board = rootBoard;
  const BoardHistory& hist = rootHistory;
  bool duplicateForSymmetries = true;
  getAnalysisData(buf, minMoves, false, analysisPVLen, duplicateForSymmetries);

  const NNOutput* nnOutput = NULL;
  const NNOutput* humanOutput = NULL;
  if(rootNode != NULL) {
    nnOutput = rootNode->getNNOutput();
    humanOutput = rootNode->getHumanOutput();
  }

  // Stats for all the individual moves
  json moveInfos = json::array();
  for(int i = 0; i < buf.size(); i++) {
    const AnalysisData& data = buf[i];
    double winrate = 0.5 * (1.0 + data.winLossValue);
    double utility = data.utility;
    double lcb = PlayUtils::getHackedLCBForWinrate(this, data, rootPla);
    double utilityLcb = data.lcb;
    double scoreMean = data.scoreMean;
    double lead = data.lead;
    if(perspective == P_BLACK || (perspective != P_BLACK && perspective != P_WHITE && rootPla == P_BLACK)) {
      winrate = 1.0 - winrate;
      lcb = 1.0 - lcb;
      utility = -utility;
      scoreMean = -scoreMean;
      lead = -lead;
      utilityLcb = -utilityLcb;
    }

    json moveInfo;
    moveInfo["move"] = Location::toString(data.move, board);
    moveInfo["visits"] = data.childVisits;
    moveInfo["weight"] = Global::roundDynamic(data.childWeightSum,OUTPUT_PRECISION);
    moveInfo["utility"] = Global::roundDynamic(utility,OUTPUT_PRECISION);
    moveInfo["winrate"] = Global::roundDynamic(winrate,OUTPUT_PRECISION);
    // We report lead for scoreMean here so that a bunch of legacy tools that use KataGo use lead instead, which
    // is usually a better field for user applications. We report scoreMean instead as scoreSelfplay
    moveInfo["scoreMean"] = Global::roundDynamic(lead,OUTPUT_PRECISION);
    moveInfo["scoreSelfplay"] = Global::roundDynamic(scoreMean,OUTPUT_PRECISION);
    moveInfo["scoreLead"] = Global::roundDynamic(lead,OUTPUT_PRECISION);
    moveInfo["scoreStdev"] = Global::roundDynamic(data.scoreStdev,OUTPUT_PRECISION);
    moveInfo["prior"] = Global::roundDynamic(data.policyPrior,OUTPUT_PRECISION);
    if(humanOutput != NULL)
      moveInfo["humanPrior"] = Global::roundDynamic(std::max(0.0,(double)humanOutput->policyProbs[getPos(data.move)]),OUTPUT_PRECISION);
    moveInfo["lcb"] = Global::roundDynamic(lcb,OUTPUT_PRECISION);
    moveInfo["utilityLcb"] = Global::roundDynamic(utilityLcb,OUTPUT_PRECISION);
    moveInfo["order"] = data.order;
    if(data.isSymmetryOf != Board::NULL_LOC)
      moveInfo["isSymmetryOf"] = Location::toString(data.isSymmetryOf, board);
    moveInfo["edgeVisits"] = data.numVisits;
    moveInfo["edgeWeight"] = Global::roundDynamic(data.weightSum,OUTPUT_PRECISION);
    moveInfo["playSelectionValue"] = Global::roundDynamic(data.playSelectionValue,OUTPUT_PRECISION);

    json pv = json::array();
    int pvLen =
      (preventEncore && data.pvContainsPass()) ? data.getPVLenUpToPhaseEnd(board, hist, rootPla) : (int)data.pv.size();
    for(int j = 0; j < pvLen; j++)
      pv.push_back(Location::toString(data.pv[j], board));
    moveInfo["pv"] = pv;

    if(includePVVisits) {
      assert(data.pvVisits.size() >= pvLen);
      json pvVisits = json::array();
      for(int j = 0; j < pvLen; j++)
        pvVisits.push_back(data.pvVisits[j]);
      moveInfo["pvVisits"] = pvVisits;

      assert(data.pvEdgeVisits.size() >= pvLen);
      json pvEdgeVisits = json::array();
      for(int j = 0; j < pvLen; j++)
        pvEdgeVisits.push_back(data.pvEdgeVisits[j]);
      moveInfo["pvEdgeVisits"] = pvEdgeVisits;
    }

    if(includeMovesOwnership && includeMovesOwnershipStdev) {
      std::pair<std::vector<double>,std::vector<double>> ownershipAndStdev = getAverageAndStandardDeviationTreeOwnership(perspective, data.node, data.symmetry);
      moveInfo["ownership"] = json(ownershipAndStdev.first);
      moveInfo["ownershipStdev"] = json(ownershipAndStdev.second);
    }
    else if(includeMovesOwnershipStdev) {
      std::pair<std::vector<double>,std::vector<double>> ownershipAndStdev = getAverageAndStandardDeviationTreeOwnership(perspective, data.node, data.symmetry);
      moveInfo["ownershipStdev"] = json(ownershipAndStdev.second);
    }
    else if(includeMovesOwnership) {
      moveInfo["ownership"] = json(getAverageTreeOwnership(perspective, data.node, data.symmetry));
    }

    moveInfos.push_back(moveInfo);
  }
  ret["moveInfos"] = moveInfos;

  // Stats for root position
  {
    ReportedSearchValues rootVals;
    bool suc = getPrunedRootValues(rootVals);
    if(!suc)
      return false;

    double winloss = rootVals.winLossValue;
    double scoreMean = rootVals.expectedScore;
    double lead = rootVals.lead;
    double utility = rootVals.utility;
    double flipFactor = (perspective == P_BLACK || (perspective != P_BLACK && perspective != P_WHITE && rootPla == P_BLACK)) ? -1.0 : 1.0;

    json rootInfo;
    rootInfo["visits"] = rootVals.visits;
    rootInfo["weight"] = rootVals.weight;
    rootInfo["winrate"] = Global::roundDynamic(0.5 + 0.5*winloss*flipFactor,OUTPUT_PRECISION);
    rootInfo["scoreSelfplay"] = Global::roundDynamic(scoreMean*flipFactor,OUTPUT_PRECISION);
    rootInfo["scoreLead"] = Global::roundDynamic(lead*flipFactor,OUTPUT_PRECISION);
    rootInfo["scoreStdev"] = Global::roundDynamic(rootVals.expectedScoreStdev,OUTPUT_PRECISION);
    rootInfo["utility"] = Global::roundDynamic(utility*flipFactor,OUTPUT_PRECISION);

    if(nnOutput != NULL) {
      rootInfo["rawWinrate"] = Global::roundDynamic(0.5 + 0.5*(nnOutput->whiteWinProb - nnOutput->whiteLossProb)*flipFactor,OUTPUT_PRECISION);
      rootInfo["rawLead"] = Global::roundDynamic(nnOutput->whiteLead*flipFactor,OUTPUT_PRECISION);
      rootInfo["rawScoreSelfplay"] = Global::roundDynamic(nnOutput->whiteScoreMean*flipFactor,OUTPUT_PRECISION);
      double wsm = nnOutput->whiteScoreMean;
      rootInfo["rawScoreSelfplayStdev"] = Global::roundDynamic(sqrt(std::max(0.0, nnOutput->whiteScoreMeanSq - wsm*wsm)),OUTPUT_PRECISION);
      rootInfo["rawNoResultProb"] = Global::roundDynamic(nnOutput->whiteNoResultProb,OUTPUT_PRECISION);
      rootInfo["rawStWrError"] = Global::roundDynamic(nnOutput->shorttermWinlossError * 0.5,OUTPUT_PRECISION);
      rootInfo["rawStScoreError"] = Global::roundDynamic(nnOutput->shorttermScoreError,OUTPUT_PRECISION);
      rootInfo["rawVarTimeLeft"] = Global::roundDynamic(nnOutput->varTimeLeft,OUTPUT_PRECISION);
    }
    if(humanOutput != NULL) {
      rootInfo["humanWinrate"] = Global::roundDynamic(0.5 + 0.5*(humanOutput->whiteWinProb - humanOutput->whiteLossProb)*flipFactor,OUTPUT_PRECISION);
      rootInfo["humanScoreMean"] = Global::roundDynamic(humanOutput->whiteScoreMean*flipFactor,OUTPUT_PRECISION);
      double wsm = humanOutput->whiteScoreMean;
      rootInfo["humanScoreStdev"] = Global::roundDynamic(sqrt(std::max(0.0, humanOutput->whiteScoreMeanSq - wsm*wsm)),OUTPUT_PRECISION);
      rootInfo["humanStWrError"] = Global::roundDynamic(humanOutput->shorttermWinlossError * 0.5,OUTPUT_PRECISION);
      rootInfo["humanStScoreError"] = Global::roundDynamic(humanOutput->shorttermScoreError,OUTPUT_PRECISION);
    }

    Hash128 thisHash;
    Hash128 symHash;
    for(int symmetry = 0; symmetry < SymmetryHelpers::NUM_SYMMETRIES; symmetry++) {
      Board symBoard = SymmetryHelpers::getSymBoard(board,symmetry);
      Hash128 hash = symBoard.getSitHashWithSimpleKo(rootPla);
      if(symmetry == 0) {
        thisHash = hash;
        symHash = hash;
      }
      else {
        if(hash < symHash)
          symHash = hash;
      }
    }
    rootInfo["thisHash"] = Global::uint64ToHexString(thisHash.hash1) + Global::uint64ToHexString(thisHash.hash0);
    rootInfo["symHash"] = Global::uint64ToHexString(symHash.hash1) + Global::uint64ToHexString(symHash.hash0);
    rootInfo["currentPlayer"] = PlayerIO::playerToStringShort(rootPla);

    ret["rootInfo"] = rootInfo;
  }

  // Raw policy prior
  if(includePolicy) {
    {
      float policyProbs[NNPos::MAX_NN_POLICY_SIZE];
      bool suc = getPolicy(policyProbs);
      if(!suc)
        return false;
      json policy = json::array();
      for(int y = 0; y < board.y_size; y++) {
        for(int x = 0; x < board.x_size; x++) {
          int pos = NNPos::xyToPos(x, y, nnXLen);
          policy.push_back(Global::roundDynamic(policyProbs[pos],OUTPUT_PRECISION));
        }
      }

      int passPos = NNPos::locToPos(Board::PASS_LOC, board.x_size, nnXLen, nnYLen);
      policy.push_back(Global::roundDynamic(policyProbs[passPos],OUTPUT_PRECISION));
      ret["policy"] = policy;
    }

    if(humanOutput != NULL) {
      const float* policyProbs = humanOutput->getPolicyProbsMaybeNoised();
      json policy = json::array();
      for(int y = 0; y < board.y_size; y++) {
        for(int x = 0; x < board.x_size; x++) {
          int pos = NNPos::xyToPos(x, y, nnXLen);
          policy.push_back(Global::roundDynamic(policyProbs[pos],OUTPUT_PRECISION));
        }
      }
      int passPos = NNPos::locToPos(Board::PASS_LOC, board.x_size, nnXLen, nnYLen);
      policy.push_back(Global::roundDynamic(policyProbs[passPos],OUTPUT_PRECISION));
      ret["humanPolicy"] = policy;
    }
  }

  // Average tree ownership
  if(includeOwnership && includeOwnershipStdev) {
    int symmetry = 0;
    std::pair<std::vector<double>,std::vector<double>> ownershipAndStdev = getAverageAndStandardDeviationTreeOwnership(perspective, rootNode, symmetry);
    ret["ownership"] = json(ownershipAndStdev.first);
    ret["ownershipStdev"] = json(ownershipAndStdev.second);
  }
  else if(includeOwnershipStdev) {
    int symmetry = 0;
    std::pair<std::vector<double>,std::vector<double>> ownershipAndStdev = getAverageAndStandardDeviationTreeOwnership(perspective, rootNode, symmetry);
    ret["ownershipStdev"] = json(ownershipAndStdev.second);
  }
  else if(includeOwnership) {
    int symmetry = 0;
    ret["ownership"] = json(getAverageTreeOwnership(perspective, rootNode, symmetry));
  }

  return true;
}

//Compute all the stats of the node based on its children, pruning weights such that they are as expected
//based on policy and utility. This is used to give accurate rootInfo even with a lot of wide root noise
bool Search::getPrunedRootValues(ReportedSearchValues& values) const {
  return getPrunedNodeValues(rootNode,values);
}

bool Search::getPrunedNodeValues(const SearchNode* nodePtr, ReportedSearchValues& values) const {
  if(nodePtr == NULL)
    return false;
  const SearchNode& node = *nodePtr;

  ConstSearchNodeChildrenReference children = node.getChildren();
  int childrenCapacity = children.getCapacity();

  vector<double> playSelectionValues;
  vector<Loc> locs; // not used
  const bool allowDirectPolicyMoves = false;
  const bool alwaysComputeLcb = false;
  const bool neverUseLcb = true;
  bool suc = getPlaySelectionValues(node,locs,playSelectionValues,NULL,1.0,allowDirectPolicyMoves,alwaysComputeLcb,neverUseLcb,NULL,NULL);
  //If there are no children, or otherwise values could not be computed,
  //then fall back to the normal case and just listen to the values on the node rather than trying
  //to recompute things.
  if(!suc) {
    return getNodeValues(nodePtr,values);
  }

  double winLossValueSum = 0.0;
  double noResultValueSum = 0.0;
  double scoreMeanSum = 0.0;
  double scoreMeanSqSum = 0.0;
  double leadSum = 0.0;
  double utilitySum = 0.0;
  double utilitySqSum = 0.0;
  double weightSum = 0.0;
  double weightSqSum = 0.0;
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    int64_t edgeVisits = childPointer.getEdgeVisits();
    NodeStats stats = NodeStats(child->stats);

    if(stats.visits <= 0 || stats.weightSum <= 0.0 || edgeVisits <= 0)
      continue;
    double weight = playSelectionValues[i];
    double weightScaling = weight / stats.weightSum;
    winLossValueSum += weight * stats.winLossValueAvg;
    noResultValueSum += weight * stats.noResultValueAvg;
    scoreMeanSum += weight * stats.scoreMeanAvg;
    scoreMeanSqSum += weight * stats.scoreMeanSqAvg;
    leadSum += weight * stats.leadAvg;
    utilitySum += weight * stats.utilityAvg;
    utilitySqSum += weight * stats.utilitySqAvg;
    weightSqSum += weightScaling * weightScaling * stats.weightSqSum;
    weightSum += weight;
  }

  //Also add in the direct evaluation of this node.
  {
    const NNOutput* nnOutput = node.getNNOutput();
    //If somehow the nnOutput is still null here, skip
    if(nnOutput == NULL)
      return false;
    double winProb = (double)nnOutput->whiteWinProb;
    double lossProb = (double)nnOutput->whiteLossProb;
    double noResultProb = (double)nnOutput->whiteNoResultProb;
    double scoreMean = (double)nnOutput->whiteScoreMean;
    double scoreMeanSq = (double)nnOutput->whiteScoreMeanSq;
    double lead = (double)nnOutput->whiteLead;
    double utility =
      getResultUtility(winProb-lossProb, noResultProb)
      + getScoreUtility(scoreMean, scoreMeanSq);

    double weight = computeWeightFromNNOutput(nnOutput);
    winLossValueSum += (winProb - lossProb) * weight;
    noResultValueSum += noResultProb * weight;
    scoreMeanSum += scoreMean * weight;
    scoreMeanSqSum += scoreMeanSq * weight;
    leadSum += lead * weight;
    utilitySum += utility * weight;
    utilitySqSum += utility * utility * weight;
    weightSqSum += weight * weight;
    weightSum += weight;
  }
  values = ReportedSearchValues(
    *this,
    winLossValueSum / weightSum,
    noResultValueSum / weightSum,
    scoreMeanSum / weightSum,
    scoreMeanSqSum / weightSum,
    leadSum / weightSum,
    utilitySum / weightSum,
    node.stats.weightSum.load(std::memory_order_acquire),
    node.stats.visits.load(std::memory_order_acquire)
  );
  return true;
}

================
File: cpp/search/search.cpp
================
//-------------------------------------------------------------------------------------
//This file contains the main core logic of the search.
//-------------------------------------------------------------------------------------

#include "../search/search.h"

#include <algorithm>
#include <numeric>

#include "../core/fancymath.h"
#include "../core/timer.h"
#include "../game/graphhash.h"
#include "../search/distributiontable.h"
#include "../search/patternbonustable.h"
#include "../search/searchnode.h"
#include "../search/searchnodetable.h"
#include "../search/subtreevaluebiastable.h"

using namespace std;

//-----------------------------------------------------------------------------------------

static string makeSeed(const Search& search, int threadIdx) {
  stringstream ss;
  ss << search.randSeed;
  ss << "$searchThread$";
  ss << threadIdx;
  ss << "$";
  ss << search.rootBoard.pos_hash;
  ss << "$";
  ss << search.rootHistory.moveHistory.size();
  ss << "$";
  ss << search.numSearchesBegun;
  return ss.str();
}

SearchThread::SearchThread(int tIdx, const Search& search)
  :threadIdx(tIdx),
   pla(search.rootPla),board(search.rootBoard),
   history(search.rootHistory),
   graphHash(search.rootGraphHash),
   graphPath(),
   shouldCountPlayout(false),
   rand(makeSeed(search,tIdx)),
   nnResultBuf(),
   statsBuf(),
   upperBoundVisitsLeft(1e30),
   oldNNOutputsToCleanUp(),
   illegalMoveHashes()
{
  statsBuf.resize(NNPos::MAX_NN_POLICY_SIZE);
  graphPath.reserve(256);

  //Reserving even this many is almost certainly overkill but should guarantee that we never have hit allocation here.
  oldNNOutputsToCleanUp.reserve(8);
}
SearchThread::~SearchThread() {
  for(size_t i = 0; i<oldNNOutputsToCleanUp.size(); i++)
    delete oldNNOutputsToCleanUp[i];
  oldNNOutputsToCleanUp.resize(0);
}

//-----------------------------------------------------------------------------------------

static const double VALUE_WEIGHT_DEGREES_OF_FREEDOM = 3.0;

Search::Search(SearchParams params, NNEvaluator* nnEval, Logger* lg, const string& rSeed)
  :Search(params,nnEval,NULL,lg,rSeed)
{}
Search::Search(SearchParams params, NNEvaluator* nnEval, NNEvaluator* humanEval, Logger* lg, const string& rSeed)
  :rootPla(P_BLACK),
   rootBoard(),
   rootHistory(),
   rootGraphHash(),
   rootHintLoc(Board::NULL_LOC),
   avoidMoveUntilByLocBlack(),avoidMoveUntilByLocWhite(),avoidMoveUntilRescaleRoot(false),
   rootSymmetries(),
   rootPruneOnlySymmetries(),
   rootSafeArea(NULL),
   recentScoreCenter(0.0),
   mirroringPla(C_EMPTY),
   mirrorAdvantage(0.0),
   mirrorCenterSymmetryError(1e10),
   alwaysIncludeOwnerMap(false),
   searchParams(params),numSearchesBegun(0),searchNodeAge(0),
   plaThatSearchIsFor(C_EMPTY),plaThatSearchIsForLastSearch(C_EMPTY),
   lastSearchNumPlayouts(0),
   effectiveSearchTimeCarriedOver(0.0),
   randSeed(rSeed),
   rootKoHashTable(NULL),
   valueWeightDistribution(NULL),
   patternBonusTable(NULL),
   externalPatternBonusTable(nullptr),
   nonSearchRand(rSeed + string("$nonSearchRand")),
   logger(lg),
   nnEvaluator(nnEval),
   humanEvaluator(humanEval),
   nnXLen(),
   nnYLen(),
   policySize(),
   rootNode(NULL),
   nodeTable(NULL),
   mutexPool(NULL),
   subtreeValueBiasTable(NULL),
   numThreadsSpawned(0),
   threads(NULL),
   threadTasks(NULL),
   threadTasksRemaining(NULL),
   oldNNOutputsToCleanUpMutex(),
   oldNNOutputsToCleanUp()
{
  assert(logger != NULL);
  nnXLen = nnEval->getNNXLen();
  nnYLen = nnEval->getNNYLen();
  assert(nnXLen > 0 && nnXLen <= NNPos::MAX_BOARD_LEN);
  assert(nnYLen > 0 && nnYLen <= NNPos::MAX_BOARD_LEN);
  policySize = NNPos::getPolicySize(nnXLen,nnYLen);

  if(humanEvaluator != NULL) {
    if(humanEvaluator->getNNXLen() != nnXLen || humanEvaluator->getNNYLen() != nnYLen)
      throw StringError("Search::init - humanEval has different nnXLen or nnYLen");
  }

  rootKoHashTable = new KoHashTable();

  rootSafeArea = new Color[Board::MAX_ARR_SIZE];

  valueWeightDistribution = new DistributionTable(
    [](double z) { return FancyMath::tdistpdf(z,VALUE_WEIGHT_DEGREES_OF_FREEDOM); },
    [](double z) { return FancyMath::tdistcdf(z,VALUE_WEIGHT_DEGREES_OF_FREEDOM); },
    -50.0,
    50.0,
    2000
  );

  rootNode = NULL;
  nodeTable = new SearchNodeTable(params.nodeTableShardsPowerOfTwo);
  mutexPool = new MutexPool(nodeTable->mutexPool->getNumMutexes());

  rootHistory.clear(rootBoard,rootPla,Rules(),0);
  rootKoHashTable->recompute(rootHistory);
}

Search::~Search() {
  clearSearch();

  delete[] rootSafeArea;
  delete rootKoHashTable;
  delete valueWeightDistribution;

  delete nodeTable;
  delete mutexPool;
  delete subtreeValueBiasTable;
  delete patternBonusTable;
  killThreads();
}

const Board& Search::getRootBoard() const {
  return rootBoard;
}
const BoardHistory& Search::getRootHist() const {
  return rootHistory;
}
Player Search::getRootPla() const {
  return rootPla;
}

Player Search::getPlayoutDoublingAdvantagePla() const {
  return searchParams.playoutDoublingAdvantagePla == C_EMPTY ? plaThatSearchIsFor : searchParams.playoutDoublingAdvantagePla;
}

int Search::getPos(Loc moveLoc) const {
  return NNPos::locToPos(moveLoc,rootBoard.x_size,nnXLen,nnYLen);
}

void Search::setPosition(Player pla, const Board& board, const BoardHistory& history) {
  clearSearch();
  rootPla = pla;
  plaThatSearchIsFor = C_EMPTY;
  rootBoard = board;
  rootHistory = history;
  rootKoHashTable->recompute(rootHistory);
  avoidMoveUntilByLocBlack.clear();
  avoidMoveUntilByLocWhite.clear();
}

void Search::setPlayerAndClearHistory(Player pla) {
  clearSearch();
  rootPla = pla;
  plaThatSearchIsFor = C_EMPTY;
  rootBoard.clearSimpleKoLoc();
  Rules rules = rootHistory.rules;
  //Preserve this value even when we get multiple moves in a row by some player
  bool assumeMultipleStartingBlackMovesAreHandicap = rootHistory.assumeMultipleStartingBlackMovesAreHandicap;
  rootHistory.clear(rootBoard,rootPla,rules,rootHistory.encorePhase);
  rootHistory.setAssumeMultipleStartingBlackMovesAreHandicap(assumeMultipleStartingBlackMovesAreHandicap);

  rootKoHashTable->recompute(rootHistory);

  //If changing the player alone, don't clear these, leave the user's setting - the user may have tried
  //to adjust the player or will be calling runWholeSearchAndGetMove with a different player and will
  //still want avoid moves to apply.
  //avoidMoveUntilByLocBlack.clear();
  //avoidMoveUntilByLocWhite.clear();
}

void Search::setPlayerIfNew(Player pla) {
  if(pla != rootPla)
    setPlayerAndClearHistory(pla);
}

void Search::setKomiIfNew(float newKomi) {
  if(rootHistory.rules.komi != newKomi) {
    clearSearch();
    rootHistory.setKomi(newKomi);
  }
}

void Search::setAvoidMoveUntilByLoc(const std::vector<int>& bVec, const std::vector<int>& wVec) {
  if(avoidMoveUntilByLocBlack == bVec && avoidMoveUntilByLocWhite == wVec)
    return;
  clearSearch();
  avoidMoveUntilByLocBlack = bVec;
  avoidMoveUntilByLocWhite = wVec;
}

void Search::setAvoidMoveUntilRescaleRoot(bool b) {
  avoidMoveUntilRescaleRoot = b;
}

void Search::setRootHintLoc(Loc loc) {
  //When we positively change the hint loc, we clear the search to make absolutely sure
  //that the hintloc takes effect, and that all nnevals (including the root noise that adds the hintloc) has a chance to happen
  if(loc != Board::NULL_LOC && rootHintLoc != loc)
    clearSearch();
  rootHintLoc = loc;
}

void Search::setAlwaysIncludeOwnerMap(bool b) {
  if(!alwaysIncludeOwnerMap && b)
    clearSearch();
  alwaysIncludeOwnerMap = b;
}

void Search::setRootSymmetryPruningOnly(const std::vector<int>& v) {
  if(rootPruneOnlySymmetries == v)
    return;
  clearSearch();
  rootPruneOnlySymmetries = v;
}


void Search::setParams(SearchParams params) {
  clearSearch();
  searchParams = params;
}

void Search::setParamsNoClearing(SearchParams params) {
  searchParams = params;
}

void Search::setExternalPatternBonusTable(std::unique_ptr<PatternBonusTable>&& table) {
  if(table == externalPatternBonusTable)
    return;
  //Probably not actually needed so long as we do a fresh search to refresh and use the new table
  //but this makes behavior consistent with all the other setters.
  clearSearch();
  externalPatternBonusTable = std::move(table);
}

void Search::setCopyOfExternalPatternBonusTable(const std::unique_ptr<PatternBonusTable>& table) {
  setExternalPatternBonusTable(table == nullptr ? nullptr : std::make_unique<PatternBonusTable>(*table));
}

void Search::setNNEval(NNEvaluator* nnEval) {
  clearSearch();
  nnEvaluator = nnEval;
  nnXLen = nnEval->getNNXLen();
  nnYLen = nnEval->getNNYLen();
  assert(nnXLen > 0 && nnXLen <= NNPos::MAX_BOARD_LEN);
  assert(nnYLen > 0 && nnYLen <= NNPos::MAX_BOARD_LEN);
  policySize = NNPos::getPolicySize(nnXLen,nnYLen);

  if(humanEvaluator != NULL) {
    if(humanEvaluator->getNNXLen() != nnXLen || humanEvaluator->getNNYLen() != nnYLen)
      throw StringError("Search::setNNEval - humanEval has different nnXLen or nnYLen");
  }
}

void Search::clearSearch() {
  effectiveSearchTimeCarriedOver = 0.0;
  if(rootNode != NULL) {
    deleteAllTableNodesMulithreaded();
    //Root is not stored in node table
    if(rootNode != NULL) {
      delete rootNode;
      rootNode = NULL;
    }
  }
  clearOldNNOutputs();
  searchNodeAge = 0;
}

bool Search::isLegalTolerant(Loc moveLoc, Player movePla) const {
  return rootHistory.isLegalTolerant(rootBoard,moveLoc,movePla);
}

bool Search::isLegalStrict(Loc moveLoc, Player movePla) const {
  return movePla == rootPla && rootHistory.isLegal(rootBoard,moveLoc,movePla);
}

bool Search::makeMove(Loc moveLoc, Player movePla) {
  return makeMove(moveLoc,movePla,false);
}

bool Search::makeMove(Loc moveLoc, Player movePla, bool preventEncore) {
  if(!isLegalTolerant(moveLoc,movePla))
    return false;

  if(movePla != rootPla)
    setPlayerAndClearHistory(movePla);

  //If the white handicap bonus changes due to the move, we will also need to recompute everything since this is
  //basically like a change to the komi.
  float oldWhiteHandicapBonusScore = rootHistory.whiteHandicapBonusScore;

  //Compute these first so we can know if we need to set forceNonTerminal below.
  rootHistory.makeBoardMoveAssumeLegal(rootBoard,moveLoc,rootPla,rootKoHashTable,preventEncore);
  rootPla = getOpp(rootPla);
  rootKoHashTable->recompute(rootHistory);

  if(rootNode != NULL) {
    SearchNode* child = NULL;
    {
      SearchNodeChildrenReference children = rootNode->getChildren();
      int childrenCapacity = children.getCapacity();
      for(int i = 0; i<childrenCapacity; i++) {
        SearchNode* childCandidate = children[i].getIfAllocated();
        if(childCandidate == NULL)
          break;
        if(children[i].getMoveLocRelaxed() == moveLoc) {
          child = childCandidate;
          break;
        }
      }
    }

    //Just in case, make sure the child has an nnOutput, otherwise no point keeping it.
    //This is a safeguard against any oddity involving node preservation into states that
    //were considered terminal.
    if(child != NULL) {
      NNOutput* nnOutput = child->getNNOutput();
      if(nnOutput == NULL)
        child = NULL;
    }

    if(child != NULL) {
      //Account for time carried over
      {
        int64_t rootVisits = rootNode->stats.visits.load(std::memory_order_acquire);
        int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
        double visitProportion = (double)childVisits / (double)rootVisits;
        if(visitProportion > 1)
          visitProportion = 1;
        effectiveSearchTimeCarriedOver = effectiveSearchTimeCarriedOver * visitProportion * searchParams.treeReuseCarryOverTimeFactor;
      }

      SearchNode* oldRootNode = rootNode;

      //Okay, this is now our new root! Create a copy so as to keep the root out of the node table.
      const bool copySubtreeValueBias = false;
      const bool forceNonTerminal = rootHistory.isGameFinished; // Make sure the root isn't considered terminal if game would be finished.
      rootNode = new SearchNode(*child, forceNonTerminal, copySubtreeValueBias);
      //Sweep over the new root marking it as good (calling NULL function), and then delete anything unmarked.
      //This will include the old copy of the child that we promoted to root.
      applyRecursivelyAnyOrderMulithreaded({rootNode}, NULL);
      bool old = true;
      deleteAllOldOrAllNewTableNodesAndSubtreeValueBiasMulithreaded(old);
      //Old root is not stored in node table, delete it too.
      delete oldRootNode;
    }
    else {
      clearSearch();
    }
  }

  //Explicitly clear avoid move arrays when we play a move - user needs to respecify them if they want them.
  avoidMoveUntilByLocBlack.clear();
  avoidMoveUntilByLocWhite.clear();

  //If we're newly inferring some moves as handicap that we weren't before, clear since score will be wrong.
  if(rootHistory.whiteHandicapBonusScore != oldWhiteHandicapBonusScore)
    clearSearch();

  //In the case that we are conservativePass and a pass would end the game, need to clear the search.
  //This is because deeper in the tree, such a node would have been explored as ending the game, but now that
  //it's a root pass, it needs to be treated as if it no longer ends the game.
  if(searchParams.conservativePass && rootHistory.passWouldEndGame(rootBoard,rootPla))
    clearSearch();

  //In the case that we're preventing encore, and the phase would have ended, we also need to clear the search
  //since the search was conducted on the assumption that we're going into encore now.
  if(preventEncore && rootHistory.passWouldEndPhase(rootBoard,rootPla))
    clearSearch();

  return true;
}


Loc Search::runWholeSearchAndGetMove(Player movePla) {
  return runWholeSearchAndGetMove(movePla,false);
}

Loc Search::runWholeSearchAndGetMove(Player movePla, bool pondering) {
  runWholeSearch(movePla,pondering);
  return getChosenMoveLoc();
}

void Search::runWholeSearch(Player movePla) {
  runWholeSearch(movePla,false);
}

void Search::runWholeSearch(Player movePla, bool pondering) {
  if(movePla != rootPla)
    setPlayerAndClearHistory(movePla);
  std::atomic<bool> shouldStopNow(false);
  runWholeSearch(shouldStopNow,pondering);
}

void Search::runWholeSearch(std::atomic<bool>& shouldStopNow) {
  runWholeSearch(shouldStopNow, false);
}

void Search::runWholeSearch(std::atomic<bool>& shouldStopNow, bool pondering) {
  std::function<void()>* searchBegun = NULL;
  runWholeSearch(shouldStopNow,searchBegun,pondering,TimeControls(),1.0);
}

void Search::runWholeSearch(
  std::atomic<bool>& shouldStopNow,
  std::function<void()>* searchBegun,
  bool pondering,
  const TimeControls& tc,
  double searchFactor
) {

  ClockTimer timer;
  atomic<int64_t> numPlayoutsShared(0);

  if(!std::atomic_is_lock_free(&numPlayoutsShared))
    logger->write("Warning: int64_t atomic numPlayoutsShared is not lock free");
  if(!std::atomic_is_lock_free(&shouldStopNow))
    logger->write("Warning: bool atomic shouldStopNow is not lock free");

  //Do this first, just in case this causes us to clear things and have 0 effective time carried over
  beginSearch(pondering);
  if(searchBegun != NULL)
    (*searchBegun)();
  const int64_t numNonPlayoutVisits = getRootVisits();

  //Compute caps on search
  int64_t maxVisits = pondering ? searchParams.maxVisitsPondering : searchParams.maxVisits;
  int64_t maxPlayouts = pondering ? searchParams.maxPlayoutsPondering : searchParams.maxPlayouts;
  double maxTime = pondering ? searchParams.maxTimePondering : searchParams.maxTime;

  {
    //Possibly reduce computation time, for human friendliness
    if(rootHistory.moveHistory.size() >= 1 && rootHistory.moveHistory[rootHistory.moveHistory.size()-1].loc == Board::PASS_LOC) {
      if(rootHistory.moveHistory.size() >= 3 && rootHistory.moveHistory[rootHistory.moveHistory.size()-3].loc == Board::PASS_LOC)
        searchFactor *= searchParams.searchFactorAfterTwoPass;
      else
        searchFactor *= searchParams.searchFactorAfterOnePass;
    }

    if(searchFactor != 1.0) {
      double cap = (double)((int64_t)1L << 62);
      maxVisits = (int64_t)ceil(std::min(cap, maxVisits * searchFactor));
      maxPlayouts = (int64_t)ceil(std::min(cap, maxPlayouts * searchFactor));
      maxTime = maxTime * searchFactor;
    }
  }

  int capThreads = 0x3fffFFFF;
  if(searchParams.minPlayoutsPerThread > 0.0) {
    int64_t numNewPlayouts = std::min(maxVisits - numNonPlayoutVisits, maxPlayouts);
    double cap = numNewPlayouts / searchParams.minPlayoutsPerThread;
    if(!std::isnan(cap) && cap < (double)0x3fffFFFF) {
      capThreads = std::max(1, (int)floor(cap));
    }
  }

  //Apply time controls. These two don't particularly need to be synchronized with each other so its fine to have two separate atomics.
  std::atomic<double> tcMaxTime(1e30);
  std::atomic<double> upperBoundVisitsLeftDueToTime(1e30);
  const bool hasMaxTime = maxTime < 1.0e12;
  const bool hasTc = !pondering && !tc.isEffectivelyUnlimitedTime();
  if(!pondering && (hasTc || hasMaxTime)) {
    int64_t rootVisits = numPlayoutsShared.load(std::memory_order_relaxed) + numNonPlayoutVisits;
    double timeUsed = timer.getSeconds();
    double tcLimit = 1e30;
    if(hasTc) {
      tcLimit = recomputeSearchTimeLimit(tc, timeUsed, searchFactor, rootVisits);
      tcMaxTime.store(tcLimit, std::memory_order_release);
    }
    double upperBoundVisits = computeUpperBoundVisitsLeftDueToTime(rootVisits, timeUsed, std::min(tcLimit,maxTime));
    upperBoundVisitsLeftDueToTime.store(upperBoundVisits, std::memory_order_release);
  }

  std::function<void(int)> searchLoop = [
    this,&timer,&numPlayoutsShared,numNonPlayoutVisits,&tcMaxTime,&upperBoundVisitsLeftDueToTime,&tc,
    &hasMaxTime,&hasTc,
    &shouldStopNow,maxVisits,maxPlayouts,maxTime,pondering,searchFactor
  ](int threadIdx) {
    SearchThread* stbuf = new SearchThread(threadIdx,*this);

    int64_t numPlayouts = numPlayoutsShared.load(std::memory_order_relaxed);
    try {
      double lastTimeUsedRecomputingTcLimit = 0.0;
      while(true) {
        double timeUsed = 0.0;
        if(hasTc || hasMaxTime)
          timeUsed = timer.getSeconds();

        double tcMaxTimeLimit = 0.0;
        if(hasTc)
          tcMaxTimeLimit = tcMaxTime.load(std::memory_order_acquire);

        bool shouldStop =
          (numPlayouts >= maxPlayouts) ||
          (numPlayouts + numNonPlayoutVisits >= maxVisits);

        //Time limits cannot stop us from doing at least a little search so we have a non-null tree
        if(hasMaxTime && numPlayouts >= 2 && timeUsed >= maxTime)
          shouldStop = true;
        if(hasTc && numPlayouts >= 2 && timeUsed >= tcMaxTimeLimit)
          shouldStop = true;

        //But an explicit stop signal can stop us from doing any search
        if(shouldStop || shouldStopNow.load(std::memory_order_relaxed)) {
          shouldStopNow.store(true,std::memory_order_relaxed);
          break;
        }

        //Thread 0 alone is responsible for recomputing time limits every once in a while
        //Cap of 10 times per second.
        if(!pondering && (hasTc || hasMaxTime) && threadIdx == 0 && timeUsed >= lastTimeUsedRecomputingTcLimit + 0.1) {
          int64_t rootVisits = numPlayouts + numNonPlayoutVisits;
          double tcLimit = 1e30;
          if(hasTc) {
            tcLimit = recomputeSearchTimeLimit(tc, timeUsed, searchFactor, rootVisits);
            tcMaxTime.store(tcLimit, std::memory_order_release);
          }
          double upperBoundVisits = computeUpperBoundVisitsLeftDueToTime(rootVisits, timeUsed, std::min(tcLimit,maxTime));
          upperBoundVisitsLeftDueToTime.store(upperBoundVisits, std::memory_order_release);
        }

        double upperBoundVisitsLeft = 1e30;
        if(hasTc)
          upperBoundVisitsLeft = upperBoundVisitsLeftDueToTime.load(std::memory_order_acquire);
        upperBoundVisitsLeft = std::min(upperBoundVisitsLeft, (double)maxPlayouts - numPlayouts);
        upperBoundVisitsLeft = std::min(upperBoundVisitsLeft, (double)maxVisits - numPlayouts - numNonPlayoutVisits);

        bool finishedPlayout = runSinglePlayout(*stbuf, upperBoundVisitsLeft);
        if(finishedPlayout) {
          numPlayouts = numPlayoutsShared.fetch_add((int64_t)1, std::memory_order_relaxed);
          numPlayouts += 1;
        }
        else {
          //In the case that we didn't finish a playout, give other threads a chance to run before we try again
          //so that it's more likely we become unstuck.
          std::this_thread::yield();
        }
      }
    }
    catch(...) {
      transferOldNNOutputs(*stbuf);
      delete stbuf;
      throw;
    }

    transferOldNNOutputs(*stbuf);
    delete stbuf;
  };

  double actualSearchStartTime = timer.getSeconds();
  performTaskWithThreads(&searchLoop, capThreads);

  //If the search did not actually do anything, we need to still make sure to update the root node if it needs
  //such an update (since root params may differ from tree params).
  if(rootNode != NULL && rootNode->nodeAge.load(std::memory_order_acquire) != searchNodeAge) {
    //Also check if the root node even got an nn eval or not. It might not be, if we quit the search instantly upon
    //start due to an explicit stop signal
    if(rootNode->getNNOutput() != nullptr) {
      const int threadIdx = 0;
      const bool isRoot = true;
      SearchThread thread(threadIdx,*this);
      maybeRecomputeExistingNNOutput(thread,*rootNode,isRoot);
    }
  }

  //Relaxed load is fine since numPlayoutsShared should be synchronized already due to the joins
  lastSearchNumPlayouts = numPlayoutsShared.load(std::memory_order_relaxed);
  effectiveSearchTimeCarriedOver += timer.getSeconds() - actualSearchStartTime;
}

//If we're being asked to search from a position where the game is over, this is fine. Just keep going, the boardhistory
//should reasonably tolerate just continuing. We do NOT want to clear history because we could inadvertently make a move
//that an external ruleset COULD think violated superko.
void Search::beginSearch(bool pondering) {
  if(rootBoard.x_size > nnXLen || rootBoard.y_size > nnYLen)
    throw StringError("Search got from NNEval nnXLen = " + Global::intToString(nnXLen) +
                      " nnYLen = " + Global::intToString(nnYLen) + " but was asked to search board with larger x or y size");

  rootBoard.checkConsistency();

  numSearchesBegun++;

  //Avoid any issues in principle from rolling over
  if(searchNodeAge > 0x3FFFFFFF)
    clearSearch();

  if(!pondering)
    plaThatSearchIsFor = rootPla;
  //If we begin the game with a ponder, then assume that "we" are the opposing side until we see otherwise.
  if(plaThatSearchIsFor == C_EMPTY)
    plaThatSearchIsFor = getOpp(rootPla);

  if(plaThatSearchIsForLastSearch != plaThatSearchIsFor) {
    //In the case we are doing playoutDoublingAdvantage without a specific player (so, doing the root player)
    //and the player that the search is for changes, we need to clear the tree since we need new evals for the new way around
    if(searchParams.playoutDoublingAdvantage != 0 && searchParams.playoutDoublingAdvantagePla == C_EMPTY)
      clearSearch();
    //If we are doing pattern bonus and the player the search is for changes, clear the search. Recomputing the search tree
    //recursively *would* fix all our utilities, but the problem is the playout distribution will still be matching the
    //old probabilities without a lot of new search, so clearing ensures a better distribution.
    if(searchParams.avoidRepeatedPatternUtility != 0 || externalPatternBonusTable != nullptr)
      clearSearch();
    //If we have a human SL net and the parameters are different for the different sides, clear the search.
    if(humanEvaluator != NULL) {
      if((searchParams.humanSLPlaExploreProbWeightless != searchParams.humanSLOppExploreProbWeightless) ||
         (searchParams.humanSLPlaExploreProbWeightful != searchParams.humanSLOppExploreProbWeightful) ||
         (searchParams.humanSLPlaExploreProbWeightless != searchParams.humanSLRootExploreProbWeightless) ||
         (searchParams.humanSLPlaExploreProbWeightful != searchParams.humanSLRootExploreProbWeightful))
        clearSearch();
    }
  }
  plaThatSearchIsForLastSearch = plaThatSearchIsFor;
  //cout << "BEGINSEARCH " << PlayerIO::playerToString(rootPla) << " " << PlayerIO::playerToString(plaThatSearchIsFor) << endl;

  clearOldNNOutputs();
  computeRootValues();

  //Prepare value bias table if we need it
  if(searchParams.subtreeValueBiasFactor != 0 && subtreeValueBiasTable == NULL && !(searchParams.antiMirror && mirroringPla != C_EMPTY))
    subtreeValueBiasTable = new SubtreeValueBiasTable(searchParams.subtreeValueBiasTableNumShards);

  //Refresh pattern bonuses if needed
  if(patternBonusTable != NULL) {
    delete patternBonusTable;
    patternBonusTable = NULL;
  }
  if(searchParams.avoidRepeatedPatternUtility != 0 || externalPatternBonusTable != nullptr) {
    if(externalPatternBonusTable != nullptr)
      patternBonusTable = new PatternBonusTable(*externalPatternBonusTable);
    else
      patternBonusTable = new PatternBonusTable();
    if(searchParams.avoidRepeatedPatternUtility != 0) {
      double bonus = plaThatSearchIsFor == P_WHITE ? -searchParams.avoidRepeatedPatternUtility : searchParams.avoidRepeatedPatternUtility;
      patternBonusTable->addBonusForGameMoves(rootHistory,bonus,plaThatSearchIsFor);
    }
    //Clear any pattern bonus on the root node itself
    if(rootNode != NULL)
      rootNode->patternBonusHash = Hash128();
  }

  if(searchParams.rootSymmetryPruning) {
    const std::vector<int>& avoidMoveUntilByLoc = rootPla == P_BLACK ? avoidMoveUntilByLocBlack : avoidMoveUntilByLocWhite;
    if(rootPruneOnlySymmetries.size() > 0)
      SymmetryHelpers::markDuplicateMoveLocs(rootBoard,rootHistory,&rootPruneOnlySymmetries,avoidMoveUntilByLoc,rootSymDupLoc,rootSymmetries);
    else
      SymmetryHelpers::markDuplicateMoveLocs(rootBoard,rootHistory,NULL,avoidMoveUntilByLoc,rootSymDupLoc,rootSymmetries);
  }
  else {
    //Just in case, don't leave the values undefined.
    std::fill(rootSymDupLoc,rootSymDupLoc+Board::MAX_ARR_SIZE, false);
    rootSymmetries.clear();
    rootSymmetries.push_back(0);
  }

  SearchThread dummyThread(-1, *this);

  if(rootNode == NULL) {
    //Avoid storing the root node in the nodeTable, guarantee that it never is part of a cycle, allocate it directly.
    //Also force that it is non-terminal.
    const bool forceNonTerminal = rootHistory.isGameFinished; // Make sure the root isn't considered terminal if game would be finished.
    rootNode = new SearchNode(rootPla, forceNonTerminal, createMutexIdxForNode(dummyThread));
  }
  else {
    //If the root node has any existing children, then prune things down if there are moves that should not be allowed at the root.
    SearchNode& node = *rootNode;
    SearchNodeChildrenReference children = node.getChildren();
    int childrenCapacity = children.getCapacity();
    bool anyFiltered = false;
    if(childrenCapacity > 0) {

      //This filtering, by deleting children, doesn't conform to the normal invariants that hold during search.
      //However nothing else should be running at this time and the search hasn't actually started yet, so this is okay.
      //Also we can't be affecting the tree since the root node isn't in the table and can't be transposed to.
      int numGoodChildren = 0;
      vector<SearchNode*> filteredNodes;
      {
        int i = 0;
        for(; i<childrenCapacity; i++) {
          SearchNode* child = children[i].getIfAllocated();
          int64_t edgeVisits = children[i].getEdgeVisits();
          Loc moveLoc = children[i].getMoveLoc();
          if(child == NULL)
            break;
          //Remove the child from its current spot
          children[i].store(NULL);
          children[i].setEdgeVisits(0);
          children[i].setMoveLoc(Board::NULL_LOC);
          //Maybe add it back. Specifically check for legality just in case weird graph interaction in the
          //tree gives wrong legality - ensure that once we are the root, we are strict on legality.
          if(rootHistory.isLegal(rootBoard,moveLoc,rootPla) && isAllowedRootMove(moveLoc)) {
            children[numGoodChildren].store(child);
            children[numGoodChildren].setEdgeVisits(edgeVisits);
            children[numGoodChildren].setMoveLoc(moveLoc);
            numGoodChildren++;
          }
          else {
            anyFiltered = true;
            filteredNodes.push_back(child);
          }
        }
        for(; i<childrenCapacity; i++) {
          SearchNode* child = children[i].getIfAllocated();
          (void)child;
          assert(child == NULL);
        }
      }

      if(anyFiltered) {
        //Fix up the node state and child arrays.
        node.collapseChildrenCapacity(numGoodChildren);
        children = node.getChildren();
        childrenCapacity = children.getCapacity();

        //Fix up the number of visits of the root node after doing this filtering
        int64_t newNumVisits = 0;
        for(int i = 0; i<childrenCapacity; i++) {
          const SearchNode* child = children[i].getIfAllocated();
          if(child == NULL)
            break;
          int64_t edgeVisits = children[i].getEdgeVisits();
          newNumVisits += edgeVisits;
        }

        //For the node's own visit itself
        newNumVisits += 1;

        //Set the visits in place
        while(node.statsLock.test_and_set(std::memory_order_acquire));
        node.stats.visits.store(newNumVisits,std::memory_order_release);
        node.statsLock.clear(std::memory_order_release);

        //Update all other stats
        recomputeNodeStats(node, dummyThread, 0, true);
      }
    }

    //Recursively update all stats in the tree if we have dynamic score values
    //And also to clear out lastResponseBiasDeltaSum and lastResponseBiasWeight
    if(searchParams.dynamicScoreUtilityFactor != 0 || searchParams.subtreeValueBiasFactor != 0 || patternBonusTable != NULL) {
      recursivelyRecomputeStats(node);
      if(anyFiltered) {
        //Recursive stats recomputation resulted in us marking all nodes we have. Anything filtered is old now, delete it.
        bool old = true;
        deleteAllOldOrAllNewTableNodesAndSubtreeValueBiasMulithreaded(old);
      }
    }
    else {
      if(anyFiltered) {
        //Sweep over the entire child marking it as good (calling NULL function), and then delete anything unmarked.
        applyRecursivelyAnyOrderMulithreaded({rootNode}, NULL);
        bool old = true;
        deleteAllOldOrAllNewTableNodesAndSubtreeValueBiasMulithreaded(old);
      }
    }
  }

  //Clear unused stuff in value bias table since we may have pruned rootNode stuff
  if(searchParams.subtreeValueBiasFactor != 0 && subtreeValueBiasTable != NULL)
    subtreeValueBiasTable->clearUnusedSynchronous();

  //Mark all nodes old for the purposes of updating old nnoutputs
  searchNodeAge++;
}

uint32_t Search::createMutexIdxForNode(SearchThread& thread) const {
  return thread.rand.nextUInt() & (mutexPool->getNumMutexes()-1);
}

//Based on sha256 of "search.cpp FORCE_NON_TERMINAL_HASH"
static const Hash128 FORCE_NON_TERMINAL_HASH = Hash128(0xd4c31800cb8809e2ULL,0xf75f9d2083f2ffcaULL);

//Must be called AFTER making the bestChildMoveLoc in the thread board and hist.
SearchNode* Search::allocateOrFindNode(SearchThread& thread, Player nextPla, Loc bestChildMoveLoc, bool forceNonTerminal, Hash128 graphHash) {
  //Hash to use as a unique id for this node in the table, for transposition detection.
  //If this collides, we will be sad, but it should be astronomically rare since our hash is 128 bits.
  Hash128 childHash;
  if(searchParams.useGraphSearch) {
    childHash = graphHash;
    if(forceNonTerminal)
      childHash ^= FORCE_NON_TERMINAL_HASH;
  }
  else {
    childHash = thread.board.pos_hash ^ Hash128(thread.rand.nextUInt64(),thread.rand.nextUInt64());
  }

  uint32_t nodeTableIdx = nodeTable->getIndex(childHash.hash0);
  std::mutex& mutex = nodeTable->mutexPool->getMutex(nodeTableIdx);
  std::lock_guard<std::mutex> lock(mutex);

  SearchNode* child = NULL;
  std::map<Hash128,SearchNode*>& nodeMap = nodeTable->entries[nodeTableIdx];

  while(true) {
    auto insertLoc = nodeMap.lower_bound(childHash);

    if(insertLoc != nodeMap.end() && insertLoc->first == childHash) {
      //Attempt to transpose to invalid node - rerandomize hash and just store this node somewhere arbitrary.
      if(insertLoc->second->nextPla != nextPla) {
        childHash = thread.board.pos_hash ^ Hash128(thread.rand.nextUInt64(),thread.rand.nextUInt64());
        continue;
      }
      child = insertLoc->second;
    }
    else {
      child = new SearchNode(nextPla, forceNonTerminal, createMutexIdxForNode(thread));

      //Also perform subtree value bias and pattern bonus handling under the mutex. These parameters are no atomic, so
      //if the node is accessed concurrently by other nodes through the table, we need to make sure these parameters are fully
      //fully-formed before we make the node accessible to anyone.

      if(searchParams.subtreeValueBiasFactor != 0 && subtreeValueBiasTable != NULL) {
        //TODO can we make subtree value bias not depend on prev move loc?
        if(thread.history.moveHistory.size() >= 2) {
          Loc prevMoveLoc = thread.history.moveHistory[thread.history.moveHistory.size()-2].loc;
          if(prevMoveLoc != Board::NULL_LOC) {
            child->subtreeValueBiasTableEntry = subtreeValueBiasTable->get(getOpp(thread.pla), prevMoveLoc, bestChildMoveLoc, thread.history.getRecentBoard(1));
          }
        }
      }

      if(patternBonusTable != NULL)
        child->patternBonusHash = patternBonusTable->getHash(getOpp(thread.pla), bestChildMoveLoc, thread.history.getRecentBoard(1));

      //Insert into map! Use insertLoc as hint.
      nodeMap.insert(insertLoc, std::make_pair(childHash,child));
    }
    break;
  }
  return child;
}

void Search::clearOldNNOutputs() {
  for(size_t i = 0; i<oldNNOutputsToCleanUp.size(); i++)
    delete oldNNOutputsToCleanUp[i];
  oldNNOutputsToCleanUp.resize(0);
}
void Search::transferOldNNOutputs(SearchThread& thread) {
  std::lock_guard<std::mutex> lock(oldNNOutputsToCleanUpMutex);
  for(size_t i = 0; i<thread.oldNNOutputsToCleanUp.size(); i++)
    oldNNOutputsToCleanUp.push_back(thread.oldNNOutputsToCleanUp[i]);
  thread.oldNNOutputsToCleanUp.resize(0);
}

void Search::removeSubtreeValueBias(SearchNode* node) {
  if(node->subtreeValueBiasTableEntry != nullptr) {
    double deltaUtilitySumToSubtract = node->lastSubtreeValueBiasDeltaSum * searchParams.subtreeValueBiasFreeProp;
    double weightSumToSubtract = node->lastSubtreeValueBiasWeight * searchParams.subtreeValueBiasFreeProp;

    SubtreeValueBiasEntry& entry = *(node->subtreeValueBiasTableEntry);
    while(entry.entryLock.test_and_set(std::memory_order_acquire));
    entry.deltaUtilitySum -= deltaUtilitySumToSubtract;
    entry.weightSum -= weightSumToSubtract;
    entry.entryLock.clear(std::memory_order_release);
    node->subtreeValueBiasTableEntry = nullptr;
  }
}

//Delete ALL nodes where nodeAge < searchNodeAge if old is true, else all nodes where nodeAge >= searchNodeAge
//Also clears subtreevaluebias for deleted nodes.
void Search::deleteAllOldOrAllNewTableNodesAndSubtreeValueBiasMulithreaded(bool old) {
  int numAdditionalThreads = numAdditionalThreadsToUseForTasks();
  assert(numAdditionalThreads >= 0);
  std::function<void(int)> g = [&](int threadIdx) {
    size_t idx0 = (size_t)((uint64_t)(threadIdx) * nodeTable->entries.size() / (numAdditionalThreads+1));
    size_t idx1 = (size_t)((uint64_t)(threadIdx+1) * nodeTable->entries.size() / (numAdditionalThreads+1));
    for(size_t i = idx0; i<idx1; i++) {
      std::map<Hash128,SearchNode*>& nodeMap = nodeTable->entries[i];
      for(auto it = nodeMap.cbegin(); it != nodeMap.cend();) {
        SearchNode* node = it->second;
        if(old == (node->nodeAge.load(std::memory_order_acquire) < searchNodeAge)) {
          removeSubtreeValueBias(node);
          delete node;
          it = nodeMap.erase(it);
        }
        else
          ++it;
      }
    }
  };
  performTaskWithThreads(&g, 0x3FFFffff);
}

//Delete ALL nodes. More efficient than deleteAllOldOrAllNewTableNodesAndSubtreeValueBiasMulithreaded if deleting everything.
//Doesn't clear subtree value bias.
void Search::deleteAllTableNodesMulithreaded() {
  int numAdditionalThreads = numAdditionalThreadsToUseForTasks();
  assert(numAdditionalThreads >= 0);
  std::function<void(int)> g = [&](int threadIdx) noexcept {
    size_t idx0 = (size_t)((uint64_t)(threadIdx) * nodeTable->entries.size() / (numAdditionalThreads+1));
    size_t idx1 = (size_t)((uint64_t)(threadIdx+1) * nodeTable->entries.size() / (numAdditionalThreads+1));
    for(size_t i = idx0; i<idx1; i++) {
      std::map<Hash128,SearchNode*>& nodeMap = nodeTable->entries[i];
      for(auto it = nodeMap.cbegin(); it != nodeMap.cend(); ++it) {
        delete it->second;
      }
      nodeMap.clear();
    }
  };
  performTaskWithThreads(&g, 0x3FFFffff);
}

//This function should NOT ever be called concurrently with any other threads modifying the search tree.
//However, it does thread-safely modify things itself, so can safely in theory run concurrently with things
//like ownership computation or analysis that simply read the tree.
void Search::recursivelyRecomputeStats(SearchNode& n) {
  int numAdditionalThreads = numAdditionalThreadsToUseForTasks();
  std::vector<SearchThread*> dummyThreads(numAdditionalThreads+1, NULL);
  for(int threadIdx = 0; threadIdx<numAdditionalThreads+1; threadIdx++)
    dummyThreads[threadIdx] = new SearchThread(threadIdx, *this);

  std::function<void(SearchNode*,int)> f = [&](SearchNode* node, int threadIdx) {
    assert(threadIdx >= 0 && threadIdx < dummyThreads.size());
    SearchThread& thread = *(dummyThreads[threadIdx]);

    bool foundAnyChildren = false;
    SearchNodeChildrenReference children = node->getChildren();
    int childrenCapacity = children.getCapacity();
    int i = 0;
    for(; i<childrenCapacity; i++) {
      SearchNode* child = children[i].getIfAllocated();
      if(child == NULL)
        break;
      foundAnyChildren = true;
    }
    for(; i<childrenCapacity; i++) {
      SearchNode* child = children[i].getIfAllocated();
      (void)child;
      assert(child == NULL);
    }

    //If this node has children, it MUST also have an nnOutput.
    if(foundAnyChildren) {
      NNOutput* nnOutput = node->getNNOutput();
      (void)nnOutput; //avoid warning when we have no asserts
      assert(nnOutput != NULL);
    }

    //Also, something is wrong if we have virtual losses at this point
    int32_t numVirtualLosses = node->virtualLosses.load(std::memory_order_acquire);
    (void)numVirtualLosses;
    assert(numVirtualLosses == 0);

    bool isRoot = (node == rootNode);

    //If the node has no children, then just update its utility directly
    //Again, this would be a little wrong if this function were running concurrently with anything else in the
    //case that new children were added in the meantime. Although maybe it would be okay.
    if(!foundAnyChildren) {
      int64_t numVisits = node->stats.visits.load(std::memory_order_acquire);
      double weightSum = node->stats.weightSum.load(std::memory_order_acquire);
      double winLossValueAvg = node->stats.winLossValueAvg.load(std::memory_order_acquire);
      double noResultValueAvg = node->stats.noResultValueAvg.load(std::memory_order_acquire);
      double scoreMeanAvg = node->stats.scoreMeanAvg.load(std::memory_order_acquire);
      double scoreMeanSqAvg = node->stats.scoreMeanSqAvg.load(std::memory_order_acquire);

      //It's possible that this node has 0 weight in the case where it's the root node
      //and has 0 visits because we began a search and then stopped it before any playouts happened.
      //In that case, there's not much to recompute.
      if(weightSum <= 0.0) {
        assert(numVisits == 0);
        assert(isRoot);
      }
      else {
        double resultUtility = getResultUtility(winLossValueAvg, noResultValueAvg);
        double scoreUtility = getScoreUtility(scoreMeanAvg, scoreMeanSqAvg);
        double newUtilityAvg = resultUtility + scoreUtility;
        newUtilityAvg += getPatternBonus(node->patternBonusHash,getOpp(node->nextPla));
        double newUtilitySqAvg = newUtilityAvg * newUtilityAvg;

        while(node->statsLock.test_and_set(std::memory_order_acquire));
        node->stats.utilityAvg.store(newUtilityAvg,std::memory_order_release);
        node->stats.utilitySqAvg.store(newUtilitySqAvg,std::memory_order_release);
        node->statsLock.clear(std::memory_order_release);
      }
    }
    else {
      //Otherwise recompute it using the usual method
      recomputeNodeStats(*node, thread, 0, isRoot);
    }
  };

  vector<SearchNode*> nodes;
  nodes.push_back(&n);
  applyRecursivelyPostOrderMulithreaded(nodes,&f);

  for(int threadIdx = 0; threadIdx<numAdditionalThreads+1; threadIdx++)
    delete dummyThreads[threadIdx];
}


void Search::computeRootValues() {
  //rootSafeArea is strictly pass-alive groups and strictly safe territory.
  bool nonPassAliveStones = false;
  bool safeBigTerritories = false;
  bool unsafeBigTerritories = false;
  bool isMultiStoneSuicideLegal = rootHistory.rules.multiStoneSuicideLegal;
  rootBoard.calculateArea(
    rootSafeArea,
    nonPassAliveStones,
    safeBigTerritories,
    unsafeBigTerritories,
    isMultiStoneSuicideLegal
  );

  //Figure out how to set recentScoreCenter
  {
    bool foundExpectedScoreFromTree = false;
    double expectedScore = 0.0;
    if(rootNode != NULL) {
      const SearchNode& node = *rootNode;
      int64_t numVisits = node.stats.visits.load(std::memory_order_acquire);
      double weightSum = node.stats.weightSum.load(std::memory_order_acquire);
      double scoreMeanAvg = node.stats.scoreMeanAvg.load(std::memory_order_acquire);
      if(numVisits > 0 && weightSum > 0) {
        foundExpectedScoreFromTree = true;
        expectedScore = scoreMeanAvg;
      }
    }

    //Grab a neural net evaluation for the current position and use that as the center
    if(!foundExpectedScoreFromTree) {
      NNResultBuf nnResultBuf;
      bool includeOwnerMap = true;
      computeRootNNEvaluation(nnResultBuf,includeOwnerMap);
      expectedScore = nnResultBuf.result->whiteScoreMean;
    }

    recentScoreCenter = expectedScore * (1.0 - searchParams.dynamicScoreCenterZeroWeight);
    double cap =  sqrt(rootBoard.x_size * rootBoard.y_size) * searchParams.dynamicScoreCenterScale;
    if(recentScoreCenter > expectedScore + cap)
      recentScoreCenter = expectedScore + cap;
    if(recentScoreCenter < expectedScore - cap)
      recentScoreCenter = expectedScore - cap;
  }

  //If we're using graph search, we recompute the graph hash from scratch at the start of search.
  if(searchParams.useGraphSearch)
    rootGraphHash = GraphHash::getGraphHashFromScratch(rootHistory, rootPla, searchParams.graphSearchRepBound, searchParams.drawEquivalentWinsForWhite);
  else
    rootGraphHash = Hash128();

  Player opponentWasMirroringPla = mirroringPla;
  //Update mirroringPla, mirrorAdvantage, mirrorCenterSymmetryError
  updateMirroring();

  //Clear search if opponent mirror status changed, so that our tree adjusts appropriately
  if(opponentWasMirroringPla != mirroringPla) {
    clearSearch();
    delete subtreeValueBiasTable;
    subtreeValueBiasTable = NULL;
  }
}


bool Search::runSinglePlayout(SearchThread& thread, double upperBoundVisitsLeft) {
  //Store this value, used for futile-visit pruning this thread's root children selections.
  thread.upperBoundVisitsLeft = upperBoundVisitsLeft;

  //Prep this value, playoutDescend will set it to false if the playout shouldn't count
  thread.shouldCountPlayout = true;

  bool finishedPlayout = playoutDescend(thread,*rootNode,true);
  (void)finishedPlayout;

  //Restore thread state back to the root state
  thread.pla = rootPla;
  thread.board = rootBoard;
  thread.history = rootHistory;
  thread.graphHash = rootGraphHash;
  thread.graphPath.clear();

  return thread.shouldCountPlayout;
}

bool Search::playoutDescend(
  SearchThread& thread, SearchNode& node,
  bool isRoot
) {
  //Hit terminal node, finish
  //forceNonTerminal marks special nodes where we cannot end the game, and is set IF they would normally be finished.
  //This includes the root if the root would be game-ended, since if we are searching a position
  //we presumably want to actually explore deeper and get a result. Also it includes the node following a pass from the root in
  //the case where we are conservativePass and the game would be ended. For friendlyPassOk rules, it may include deeper nodes.
  //Note that we also carefully clear the search when a pass from the root would be terminal, so nodes should never need to switch
  //status after tree reuse in the latter case.
  if(thread.history.isGameFinished && !node.forceNonTerminal) {
    //Avoid running "too fast", by making sure that a leaf evaluation takes roughly the same time as a genuine nn eval
    //This stops a thread from building a silly number of visits to distort MCTS statistics while other threads are stuck on the GPU.
    nnEvaluator->waitForNextNNEvalIfAny();
    if(thread.history.isNoResult) {
      double winLossValue = 0.0;
      double noResultValue = 1.0;
      double scoreMean = 0.0;
      double scoreMeanSq = 0.0;
      double lead = 0.0;
      double weight = (searchParams.useUncertainty && nnEvaluator->supportsShorttermError()) ? searchParams.uncertaintyMaxWeight : 1.0;
      addLeafValue(node, winLossValue, noResultValue, scoreMean, scoreMeanSq, lead, weight, true, false);
      return true;
    }
    else {
      double winLossValue = 2.0 * ScoreValue::whiteWinsOfWinner(thread.history.winner, searchParams.drawEquivalentWinsForWhite) - 1;
      double noResultValue = 0.0;
      double scoreMean = ScoreValue::whiteScoreDrawAdjust(thread.history.finalWhiteMinusBlackScore,searchParams.drawEquivalentWinsForWhite,thread.history);
      double scoreMeanSq = ScoreValue::whiteScoreMeanSqOfScoreGridded(thread.history.finalWhiteMinusBlackScore,searchParams.drawEquivalentWinsForWhite);
      double lead = scoreMean;
      double weight = (searchParams.useUncertainty && nnEvaluator->supportsShorttermError()) ? searchParams.uncertaintyMaxWeight : 1.0;
      addLeafValue(node, winLossValue, noResultValue, scoreMean, scoreMeanSq, lead, weight, true, false);
      return true;
    }
  }

  SearchNodeState nodeState = node.state.load(std::memory_order_acquire);
  if(nodeState == SearchNode::STATE_UNEVALUATED) {
    //Always attempt to set a new nnOutput. That way, if some GPU is slow and malfunctioning, we don't get blocked by it.
    {
      bool suc = initNodeNNOutput(thread,node,isRoot,false,false);
      //Leave the node as unevaluated - only the thread that first actually set the nnOutput into the node
      //gets to update the state, to avoid races where we update the state while the node stats aren't updated yet.
      if(!suc) {
        thread.shouldCountPlayout = false;
        return false;
      }
    }

    bool suc = node.state.compare_exchange_strong(nodeState, SearchNode::STATE_EVALUATING, std::memory_order_seq_cst);
    if(!suc) {
      //Presumably someone else got there first.
      //Just give up on this playout and try again from the start.
      thread.shouldCountPlayout = false;
      return false;
    }
    else {
      //Perform the nn evaluation and finish!
      node.initializeChildren();
      node.state.store(SearchNode::STATE_EXPANDED0, std::memory_order_seq_cst);
      return true;
    }
  }
  else if(nodeState == SearchNode::STATE_EVALUATING) {
    //Just give up on this playout and try again from the start.
    thread.shouldCountPlayout = false;
    return false;
  }

  assert(nodeState >= SearchNode::STATE_EXPANDED0);
  maybeRecomputeExistingNNOutput(thread,node,isRoot);

  //Find the best child to descend down
  int numChildrenFound;
  int bestChildIdx;
  Loc bestChildMoveLoc;
  bool countEdgeVisit;

  SearchNode* child = NULL;
  while(true) {
    selectBestChildToDescend(thread,node,nodeState,numChildrenFound,bestChildIdx,bestChildMoveLoc,countEdgeVisit,isRoot);

    //The absurdly rare case that the move chosen is not legal
    //(this should only happen either on a bug or where the nnHash doesn't have full legality information or when there's an actual hash collision).
    //Regenerate the neural net call and continue
    //Could also be true if we have an illegal move due to graph search and we had a cycle and superko interaction, or a true collision
    //on an older path that results in bad transposition between positions that don't transpose.
    if(bestChildIdx >= 0 && !thread.history.isLegal(thread.board,bestChildMoveLoc,thread.pla)) {
      bool isReInit = true;
      initNodeNNOutput(thread,node,isRoot,true,isReInit);

      {
        NNOutput* nnOutput = node.getNNOutput();
        assert(nnOutput != NULL);
        Hash128 nnHash = nnOutput->nnHash;
        //In case of a cycle or bad transposition, this will fire a lot, so limit it to once per thread per search.
        if(thread.illegalMoveHashes.find(nnHash) == thread.illegalMoveHashes.end()) {
          thread.illegalMoveHashes.insert(nnHash);
          logger->write("WARNING: Chosen move not legal so regenerated nn output, nnhash=" + nnHash.toString());
          ostringstream out;
          thread.history.printBasicInfo(out,thread.board);
          thread.history.printDebugInfo(out,thread.board);
          out << Location::toString(bestChildMoveLoc,thread.board) << endl;
          logger->write(out.str());
        }
      }

      //Give up on this playout now that we've forced the nn output to be consistent legality of this path.
      //Return TRUE though, so that the parent path we traversed increments its edge visits.
      //We want the search to continue as best it can, so we increment visits so search will still make progress
      //even if this keeps happening in some really bad transposition or something.
      return true;
    }

    if(bestChildIdx <= -1) {
      //This might happen if all moves have been forbidden. The node will just get stuck counting visits without expanding
      //and we won't do any search.
      addCurrentNNOutputAsLeafValue(node,false);
      return true;
    }

    //Do we think we are searching a new child for the first time?
    if(bestChildIdx >= numChildrenFound) {
      assert(bestChildIdx == numChildrenFound);
      assert(bestChildIdx < NNPos::MAX_NN_POLICY_SIZE);
      bool suc = node.maybeExpandChildrenCapacityForNewChild(nodeState, numChildrenFound+1);
      //Someone else is expanding. Loop again trying to select the best child to explore.
      if(!suc) {
        std::this_thread::yield();
        nodeState = node.state.load(std::memory_order_acquire);
        continue;
      }

      SearchNodeChildrenReference children = node.getChildren(nodeState);
      int childrenCapacity = children.getCapacity();
      assert(childrenCapacity > bestChildIdx);
      (void)childrenCapacity;

      //We can only test this before we make the move, so do it now.
      const bool canForceNonTerminalDueToFriendlyPass =
        bestChildMoveLoc == Board::PASS_LOC &&
        thread.history.shouldSuppressEndGameFromFriendlyPass(thread.board, thread.pla);

      //Make the move! We need to make the move before we create the node so we can see the new state and get the right graphHash.
      thread.history.makeBoardMoveAssumeLegal(thread.board,bestChildMoveLoc,thread.pla,rootKoHashTable);
      thread.pla = getOpp(thread.pla);
      if(searchParams.useGraphSearch)
        thread.graphHash = GraphHash::getGraphHash(
          thread.graphHash, thread.history, thread.pla, searchParams.graphSearchRepBound, searchParams.drawEquivalentWinsForWhite
        );

      //If conservative pass, passing from the root is always non-terminal
      //If friendly passing rules, we might also be non-terminal
      const bool forceNonTerminal = bestChildMoveLoc == Board::PASS_LOC && thread.history.isGameFinished && (
        (searchParams.conservativePass && (&node == rootNode)) ||
        canForceNonTerminalDueToFriendlyPass
      );
      child = allocateOrFindNode(thread, thread.pla, bestChildMoveLoc, forceNonTerminal, thread.graphHash);
      child->virtualLosses.fetch_add(1,std::memory_order_release);

      {
        //Lock mutex to store child and move loc in a synchronized way
        std::lock_guard<std::mutex> lock(mutexPool->getMutex(node.mutexIdx));
        SearchNode* existingChild = children[bestChildIdx].getIfAllocated();
        if(existingChild == NULL) {
          //Set relaxed *first*, then release this value via storing the child. Anyone who load-acquires the child
          //is guaranteed by release semantics to see the move as well.
          SearchChildPointer& childPointer = children[bestChildIdx];
          childPointer.setMoveLocRelaxed(bestChildMoveLoc);
          childPointer.store(child);
        }
        else {
          //Someone got there ahead of us. We already made a move so we can't just loop again. Instead just fail this playout and try again.
          //Even if the node was newly allocated, no need to delete the node, it will get cleaned up next time we mark and sweep the node table later.
          //Clean up virtual losses in case the node is a transposition and is being used.
          child->virtualLosses.fetch_add(-1,std::memory_order_release);
          thread.shouldCountPlayout = false;
          return false;
        }
      }

      //If edge visits is too much smaller than the child's visits, we can avoid descending.
      //Instead just add edge visits and treat that as a visit.
      //If we're not counting edge visits, then we're deliberately trying to add child visits beyond edge visits, don't return early
      if(countEdgeVisit && maybeCatchUpEdgeVisits(thread, node, child, nodeState, bestChildIdx)) {
        updateStatsAfterPlayout(node,thread,isRoot);
        child->virtualLosses.fetch_add(-1,std::memory_order_release);
        return true;
      }
    }
    //Searching an existing child
    else {
      SearchNodeChildrenReference children = node.getChildren(nodeState);
      child = children[bestChildIdx].getIfAllocated();
      assert(child != NULL);

      child->virtualLosses.fetch_add(1,std::memory_order_release);

      //If edge visits is too much smaller than the child's visits, we can avoid descending.
      //Instead just add edge visits and treat that as a visit.
      //If we're not counting edge visits, then we're deliberately trying to add child visits beyond edge visits, don't return early
      if(countEdgeVisit && maybeCatchUpEdgeVisits(thread, node, child, nodeState, bestChildIdx)) {
        updateStatsAfterPlayout(node,thread,isRoot);
        child->virtualLosses.fetch_add(-1,std::memory_order_release);
        return true;
      }

      //Make the move!
      thread.history.makeBoardMoveAssumeLegal(thread.board,bestChildMoveLoc,thread.pla,rootKoHashTable);
      thread.pla = getOpp(thread.pla);
      if(searchParams.useGraphSearch)
        thread.graphHash = GraphHash::getGraphHash(
          thread.graphHash, thread.history, thread.pla, searchParams.graphSearchRepBound, searchParams.drawEquivalentWinsForWhite
        );
    }

    break;
  }

  //If somehow we find ourselves in a cycle, increment edge visits and terminate the playout.
  //Basically if the search likes a cycle... just reinforce playing around the cycle and hope we return something
  //reasonable in the end of the search.
  //Note that this means that child visits >= edge visits is NOT an invariant.
  {
    std::pair<std::unordered_set<SearchNode*>::iterator,bool> result = thread.graphPath.insert(child);
    //No insertion, child was already there
    if(!result.second) {
      if(countEdgeVisit) {
        SearchNodeChildrenReference children = node.getChildren(nodeState);
        children[bestChildIdx].addEdgeVisits(1);
        updateStatsAfterPlayout(node,thread,isRoot);
      }
      // Regardless of whether we count an edge visit or not here, we
      // leave thread.shouldCountPlayout as true so that if we repeatedly are stuck searching a cycle
      // we don't go forever, and eventually hit a visits/playouts limit.

      child->virtualLosses.fetch_add(-1,std::memory_order_release);
      // If we didn't count an edge visit, none of the parents need to update either.
      return countEdgeVisit;
    }
  }

  //Recurse!
  bool shouldUpdateChildAncestors = playoutDescend(thread,*child,false);

  //Update this node stats
  shouldUpdateChildAncestors = shouldUpdateChildAncestors && countEdgeVisit;
  if(shouldUpdateChildAncestors) {
    nodeState = node.state.load(std::memory_order_acquire);
    SearchNodeChildrenReference children = node.getChildren(nodeState);
    children[bestChildIdx].addEdgeVisits(1);
    updateStatsAfterPlayout(node,thread,isRoot);
  }
  child->virtualLosses.fetch_add(-1,std::memory_order_release);

  return shouldUpdateChildAncestors;
}


//If edge visits is too much smaller than the child's visits, we can avoid descending.
//Instead just add edge visits and return immediately.
bool Search::maybeCatchUpEdgeVisits(
  SearchThread& thread,
  SearchNode& node,
  SearchNode* child,
  const SearchNodeState& nodeState,
  const int bestChildIdx
) {
  //Don't need to do this since we already are pretty recent as of finding the best child.
  //nodeState = node.state.load(std::memory_order_acquire);
  SearchNodeChildrenReference children = node.getChildren(nodeState);
  SearchChildPointer& childPointer = children[bestChildIdx];

  // int64_t maxNumToAdd = 1;
  // if(searchParams.graphSearchCatchUpProp > 0.0) {
  //   int64_t parentVisits = node.stats.visits.load(std::memory_order_acquire);
  //   //Truncate down
  //   maxNumToAdd = 1 + (int64_t)(searchParams.graphSearchCatchUpProp * parentVisits);
  // }
  int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
  int64_t edgeVisits = childPointer.getEdgeVisits();

  //If we want to leak through some of the time, then we keep searching the transposition node even if we'd be happy to stop here with
  //how many visits it has
  if(searchParams.graphSearchCatchUpLeakProb > 0.0 && edgeVisits < childVisits && thread.rand.nextBool(searchParams.graphSearchCatchUpLeakProb))
    return false;

  //If the edge visits exceeds the child then we need to search the child more, but as long as that's not the case,
  //we can add more edge visits.
  constexpr int64_t numToAdd = 1;
  // int64_t numToAdd;
  do {
    if(edgeVisits >= childVisits)
      return false;
    // numToAdd = std::min((childVisits - edgeVisits + 3) / 4, maxNumToAdd);
  } while(!childPointer.compexweakEdgeVisits(edgeVisits, edgeVisits + numToAdd));

  return true;
}




================================================================
End of Codebase
================================================================
