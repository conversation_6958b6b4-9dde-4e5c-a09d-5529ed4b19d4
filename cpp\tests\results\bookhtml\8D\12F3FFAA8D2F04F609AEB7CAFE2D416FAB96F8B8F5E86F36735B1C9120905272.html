
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../01/087A91D601B7AE323571DE219ADFC452E15F44577C11C1F33DD932B7DC3CAC06.html';
const pSym = 3;
const board = [0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,1,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,0],],'p':0.4182,'wl':-0.6324,'ssM':0.50,'wlRad':0.0531,'sRad':1.03,'v':19,'av':19,},{'xy':[[1,3],],'p':0.3902,'wl':-0.3792,'ssM':-0.83,'wlRad':0.0158,'sRad':0.54,'v':16,'av':16,},{'xy':[[2,2],],'p':0.4453,'wl':-0.3764,'ssM':-0.38,'wlRad':0.0182,'sRad':0.19,'v':15,'av':15,},{'xy':[[0,3],],'p':0.0667,'wl':0.1612,'ssM':0.48,'wlRad':0.1672,'sRad':0.78,'v':17,'av':17,},{'xy':[[3,1],],'p':0.2637,'wl':0.3433,'ssM':-0.79,'wlRad':0.0011,'sRad':0.51,'v':17,'av':17,},{'xy':[[2,3],],'p':0.4312,'wl':0.3660,'ssM':1.19,'wlRad':0.1880,'sRad':1.23,'v':119,'av':119,},{'xy':[[0,2],],'p':0.7255,'wl':0.4501,'ssM':0.40,'wlRad':0.0265,'sRad':0.88,'v':15,'av':15,},{'xy':[[0,1],],'p':0.1745,'wl':0.5971,'ssM':-0.11,'wlRad':0.0482,'sRad':0.49,'v':13,'av':13,},{'move':'other','p':0.0530,'wl':-0.0289,'ssM':0.07,'wlRad':0.0280,'sRad':0.45,'v':19,'av':19,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
