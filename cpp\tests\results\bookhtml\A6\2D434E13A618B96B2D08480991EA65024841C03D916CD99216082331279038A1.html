
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 1;
const board = [2,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'move':'pass','p':0.6357,'wl':-0.1613,'ssM':0.65,'wlRad':0.0252,'sRad':0.99,'v':196,'av':196,},{'xy':[[2,1],],'p':0.8128,'wl':0.2810,'ssM':0.31,'wlRad':0.1792,'sRad':0.68,'v':91,'av':91,},{'xy':[[2,3],],'p':0.8990,'wl':0.3367,'ssM':0.27,'wlRad':0.0027,'sRad':1.20,'v':71,'av':71,},{'move':'other','p':0.1628,'wl':-0.6317,'ssM':-1.89,'wlRad':0.0176,'sRad':2.07,'v':13,'av':13,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
