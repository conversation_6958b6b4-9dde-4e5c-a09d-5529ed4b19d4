#include "../search/search.h"

#include "../search/searchnode.h"

//------------------------
#include "../core/using.h"
//------------------------

//========================================================================================
// 1. 基础PUCT探索系数计算
//========================================================================================

// 数学公式：c_base(N) = c_puct + c_log * log((N + c_base) / c_base)
// c_puct = 1.0
// c_log = 0.45
// N + c_base = totalChildWeight + 500,  c_base = 500
static double cpuctExploration(double totalChildWeight, const SearchParams& searchParams) {
  return searchParams.cpuctExploration +
    searchParams.cpuctExplorationLog * log((totalChildWeight + searchParams.cpuctExplorationBase) / searchParams.cpuctExplorationBase);
}

// 人类SL网络的探索系数（不同的计算方式）
// 数学公式：c_human(N) = c_human_base + c_human_permanent * sqrt(N)
static double cpuctExplorationHuman(double totalChildWeight, const SearchParams& searchParams) {
  return searchParams.humanSLCpuctExploration + searchParams.humanSLCpuctPermanent * sqrt(totalChildWeight);
}

//Tiny constant to add to numerator of puct formula to make it positive
//even when visits = 0.
// 数值稳定性常数，避免除零
static constexpr double TOTALCHILDWEIGHT_PUCT_OFFSET = 0.01;

//========================================================================================
// 2. 动态探索缩放计算 - KataGo核心创新
//========================================================================================

// 数学公式：exploreScaling = c_base(N) * sqrt(N + ε) * σ_factor(n)

double Search::getExploreScaling(
  double totalChildWeight,          // N: 所有子节点权重和
  double parentUtilityStdevFactor   // σ_factor(n): 动态方差缩放因子
) const {
  return
    cpuctExploration(totalChildWeight, searchParams)           // c_base(N)
    * sqrt(totalChildWeight + TOTALCHILDWEIGHT_PUCT_OFFSET)    // sqrt(N + ε)
    * parentUtilityStdevFactor;                                // σ_factor(n)
}

// 人类SL网络版本
double Search::getExploreScalingHuman(
  double totalChildWeight
) const {
  return
    cpuctExplorationHuman(totalChildWeight, searchParams)
    * sqrt(totalChildWeight + TOTALCHILDWEIGHT_PUCT_OFFSET);
}

//========================================================================================
// 3. 核心PUCT选择值计算
//========================================================================================

// PUCT(c) = V(c) + c_PUCT * P(c) * sqrt(sum(N(c'))) / (1 + N(c))
// 数学公式：PUCT(a) = V_pla(a) + (exploreScaling * P(a)) / (1 + N(a))
double Search::getExploreSelectionValue(
  double exploreScaling,    // c_base(N) * sqrt(N + ε) * σ_factor(n)
  double nnPolicyProb,      // P(a): 神经网络策略概率
  double childWeight,       // N(a): 子节点权重（访问次数的加权版本）
  double childUtility,      // U_white(a): 子节点效用值（白棋视角）
  Player pla                // 当前玩家
) const {
  // 检查移动合法性
  if(nnPolicyProb < 0)
    return POLICY_ILLEGAL_SELECTION_VALUE;  // 非法移动返回极小值

  // exploreScaling = c_PUCT * sqrt(totalChildWeight)
  // 探索项：c_dynamic * P(a) / (1 + N(a))
  //      = c_base(N) * sqrt(N + ε) * σ_factor(n) * P(a) / (1 + N(a))
  double exploreComponent = exploreScaling * nnPolicyProb / (1.0 + childWeight);

  //At the last moment, adjust value to be from the player's perspective, so that players prefer values in their favor
  //rather than in white's favor
  // 价值项：转换为当前玩家视角
  // 数学公式：V_pla(a) = sign(pla) * U_white(a)
  
  // childUtility始终是白棋视角的效用值
  // 正值表示对白棋有利，负值表示对黑棋有利
  // 视角转换的逻辑：

  // 白棋回合：直接使用childUtility（白棋希望最大化白棋视角的效用）
  // 黑棋回合：使用-childUtility（黑棋希望最小化白棋视角的效用，即最大化黑棋视角的效用）
  double valueComponent = pla == P_WHITE ? childUtility : -childUtility;

  // PUCT公式：价值项 + 探索项
  return exploreComponent + valueComponent; 
}

//========================================================================================
// 4. PUCT逆向计算 - 用于策略目标剪枝
//========================================================================================

// 数学公式：给定PUCT值，反推需要的childWeight
// 解方程：PUCT = V_pla + (exploreScaling * P(a)) / (1 + N(a))
// 求解：N(a) = (exploreScaling * P(a)) / (PUCT - V_pla) - 1
// 给定目标 PUCT 值，计算需要多少访问次数才能达到这个值
// 用于判断当前的访问分配是否"过度"

//Return the childWeight that would make Search::getExploreSelectionValue return the given explore selection value.
//Or return 0, if it would be less than 0.
double Search::getExploreSelectionValueInverse(
  double exploreSelectionValue,   // 目标PUCT值
  double exploreScaling,
  double nnPolicyProb,
  double childUtility,
  Player pla
) const {
  if(nnPolicyProb < 0)
    return 0;
  double valueComponent = pla == P_WHITE ? childUtility : -childUtility;

  double exploreComponent = exploreSelectionValue - valueComponent;
  double exploreComponentScaling = exploreScaling * nnPolicyProb;

  //Guard against float weirdness
  // 目标 PUCT 值 ≤ 当前价值评估
  // 这种情况下，无论访问多少次都无法达到目标
  // 返回极大值表示"需要无限次访问"，实际上是标记这个计算无意义
  if(exploreComponent <= 0)
    return 1e100;

  double childWeight = exploreComponentScaling / exploreComponent - 1;
  if(childWeight < 0)
    childWeight = 0;
  return childWeight;
}

//========================================================================================
// 5. 宽根噪声 - 增加根节点探索多样性
//========================================================================================

// 数学公式：对根节点添加随机效用扰动和策略平滑

static void maybeApplyWideRootNoise(
  double& childUtility,                  // 修改子节点效用
  float& nnPolicyProb,                   // 修改策略概率
  const SearchParams& searchParams,
  SearchThread* thread,
  const SearchNode& parent
) {
  //For very large wideRootNoise, go ahead and also smooth out the policy
  // 策略平滑：P'(a) = P(a)^(1/(4*noise + 1))
  nnPolicyProb = (float)pow(nnPolicyProb, 1.0 / (4.0*searchParams.wideRootNoise + 1.0));
  // 随机效用扰动：50%概率添加高斯噪声
  if(thread->rand.nextBool(0.5)) {
    double bonus = searchParams.wideRootNoise * std::fabs(thread->rand.nextGaussian());
    // 随机效用扰动：50%概率添加高斯噪声
    if(parent.nextPla == P_WHITE)
      childUtility += bonus;
    else
      childUtility -= bonus;
  }
}


//========================================================================================
// 6. 已存在子节点的完整PUCT计算
//========================================================================================

// 这是KataGo最复杂的函数，整合了所有MCTS改进
double Search::getExploreSelectionValueOfChild(
  const SearchNode& parent,              // 父节点
  const float* parentPolicyProbs,        // 父节点策略概率数组
  const SearchNode* child,               // 子节点
  Loc moveLoc,                          // 移动位置          
  double exploreScaling,                // 探索缩放因子
  double totalChildWeight,              // 所有子节点权重和
  int64_t childEdgeVisits,             // 子节点边访问次数
  double fpuValue,                     // FPU值（未探索节点的估计值）
  double parentUtility,                // 父节点效用
  double parentWeightPerVisit,         // 父节点每次访问的权重
  bool isDuringSearch,                 // 是否在搜索过程中
  bool antiMirror,                     // 是否启用反镜像
  double maxChildWeight,               // 最大子节点权重
  bool countEdgeVisit,                 // 是否计算边访问
  SearchThread* thread                 // 搜索线程
) const {
  (void)parentUtility; // 未使用的参数
  int movePos = getPos(moveLoc);
  float nnPolicyProb = parentPolicyProbs[movePos];

  //========================================================================================
  // 6.1 子节点统计信息获取 - 多线程安全的原子操作
  //========================================================================================
  
  // 步骤1：获取子节点的核心统计数据（全部使用原子操作确保线程安全）
  // virtualLosses：当前正在搜索此节点的线程数（int32_t，原子变量）
  // 用于多线程搜索中的负载均衡，避免多个线程同时搜索同一分支
  int32_t childVirtualLosses = child->virtualLosses.load(std::memory_order_acquire);
  
  // visits：子节点被访问的总次数（int64_t，原子变量）
  // 这是 MCTS 中的基础统计量，用于计算访问频率和置信度
  int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
  
  // utilityAvg：子节点的平均效用值（double，原子变量）
  // 表示从白棋视角看该节点的价值，范围通常在 [-3, +3] 之间
  // 包含胜负概率、得分差异、动态评估等综合信息
  double utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);
  
  // scoreMeanAvg：子节点的平均得分（double，原子变量）
  // 表示预期的最终得分差异，用于精确的终局计算
  double scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
  
  // scoreMeanSqAvg：子节点得分平方的平均值（double，原子变量）
  // 用于计算得分的方差：Var(score) = E[score²] - E[score]²
  // 方差信息帮助评估预测的不确定性
  double scoreMeanSqAvg = child->stats.scoreMeanSqAvg.load(std::memory_order_acquire);

  //========================================================================================
  // 6.2 子节点权重计算 - 区分边访问和节点权重
  //========================================================================================
  
  double childWeight;
  // countEdgeVisit：决定使用哪种权重计算模式
  if(countEdgeVisit) {
    // 边访问模式：计算从父节点到子节点的边被访问的权重
    // childEdgeVisits：边的访问次数，childVisits：节点本身的访问次数
    // 用于正常的 MCTS 搜索，精确统计选择频率
    childWeight = child->stats.getChildWeight(childEdgeVisits,childVisits);
  }
  else {
    // 节点权重模式：直接使用子节点的累积权重和
    // weightSum：节点内部所有搜索的累积权重（原子变量）
    // 用于特殊搜索模式（如人类网络、分析模式），不影响正常统计
    childWeight = child->stats.weightSum.load(std::memory_order_acquire);
  }

  //========================================================================================
  // 6.3 多线程竞态条件处理
  //========================================================================================
  
  // 多线程环境中的潜在问题：
  // 1. childVisits = 0：子节点刚被创建，但首次访问尚未完成
  // 2. childWeight <= 0：由于原子变量更新的时序差异，权重可能暂时不一致
  // 3. 数据不同步：visits 和 weight 是独立的原子变量，可能短暂不同步

  //========================================================================================
  // 6.4 子节点效用值计算
  //========================================================================================
  
  double childUtility;
  // 检查节点是否有有效的统计数据
  if(childVisits <= 0 || childWeight <= 0.0) {
    // 情况1：节点未被充分访问，使用 FPU（First Play Urgency）值
    // fpuValue：未探索节点的估计效用，通常基于父节点效用计算
    // 这确保了未探索的节点仍有合理的初始评估
    childUtility = fpuValue;
  } else {
    // 情况2：节点有充分统计数据，使用平均效用值
    childUtility = utilityAvg;

    //========================================================================================
    // 6.5 终局得分精细调整 - KataGo 特有的得分优化
    //========================================================================================
    
    // getEndingWhiteScoreBonus：计算该着法对白棋得分的奖励/惩罚
    // 考虑提子、死活、目数、贴目等终局因素
    // 返回值：正数表示对白棋有利，负数表示不利，零表示无特殊影响
    double endingScoreBonus = getEndingWhiteScoreBonus(parent,moveLoc);
    
    if(endingScoreBonus != 0) {
      // getScoreUtilityDiff：将得分差异转换为效用差异
      // 参数：scoreMeanAvg（平均得分）、scoreMeanSqAvg（得分方差）、endingScoreBonus（得分调整）
      // 返回：对应的效用调整量，考虑了得分的不确定性
      // 
      // 数学原理：ΔUtility = f(ΔScore, σ_score)
      // 其中 σ_score = sqrt(scoreMeanSqAvg - scoreMeanAvg²)
      childUtility += getScoreUtilityDiff(scoreMeanAvg, scoreMeanSqAvg, endingScoreBonus);
    }
  }

  //========================================================================================
  // 7. 虚拟损失处理 - 多线程搜索中避免重复探索
  //========================================================================================
  
  // 数学公式：U'(a) = U(a) * (1 - α) + U_virtual * α
  // 其中：α = W_virtual / (W_virtual + W_real)
  
  //Virtual losses to direct threads down different paths
  // 目的：当多个线程同时选择某个子节点时，降低该节点的吸引力，
  //       引导其他线程选择不同的路径，提高搜索效率
  if(childVirtualLosses > 0) {
    // 步骤1：计算虚拟损失的总权重
    // childVirtualLosses：正在搜索此节点的线程数量（原子变量）
    // numVirtualLossesPerThread：每个线程贡献的虚拟损失权重（默认 1.0）
    // W_virtual = N_threads * weight_per_thread
    double virtualLossWeight = childVirtualLosses * searchParams.numVirtualLossesPerThread;

    // 步骤2：计算虚拟损失的效用值（极端不利值）
    // utilityRadius：效用值的最大范围 ≈ winLoss + staticScore + dynamicScore ≈ 2.0~3.0
    // 这代表了胜负、静态得分、动态得分的总影响范围
    double utilityRadius = searchParams.winLossUtilityFactor + searchParams.staticScoreUtilityFactor + searchParams.dynamicScoreUtilityFactor;
    
    // virtualLossUtility：对当前玩家极其不利的效用值
    // 如果轮到白棋：U_virtual = -utilityRadius（对白棋极不利）
    // 如果轮到黑棋：U_virtual = +utilityRadius（对黑棋极不利）
    double virtualLossUtility = (parent.nextPla == P_WHITE ? -utilityRadius : utilityRadius);
    
    // 步骤3：计算虚拟损失在总权重中的比例（混合系数）
    // α = W_virtual / (W_virtual + W_real)
    // 其中：W_real = max(0.25, childWeight)，0.25是数值稳定性保护
    // virtualLossWeightFrac ∈ [0, 1]：虚拟损失的影响比例
    double virtualLossWeightFrac = (double)virtualLossWeight / (virtualLossWeight + std::max(0.25,childWeight));
    
    // 步骤4：计算混合后的效用值（加权平均）
    // 数学公式：U'(a) = U(a) + (U_virtual - U(a)) * α
    // 等价于：U'(a) = U(a) * (1 - α) + U_virtual * α
    // 
    // 解释：当 α = 0 时，U'(a) = U(a)（无虚拟损失）
    //       当 α = 1 时，U'(a) = U_virtual（完全是虚拟损失）
    //       当 α ∈ (0,1) 时，是真实效用和虚拟损失的线性插值
    childUtility = childUtility + (virtualLossUtility - childUtility) * virtualLossWeightFrac;
    
    // 步骤5：更新子节点总权重（保持PUCT公式一致性）
    // W'(a) = W(a) + W_virtual
    // 这确保了在 PUCT 公式 P(a) / (1 + N(a)) 中，分母也相应增大
    childWeight += virtualLossWeight;
  }

  //========================================================================================
  // 8. 根节点特殊优化策略 - 仅在根节点和正常搜索时启用
  //========================================================================================
  
  // 条件检查：必须是搜索过程中 && 当前是根节点 && 计算边访问
  if(isDuringSearch && (&parent == rootNode) && countEdgeVisit) {
    
    //========================================================================================
    // 8.1 无效访问剪枝 (Futile Visits Pruning) - 时间管理优化
    //========================================================================================
    
    // 目的：如果剩余搜索时间不足以让某个着法达到有意义的访问次数，就提前剪枝
    // 避免在明显无法充分探索的分支上浪费时间
    if(searchParams.futileVisitsThreshold > 0) {
      // 步骤1：计算该着法要达到"有意义"状态需要的权重
      // futileVisitsThreshold：阈值系数（通常为 0.02-0.05）
      // maxChildWeight：当前最受关注的子节点权重
      // requiredWeight：该着法需要达到的最小权重 = 阈值 × 最大权重
      double requiredWeight = searchParams.futileVisitsThreshold * maxChildWeight;
      
      // 步骤2：估算达到目标权重需要多少次访问
      // averageVisitsPerWeight：每单位权重需要多少次访问
      // +1.0 和 +parentWeightPerVisit：数值稳定性保护，避免除零
      double averageVisitsPerWeight = (childEdgeVisits + 1.0) / (childWeight + parentWeightPerVisit);
      
      // 步骤3：计算预期需要的总访问次数
      double estimatedRequiredVisits = requiredWeight * averageVisitsPerWeight;
      
      // 步骤4：判断是否有足够的剩余访问次数
      // childVisits：已有访问次数
      // upperBoundVisitsLeft：剩余可用访问次数的上界
      // 如果 (已有 + 剩余) < 预期需求，则剪枝
      if(childVisits + thread->upperBoundVisitsLeft < estimatedRequiredVisits)
        return FUTILE_VISITS_PRUNE_VALUE;  // 返回极小值，标记为无效
    }
    
    //========================================================================================
    // 8.2 根节点访问分配强化 - 确保探索覆盖度
    //========================================================================================
    
    // 目的：根据策略概率强制分配最小访问次数，避免高概率着法被过早忽略
    if(searchParams.rootDesiredPerChildVisitsCoeff > 0.0) {
      // 数学公式：期望权重 = sqrt(P(a) × 总权重 × 系数)
      // 
      // 设计思想：
      // - 高策略概率的着法应该获得更多访问
      // - 使用平方根确保不会过度集中
      // - 系数控制强制程度
      if(nnPolicyProb > 0 && childWeight < sqrt(nnPolicyProb * totalChildWeight * searchParams.rootDesiredPerChildVisitsCoeff)) {
        return 1e20;  // 返回极大值，强制选择这个着法
      }
    }
    
    //========================================================================================
    // 8.3 提示着法特殊处理 - 人工干预机制
    //========================================================================================
    
    // 目的：确保用户指定的"提示着法"获得充分搜索
    // rootHintLoc：用户通过接口指定的建议着法位置
    if(rootHintLoc != Board::NULL_LOC && moveLoc == rootHintLoc) {
      // 步骤1：计算当前着法的平均权重增长率
      double averageWeightPerVisit = (childWeight + parentWeightPerVisit) / (childVisits + 1.0);
      
      // 步骤2：遍历所有其他子节点，确保提示着法不落后太多
      // ConstSearchNodeChildrenReference：线程安全的子节点容器引用
      // 提供对父节点所有子节点的只读访问，支持多线程并发访问
      ConstSearchNodeChildrenReference children = parent.getChildren();
      
      // childrenCapacity：子节点容器的最大容量
      // 这是预分配的数组大小，实际子节点数量可能小于此值
      int childrenCapacity = children.getCapacity();
      
      // 遍历所有已分配的子节点
      for(int i = 0; i<childrenCapacity; i++) {
        // SearchChildPointer：智能指针，管理子节点的生命周期和访问
        // 包含子节点指针、移动位置、边访问统计等信息
        const SearchChildPointer& childPointer = children[i];
        
        // getIfAllocated()：安全获取子节点指针
        // 返回值：如果子节点已分配则返回指针，否则返回 NULL
        // 这是多线程安全的操作，避免访问未初始化的节点
        const SearchNode* c = childPointer.getIfAllocated();
        
        // 提前终止条件：遇到第一个未分配的位置
        // 由于子节点是连续分配的，NULL 表示后续位置都未使用
        if(c == NULL)
          break;
        
        // 获取其他子节点的访问统计信息
        // cEdgeVisits：该子节点被从父节点选择的次数（边访问次数）
        // 这是 MCTS 中的关键统计量，反映了该着法的受关注程度
        int64_t cEdgeVisits = childPointer.getEdgeVisits();
        
        // cWeight：该子节点的加权访问次数
        // getChildWeight(edgeVisits)：将边访问次数转换为权重值
        // 权重可能与访问次数不完全相等，考虑了访问质量等因素
        // 
        // 计算逻辑可能包含：
        // - 基础访问次数
        // - 访问时间衰减
        // - 搜索深度调整
        // - 其他启发式因子
        double cWeight = c->stats.getChildWeight(cEdgeVisits);
        
        // 步骤3：检查提示着法是否落后太多
        // 如果 (当前权重 + 一次访问的权重) < 其他着法权重 × 0.8
        // 则强制选择提示着法，确保它不会被严重忽略
        if(childWeight + averageWeightPerVisit < cWeight * 0.8)
          return 1e20;  // 强制选择
      }
    }

    //========================================================================================
    // 8.4 宽根噪声应用 - 增加根节点探索多样性
    //========================================================================================
    
    // 目的：在根节点添加随机扰动，避免搜索过于确定性
    // wideRootNoise > 0：启用噪声
    // nnPolicyProb >= 0：确保是合法着法
    if(searchParams.wideRootNoise > 0.0 && nnPolicyProb >= 0) {
      // maybeApplyWideRootNoise：随机修改效用值和策略概率
      // 50%概率添加高斯噪声，增加探索的随机性
      maybeApplyWideRootNoise(childUtility, nnPolicyProb, searchParams, thread, parent);
    }
  }
  //========================================================================================
  // 9. 反镜像策略 - 避免左右对称的重复搜索
  //========================================================================================
  
  // 条件检查：搜索过程中 && 启用反镜像 && 合法着法 && 计算边访问
  if(isDuringSearch && antiMirror && nnPolicyProb >= 0 && countEdgeVisit) {
    
    // 步骤1：策略概率反镜像调整
    // maybeApplyAntiMirrorPolicy：根据镜像对称性调整策略概率
    // 目的：如果检测到当前局面与之前某个局面镜像对称，
    //       则降低已经在镜像位置搜索过的着法的策略概率
    // 
    // 参数解释：
    // - nnPolicyProb：当前着法的神经网络策略概率（会被修改）
    // - moveLoc：当前着法位置
    // - parentPolicyProbs：父节点的完整策略概率数组
    // - parent.nextPla：当前轮到的玩家
    // - thread：搜索线程（包含随机数生成器等）
    maybeApplyAntiMirrorPolicy(nnPolicyProb, moveLoc, parentPolicyProbs, parent.nextPla, thread);
    
    // 步骤2：强制反镜像探索
    // maybeApplyAntiMirrorForcedExplore：在效用值上应用反镜像调整
    // 目的：如果某个着法在镜像位置已经被充分探索，
    //       则可能提升当前位置的效用值，鼓励探索非镜像的变化
    // 
    // 参数解释：
    // - childUtility：子节点效用值（会被修改）
    // - parentUtility：父节点效用值（参考基准）
    // - moveLoc：当前着法位置
    // - parentPolicyProbs：父节点策略概率数组
    // - childWeight：当前子节点权重
    // - totalChildWeight：所有子节点权重总和
    // - parent.nextPla：当前玩家
    // - thread：搜索线程
    // - parent：父节点引用
    // 
    // 反镜像机制的核心思想：
    // - 检测局面的左右镜像对称性
    // - 如果发现镜像模式，调整搜索策略避免重复计算
    // - 这在围棋开局阶段特别有效，因为很多开局具有对称性
    maybeApplyAntiMirrorForcedExplore(childUtility, parentUtility, moveLoc, parentPolicyProbs, childWeight, totalChildWeight, parent.nextPla, thread, parent);
  }

  //========================================================================================
  // 10. 最终PUCT值计算 - 整合所有因素
  //========================================================================================
  
  // 调用核心PUCT计算函数，整合前面所有的处理结果：
  // - exploreScaling：动态探索缩放因子（考虑总访问数和方差）
  // - nnPolicyProb：神经网络策略概率（可能经过噪声、反镜像等调整）
  // - childWeight：子节点权重（包含虚拟损失调整）
  // - childUtility：子节点效用（包含得分调整、虚拟损失、噪声等）
  // - parent.nextPla：当前玩家（用于效用值视角转换）
  // 
  // 返回：该着法的最终PUCT选择值，用于与其他着法比较
  return getExploreSelectionValue(exploreScaling,nnPolicyProb,childWeight,childUtility,parent.nextPla);
}

//========================================================================================
// 11. 新子节点的PUCT计算 - 处理未创建的子节点
//========================================================================================

// 功能：为尚未在搜索树中创建的新子节点计算PUCT选择值
// 与 getExploreSelectionValueOfChild 的区别：
// - 该函数：处理已存在的子节点，有完整的统计数据
// - 本函数：处理新子节点，只有策略概率，无访问历史
double Search::getNewExploreSelectionValue(
  const SearchNode& parent,              // 父节点
  double exploreScaling,                // 探索缩放因子
  float nnPolicyProb,                   // 新着法的神经网络策略概率
  double fpuValue,                      // FPU值（First Play Urgency，首次游戏紧迫性）
  double parentWeightPerVisit,          // 父节点每次访问的平均权重
  double maxChildWeight,                // 当前最大子节点权重（用于剪枝）
  bool countEdgeVisit,                  // 是否计算边访问（影响权重统计）
  SearchThread* thread                  // 搜索线程
) const {
  
  //========================================================================================
  // 11.1 新节点的初始状态设置
  //========================================================================================
  
  // childWeight = 0：新节点尚未被访问，权重为零
  // 这意味着在PUCT公式中，探索项会获得最大值：
  // exploreComponent = exploreScaling * nnPolicyProb / (1.0 + 0) = exploreScaling * nnPolicyProb
  double childWeight = 0;
  
  // childUtility = fpuValue：新节点使用FPU机制进行效用估计
  // FPU值通常基于父节点效用计算，考虑了探索的保守性
  // 设计原理：未探索的节点应该有适度的吸引力，但不能过高
  double childUtility = fpuValue;
  
  //========================================================================================
  // 11.2 根节点特殊处理 - 简化版优化策略
  //========================================================================================
  
  // 条件：仅在根节点且计算边访问时应用优化
  // 注意：新节点的优化策略比已存在节点更简单，只包含两个主要机制
  if(&parent == rootNode && countEdgeVisit) {
    
    //========================================================================================
    // 11.2.1 新节点的无效访问剪枝
    //========================================================================================
    
    // 目的：对于新节点，提前判断是否值得创建和搜索
    // 逻辑：如果剩余搜索时间连基本探索都无法完成，直接剪枝
    if(searchParams.futileVisitsThreshold > 0) {
      // 计算新节点达到有意义状态需要的访问次数
      // averageVisitsPerWeight：每单位权重需要的访问次数估计
      // 对于新节点，使用父节点的访问效率作为估计基准
      double averageVisitsPerWeight = 1.0 / parentWeightPerVisit;
      
      // requiredWeight：新节点需要达到的最小权重阈值
      // 基于当前最受关注节点的权重比例计算
      double requiredWeight = searchParams.futileVisitsThreshold * maxChildWeight;
      
      // estimatedRequiredVisits：预估需要的总访问次数
      double estimatedRequiredVisits = requiredWeight * averageVisitsPerWeight;
      
      // 剪枝条件：如果剩余访问次数不足以达到最小阈值，直接放弃
      // 注意：新节点的条件比已存在节点更严格，因为还没有任何访问基础
      if(thread->upperBoundVisitsLeft < estimatedRequiredVisits)
        return FUTILE_VISITS_PRUNE_VALUE;  // 返回极小值，标记为无价值
    }
    
    //========================================================================================
    // 11.2.2 新节点的宽根噪声应用
    //========================================================================================
    
    // 目的：为新节点添加随机性，增加探索多样性
    // 重要性：新节点特别需要噪声来打破确定性选择模式
    if(searchParams.wideRootNoise > 0.0) {
      // 应用策略概率平滑和效用随机扰动
      // 对于新节点，噪声的影响更显著，因为没有历史数据的"惯性"
      maybeApplyWideRootNoise(childUtility, nnPolicyProb, searchParams, thread, parent);
    }
  }
  
  //========================================================================================
  // 11.3 最终PUCT计算 - 新节点的特殊性
  //========================================================================================
  
  // 调用核心PUCT计算，新节点的特点：
  // - exploreScaling：与已存在节点相同的探索缩放
  // - nnPolicyProb：可能经过噪声调整的策略概率
  // - childWeight = 0：这使得探索项达到最大值，体现了"未知即有价值"的原则
  // - childUtility = fpuValue：保守但合理的效用估计
  // - parent.nextPla：当前玩家视角
  // 
  // 数学效果：PUCT = fpuValue + exploreScaling * nnPolicyProb
  // 这确保了新节点有足够的吸引力被选择，同时受策略概率引导
  return getExploreSelectionValue(exploreScaling,nnPolicyProb,childWeight,childUtility,parent.nextPla);
}

//========================================================================================
// 12. 缩减游戏选择权重 - 搜索资源分配优化
//========================================================================================

// 功能：计算在"缩减游戏"（reduced play）模式下，某个子节点应该获得的权重
// 
// 核心思想：搜索资源的回顾性分配优化
// - 在搜索过程中，我们可能对某些着法分配了过多的访问次数
// - 如果某个着法的当前权重超出了它"应该"获得的权重，则进行削减
// - 这有助于将多余的搜索资源重新分配给更有价值的着法
// 
// 使用场景：
// 1. 实时分析模式：动态调整搜索焦点
// 2. 时间管理：在时间压力下优化资源分配
// 3. 策略改进：根据最新信息重新评估着法价值
// 
// 数学原理：
// 给定最佳着法的PUCT值，反推其他着法"应该"获得的权重
// 如果实际权重 > 理论权重，则削减到理论值
// 如果实际权重 ≤ 理论权重，则保持不变

double Search::getReducedPlaySelectionWeight(
  const SearchNode& parent,               // 父节点（必须是根节点）
  const float* parentPolicyProbs,         // 父节点的策略概率数组
  const SearchNode* child,                // 目标子节点
  Loc moveLoc,                           // 着法位置
  double exploreScaling,                 // 探索缩放因子
  int64_t childEdgeVisits,              // 子节点边访问次数
  double bestChildExploreSelectionValue // 当前最佳子节点的PUCT值（基准）
) const {
  
  //========================================================================================
  // 12.1 前置条件验证
  //========================================================================================
  
  // 断言：此函数仅用于根节点
  // 原因：缩减游戏主要用于根节点的最终着法选择和资源重分配
  //       非根节点的权重调整可能影响搜索树的一致性
  assert(&parent == rootNode);
  
  // 获取着法在策略数组中的位置索引
  // getPos(moveLoc)：将棋盘坐标转换为神经网络输出数组的索引
  // 例如：围棋19×19，movePos ∈ [0, 361)，其中361 = 19×19 + 1(pass)
  int movePos = getPos(moveLoc);
  
  // 获取该着法的神经网络策略概率
  // 
  // parentPolicyProbs：父节点的完整策略概率数组
  // - 数据结构：float数组，长度为 policySize (通常361 for 19×19围棋)
  // - 内容：P(a|s) 对于所有可能着法 a 在当前局面 s 下的概率
  // - 来源：神经网络直接输出，经过softmax归一化
  // - 范围：每个元素 ∈ [0, 1]，所有元素之和 ≈ 1.0
  // - 负值：表示非法着法（如occupied position、ko violation等）
  // 
  // 数组示例（简化的5×5围棋）：
  // parentPolicyProbs[0] = 0.02   // 位置(0,0)的概率
  // parentPolicyProbs[1] = 0.15   // 位置(0,1)的概率
  // parentPolicyProbs[2] = 0.08   // 位置(0,2)的概率
  // ...
  // parentPolicyProbs[24] = 0.03  // 位置(4,4)的概率
  // parentPolicyProbs[25] = 0.05  // pass的概率
  // 
  // nnPolicyProb：当前考虑的特定着法 moveLoc 的策略概率
  // - 这是一个标量值，从完整概率分布中提取的单个元素
  // - 表示神经网络认为在当前局面下，选择 moveLoc 这步棋的合理性
  // - 这是该着法的"先验价值"，不依赖于MCTS搜索统计
  // - 用于PUCT公式中的探索项：exploreScaling * P(a) / (1 + N(a))
  float nnPolicyProb = parentPolicyProbs[movePos];

  //========================================================================================
  // 12.2 子节点统计信息获取 - 多线程安全访问
  //========================================================================================
  
  // 步骤1：获取子节点的核心统计数据（全部使用原子操作）
  // visits：子节点被访问的总次数（int64_t，原子变量）
  // 反映该着法在搜索中的受关注程度和统计置信度
  int64_t childVisits = child->stats.visits.load(std::memory_order_acquire);
  
  // scoreMeanAvg：子节点的平均得分（double，原子变量）
  // 表示从该节点继续游戏的预期得分差异（白棋视角）
  double scoreMeanAvg = child->stats.scoreMeanAvg.load(std::memory_order_acquire);
  
  // scoreMeanSqAvg：子节点得分平方的平均值（double，原子变量）
  // 用于计算得分方差：Var(score) = E[score²] - E[score]²
  // 方差信息用于评估预测的不确定性和可靠性
  double scoreMeanSqAvg = child->stats.scoreMeanSqAvg.load(std::memory_order_acquire);
  
  // utilityAvg：子节点的平均效用值（double，原子变量）
  // 综合考虑胜负概率、得分差异、动态评估的综合价值指标
  double utilityAvg = child->stats.utilityAvg.load(std::memory_order_acquire);
  
  // childWeight：子节点的加权访问次数
  // getChildWeight(edgeVisits, visits)：将边访问和节点访问转换为权重值
  // 权重可能包含质量调整、时间衰减等因素，不一定等于简单的访问次数
  double childWeight = child->stats.getChildWeight(childEdgeVisits,childVisits);

  //========================================================================================
  // 12.3 数据有效性检查 - 多线程竞态条件处理
  //========================================================================================
  
  // 多线程环境中的潜在问题：
  // 1. childVisits = 0：节点刚被创建，但首次访问尚未完成
  // 2. childWeight = 0：由于原子变量更新的时序差异，权重可能暂时为零
  // 3. 实时分析场景：函数可能在搜索进行中被调用，数据可能不稳定
  
  //Child visits may be 0 if this function is called in a multithreaded context, such as during live analysis
  //Child weight may also be 0 if it's out of sync.
  
  // 处理策略：如果数据无效，返回权重0
  // 这相当于"完全削减"该着法的权重，将资源分配给其他着法
  if(childVisits <= 0 || childWeight <= 0.0)
    return 0;

  //========================================================================================
  // 12.4 子节点效用精确计算 - 考虑终局得分调整
  //========================================================================================
  
  //Tiny adjustment for passing
  
  // 步骤1：计算终局得分奖励/惩罚
  // getEndingWhiteScoreBonus：分析该着法对白棋最终得分的影响
  // 考虑因素：提子、死活判断、目数计算、贴目规则等
  // 返回值：正数表示对白棋有利，负数表示不利，零表示无特殊影响
  double endingScoreBonus = getEndingWhiteScoreBonus(parent,moveLoc);
  
  // 步骤2：初始化子节点效用为平均效用值
  double childUtility = utilityAvg;
  
  // 步骤3：应用终局得分调整（如果存在）
  if(endingScoreBonus != 0) {
    // getScoreUtilityDiff：将得分差异转换为效用差异
    // 
    // 参数说明：
    // - scoreMeanAvg：当前平均得分
    // - scoreMeanSqAvg：得分平方的平均值（用于计算方差）
    // - endingScoreBonus：得分调整量
    // 
    // 数学原理：ΔUtility = f(ΔScore, σ_score)
    // 其中 σ_score = sqrt(scoreMeanSqAvg - scoreMeanAvg²)
    // 
    // 效果：得分调整会根据当前预测的不确定性进行缩放
    //       如果得分预测很确定（小方差），调整影响较大
    //       如果得分预测不确定（大方差），调整影响较小
    childUtility += getScoreUtilityDiff(scoreMeanAvg, scoreMeanSqAvg, endingScoreBonus);
  }

  //========================================================================================
  // 12.5 理论权重计算 - PUCT逆向求解
  //========================================================================================
  
  // 核心算法：给定目标PUCT值，反推该着法"应该"获得的访问权重
  // 
  // 数学公式求解：
  // 已知：PUCT = V_pla + (exploreScaling * P(a)) / (1 + N(a))
  // 给定：PUCT = bestChildExploreSelectionValue（最佳着法的PUCT值）
  // 求解：N(a) = (exploreScaling * P(a)) / (PUCT - V_pla) - 1
  // 
  // 设计思想：
  // 1. 最佳着法的PUCT值代表当前的"选择标准"
  // 2. 其他着法的权重应该调整到刚好达到这个标准的水平
  // 3. 如果某着法权重过高，说明之前"过度投资"了，应该削减
  // 4. 如果某着法权重合适，则保持不变
  
  // childWeightWeRetrospectivelyWanted：回顾性期望权重
  // "如果我们从一开始就知道最佳PUCT值，该着法应该获得多少权重"
  double childWeightWeRetrospectivelyWanted = getExploreSelectionValueInverse(
    bestChildExploreSelectionValue,   // 目标PUCT值（最佳着法的PUCT值）
    exploreScaling,                   // 探索缩放因子（与正向计算一致）
    nnPolicyProb,                     // 该着法的策略概率
    childUtility,                     // 该着法的调整后效用值
    parent.nextPla                    // 当前玩家（用于效用视角转换）
  );
  
  //========================================================================================
  // 12.6 权重削减决策 - 资源重分配策略
  //========================================================================================
  
  // 核心决策逻辑：比较实际权重与理论权重
  // 
  // 情况1：实际权重 > 理论权重
  // - 表示该着法获得了"过多"的搜索资源
  // - 削减到理论权重水平，释放多余资源
  // - 这些资源可以重新分配给其他更有价值的着法
  if(childWeight > childWeightWeRetrospectivelyWanted)
    return childWeightWeRetrospectivelyWanted;  // 削减权重
  
  // 情况2：实际权重 ≤ 理论权重
  // - 表示该着法的权重分配合理或不足
  // - 保持现有权重，不进行削减
  // - 这确保了不会"误伤"权重合理的着法
  return childWeight;  // 保持原权重
  
  // 总结：
  // 返回值 = min(实际权重, 理论权重)
  // 这种单向削减策略确保了：
  // 1. 过度投资的着法被适度削减
  // 2. 投资不足的着法不会被进一步削减
  // 3. 总体搜索效率得到提升
}

//========================================================================================
// 13. FPU值计算与父节点统计信息生成 - MCTS未探索节点的价值估计
//========================================================================================

// 功能：为未探索的子节点计算FPU（First Play Urgency，首次游戏紧迫性）值
//       同时计算父节点的关键统计信息，用于后续的PUCT计算
// 
// FPU机制的核心思想：
// - 未探索的节点需要有合理的初始价值估计
// - 既不能过高（导致盲目探索），也不能过低（导致有价值的分支被忽略）
// - 基于父节点的统计信息和已访问子节点的策略概率质量进行调整
// 
// 数学原理：
// FPU = U_parent - reduction * sqrt(已访问策略概率质量)
// 其中 reduction 基于探索保守性参数计算
//
// 输出参数（通过引用返回）：
// - parentUtility：父节点效用值
// - parentWeightPerVisit：父节点每次访问的平均权重
// - parentUtilityStdevFactor：父节点效用标准差缩放因子（用于动态探索调整）

double Search::getFpuValueForChildrenAssumeVisited(
  const SearchNode& node,                    // 父节点（即将为其子节点计算FPU值）
  Player pla,                               // 当前玩家
  bool isRoot,                              // 是否为根节点（影响FPU参数选择）
  double policyProbMassVisited,             // 已访问子节点的策略概率总和
  double& parentUtility,                    // 输出：父节点效用值
  double& parentWeightPerVisit,             // 输出：父节点每次访问的平均权重
  double& parentUtilityStdevFactor          // 输出：效用标准差缩放因子
) const {
  
  //========================================================================================
  // 13.1 父节点基础统计信息获取 - 多线程安全的原子操作
  //========================================================================================
  
  // 步骤1：原子获取父节点的核心统计数据
  // visits：父节点被访问的总次数（int64_t，原子变量）
  // 这是计算各种平均值和方差的基础统计量
  int64_t visits = node.stats.visits.load(std::memory_order_acquire);
  
  // weightSum：父节点的累积权重总和（double，原子变量）
  // 用于计算加权平均值，权重可能包含访问质量调整
  double weightSum = node.stats.weightSum.load(std::memory_order_acquire);
  
  // utilityAvg：父节点的平均效用值（double，原子变量）
  // 表示从该节点继续游戏的预期价值（白棋视角）
  double utilityAvg = node.stats.utilityAvg.load(std::memory_order_acquire);
  
  // utilitySqAvg：父节点效用平方的平均值（double，原子变量）
  // 用于计算效用的方差：Var(utility) = E[utility²] - E[utility]²
  double utilitySqAvg = node.stats.utilitySqAvg.load(std::memory_order_acquire);

  //========================================================================================
  // 13.2 数据有效性验证与基础输出参数计算
  //========================================================================================
  
  // 前置条件验证：父节点必须有有效的访问历史
  // 这确保了后续计算的数学意义和数值稳定性
  assert(visits > 0);
  assert(weightSum > 0.0);
  
  // 输出参数1：计算父节点每次访问的平均权重
  // parentWeightPerVisit = 总权重 / 总访问次数
  // 用于估算新访问的权重贡献，在时间管理和剪枝中使用
  parentWeightPerVisit = weightSum / visits;
  
  // 输出参数2：设置父节点效用值
  // 直接使用统计平均值作为父节点的代表性效用
  parentUtility = utilityAvg;
  
  //========================================================================================
  // 13.3 效用方差计算 - 贝叶斯方差估计与数值稳定性处理
  //========================================================================================
  
  // 步骤1：获取先验方差参数
  // cpuctUtilityStdevPrior：效用标准差的先验值（通常为 0.6~1.0）
  // 这是对效用不确定性的先验估计，用于贝叶斯统计
  double variancePrior = searchParams.cpuctUtilityStdevPrior * searchParams.cpuctUtilityStdevPrior;
  
  // cpuctUtilityStdevPriorWeight：先验权重（通常为 1.0~10.0）
  // 控制先验信息在方差估计中的影响强度
  double variancePriorWeight = searchParams.cpuctUtilityStdevPriorWeight;
  
  // 步骤2：计算效用标准差
  double parentUtilityStdev;
  
  // 数据不足时的回退策略：直接使用先验标准差
  // 条件：访问次数太少或权重太小，统计不可靠
  if(visits <= 0 || weightSum <= 1)
    parentUtilityStdev = searchParams.cpuctUtilityStdevPrior;
  else {
    // 数据充足时：使用贝叶斯方差估计
    
    // 步骤2a：计算当前效用的平方
    double utilitySq = parentUtility * parentUtility;
    
    // 步骤2b：数值稳定性保护
    // 防止由于浮点精度问题或多线程同步问题导致的负方差
    // 理论上 E[X²] ≥ E[X]²，但实际计算中可能出现小的违反
    
    //Make sure we're robust to numerical precision issues or threading desync of these values, so we don't observe negative variance
    if(utilitySqAvg < utilitySq)
      utilitySqAvg = utilitySq;  // 强制满足数学约束
    
    // 步骤2c：贝叶斯方差计算
    // 数学公式：
    // Var_posterior = [(μ² + σ²_prior) * w_prior + E[X²] * w_data] / (w_prior + w_data - 1) - μ²
    // 
    // 其中：
    // - μ²：当前效用的平方
    // - σ²_prior：先验方差
    // - w_prior：先验权重
    // - E[X²]：观测到的效用平方的期望
    // - w_data：数据权重（weightSum）
    // 
    // 设计原理：
    // - 结合先验知识和观测数据
    // - 先验权重控制对历史经验的信任程度
    // - 数据权重反映当前观测的可靠性
    parentUtilityStdev = sqrt(
      std::max(
        0.0,  // 确保非负（数值稳定性的最后防线）
        ((utilitySq + variancePrior) * variancePriorWeight + utilitySqAvg * weightSum)
        / (variancePriorWeight + weightSum - 1.0)
        - utilitySq
      )
    );
  }
  
  //========================================================================================
  // 13.4 动态探索缩放因子计算 - 方差自适应探索强度
  //========================================================================================
  
  // 输出参数3：计算效用标准差缩放因子
  // 数学公式：
  // parentUtilityStdevFactor = 1 + scale * (σ_observed / σ_prior - 1)
  // 
  // 参数解释：
  // - σ_observed：当前观测到的效用标准差
  // - σ_prior：先验效用标准差
  // - scale：缩放系数（cpuctUtilityStdevScale，通常为 0.4~1.0）
  // 
  // 设计思想：
  // - 当 σ_observed > σ_prior 时，factor > 1，增强探索（局面更复杂/不确定）
  // - 当 σ_observed < σ_prior 时，factor < 1，减少探索（局面更确定）
  // - 当 σ_observed = σ_prior 时，factor = 1，保持标准探索强度
  // 
  // 实际效果：
  // - 复杂位置（高方差）：更积极探索未知分支
  // - 简单位置（低方差）：更保守，依赖已知评估
  parentUtilityStdevFactor = 1.0 + searchParams.cpuctUtilityStdevScale * (parentUtilityStdev / searchParams.cpuctUtilityStdevPrior - 1.0);

  //========================================================================================
  // 13.5 FPU计算的父节点效用调整 - 混合神经网络与搜索统计
  //========================================================================================
  
  // 步骤1：初始化FPU计算用的父节点效用
  // 默认使用搜索统计的平均效用
  double parentUtilityForFPU = parentUtility;
  
  // 步骤2：基于已访问策略的动态权重调整
  if(searchParams.fpuParentWeightByVisitedPolicy) {
    // 动态权重计算：根据已访问的策略概率质量调整混合比例
    // 数学公式：avgWeight = min(1.0, (已访问策略质量)^幂次)
    // 
    // 设计思想：
    // - 当已访问的策略概率质量高时，更信任搜索统计
    // - 当已访问的策略概率质量低时，更依赖神经网络初始评估
    // - 幂次参数控制这种依赖关系的非线性程度
    double avgWeight = std::min(1.0, pow(policyProbMassVisited, searchParams.fpuParentWeightByVisitedPolicyPow));
    
    // 混合公式：U_mixed = w * U_search + (1-w) * U_nn
    // 其中：
    // - U_search：搜索统计的效用（parentUtility）
    // - U_nn：神经网络的直接效用评估
    // - w：动态权重（avgWeight）
    parentUtilityForFPU = avgWeight * parentUtility + (1.0 - avgWeight) * getUtilityFromNN(*(node.getNNOutput()));
  }
  // 步骤3：固定权重的静态混合
  else if(searchParams.fpuParentWeight > 0.0) {
    // 固定权重混合：U_mixed = w_fixed * U_nn + (1-w_fixed) * U_search
    // fpuParentWeight：固定的神经网络权重（通常为 0.0~0.5）
    // 
    // 使用场景：
    // - 当搜索统计可能不够稳定时
    // - 需要保持对神经网络评估的一定信任度
    parentUtilityForFPU = searchParams.fpuParentWeight * getUtilityFromNN(*(node.getNNOutput())) + (1.0 - searchParams.fpuParentWeight) * parentUtility;
  }

  //========================================================================================
  // 13.6 最终FPU值计算 - 探索保守性与损失倾向调整
  //========================================================================================
  
  double fpuValue;
  {
    // 步骤1：获取FPU参数（根据是否为根节点选择不同参数）
    // fpuReductionMax：最大FPU削减量
    // - 根节点：rootFpuReductionMax（通常更保守，0.04~0.2）
    // - 非根节点：fpuReductionMax（通常更激进，0.1~0.5）
    double fpuReductionMax = isRoot ? searchParams.rootFpuReductionMax : searchParams.fpuReductionMax;
    
    // fpuLossProp：损失倾向比例
    // - 控制FPU值向"极端损失"方向偏移的程度（通常为 0.0~0.4）
    // - 目的：让未探索节点稍微不那么吸引人，避免过度乐观
    double fpuLossProp = isRoot ? searchParams.rootFpuLossProp : searchParams.fpuLossProp;
    
    // utilityRadius：效用值的理论最大范围
    // 计算公式：R = winLoss + staticScore + dynamicScore
    // 这代表了从最优到最劣局面的效用差异范围（通常为 2.0~3.0）
    double utilityRadius = searchParams.winLossUtilityFactor + searchParams.staticScoreUtilityFactor + searchParams.dynamicScoreUtilityFactor;

    // 步骤2：计算基础FPU削减量
    // 数学公式：reduction = max_reduction * sqrt(已访问策略概率质量)
    // 
    // 设计原理：
    // - 已访问的策略概率质量越高，对未访问节点的估计越保守
    // - 平方根函数确保削减量不会过度激进
    // - 这避免了在已经充分探索高质量着法后，盲目探索低质量着法
    double reduction = fpuReductionMax * sqrt(policyProbMassVisited);
    
    // 步骤3：应用玩家视角的FPU调整
    // 基础FPU公式：FPU = U_parent ± reduction
    // - 白棋视角：FPU = parentUtilityForFPU - reduction（更保守）
    // - 黑棋视角：FPU = parentUtilityForFPU + reduction（更保守）
    // 
    // 逻辑：每个玩家对未探索分支都采用稍微悲观的估计
    fpuValue = pla == P_WHITE ? parentUtilityForFPU - reduction : parentUtilityForFPU + reduction;
    
    // 步骤4：损失倾向调整（最终的保守性修正）
    // lossValue：对当前玩家极其不利的效用值
    // - 白棋：lossValue = -utilityRadius（最大损失）
    // - 黑棋：lossValue = +utilityRadius（最大损失）
    double lossValue = pla == P_WHITE ? -utilityRadius : utilityRadius;
    
    // 最终FPU公式：FPU_final = FPU + (lossValue - FPU) * fpuLossProp
    // 等价于：FPU_final = FPU * (1 - fpuLossProp) + lossValue * fpuLossProp
    // 
    // 效果：将FPU值向极端损失方向偏移，增加探索的保守性
    // - fpuLossProp = 0：无额外调整
    // - fpuLossProp = 1：FPU = 极端损失（完全悲观）
    // - fpuLossProp ∈ (0,1)：在基础FPU和极端损失之间插值
    fpuValue = fpuValue + (lossValue - fpuValue) * fpuLossProp;
  }

  // 返回最终计算的FPU值
  // 此值将被用作所有未探索子节点的初始效用估计
  return fpuValue;
}


//========================================================================================
// 14. 最佳子节点选择函数 - MCTS搜索的核心决策引擎
//========================================================================================

// 功能：在MCTS搜索过程中，从当前节点的所有可能子节点中选择最优的一个进行下降
// 这是整个搜索算法的核心，决定了搜索树的扩展方向和搜索效率
// 
// 主要职责：
// 1. 遍历所有已存在的子节点，计算其PUCT选择值
// 2. 考虑最优的未创建子节点（基于策略概率）
// 3. 处理人类SL网络的混合搜索策略
// 4. 应用各种搜索优化（反镜像、剪枝、特殊规则等）
// 5. 返回最终选择的子节点信息
//
// 输入参数：
// - thread：搜索线程（包含随机数生成器、棋盘状态等）
// - node：当前父节点
// - nodeState：节点状态（用于并发控制）
// - isRoot：是否为根节点（影响某些优化策略）
//
// 输出参数（通过引用返回）：
// - numChildrenFound：找到的子节点总数
// - bestChildIdx：最佳子节点的索引
// - bestChildMoveLoc：最佳子节点对应的着法位置
// - countEdgeVisit：是否计算边访问（影响权重统计方式）

void Search::selectBestChildToDescend(
  SearchThread& thread,               // 搜索线程
  const SearchNode& node,             // 当前父节点
  SearchNodeState nodeState,          // 节点状态（并发控制）
  int& numChildrenFound,              // 输出：找到的子节点数量
  int& bestChildIdx,                  // 输出：最佳子节点索引
  Loc& bestChildMoveLoc,              // 输出：最佳着法位置
  bool& countEdgeVisit,               // 输出：是否计算边访问
  bool isRoot                         // 是否为根节点
) const {
  
  //========================================================================================
  // 14.1 前置条件验证与初始化
  //========================================================================================
  
  // 验证线程状态与节点状态的一致性
  // 确保当前轮到的玩家与节点预期的下一步玩家一致
  assert(thread.pla == node.nextPla);

  // 初始化输出变量
  double maxSelectionValue = POLICY_ILLEGAL_SELECTION_VALUE;  // 当前最大PUCT值（初始为极小值）
  bestChildIdx = -1;                                         // 最佳子节点索引（-1表示未找到）
  bestChildMoveLoc = Board::NULL_LOC;                        // 最佳着法位置（NULL表示未确定）
  countEdgeVisit = true;                                     // 默认计算边访问（正常MCTS模式）

  //========================================================================================
  // 14.2 子节点容器获取与神经网络输出准备
  //========================================================================================
  
  // 获取当前节点的子节点容器引用（线程安全）
  // ConstSearchNodeChildrenReference：提供对子节点的只读访问，支持并发
  ConstSearchNodeChildrenReference children = node.getChildren(nodeState);
  
  // childrenCapacity：子节点容器的最大容量
  // 这是预分配的数组大小，实际子节点数量可能少于此值
  int childrenCapacity = children.getCapacity();

  //========================================================================================
  // 14.3 第一轮扫描：统计已存在子节点的基础信息
  //========================================================================================
  
  // 初始化统计变量
  double policyProbMassVisited = 0.0;    // 已访问子节点的策略概率总和
  double maxChildWeight = 0.0;           // 最大子节点权重（用于剪枝判断）
  double totalChildWeight = 0.0;         // 所有子节点权重总和（用于探索缩放）
  int64_t totalChildEdgeVisits = 0;      // 所有边访问次数总和
  
  // 获取神经网络输出
  const NNOutput* nnOutput = node.getNNOutput();
  assert(nnOutput != NULL);  // 确保神经网络已经评估过此节点
  
  // 获取策略概率数组（可能包含噪声）
  // getPolicyProbsMaybeNoised()：如果启用了噪声，返回加噪后的概率
  const float* policyProbs = nnOutput->getPolicyProbsMaybeNoised();
  
  // 第一轮遍历：收集已存在子节点的统计信息
  for(int i = 0; i<childrenCapacity; i++) {
    // 获取子节点指针
    const SearchChildPointer& childPointer = children[i];
    
    // 安全获取子节点实例
    // getIfAllocated()：如果已分配返回指针，否则返回NULL
    const SearchNode* child = childPointer.getIfAllocated();
    
    // 遇到第一个未分配位置，终止遍历
    // 子节点是连续分配的，NULL表示后续都未使用
    if(child == NULL)
      break;
    
    // 获取该子节点对应的着法位置
    Loc moveLoc = childPointer.getMoveLocRelaxed();
    
    // 将着法位置转换为策略数组索引
    int movePos = getPos(moveLoc);
    
    // 获取该着法的神经网络策略概率
    float nnPolicyProb = policyProbs[movePos];
    
    // 跳过非法着法（策略概率为负值）
    if(nnPolicyProb < 0)
      continue;
    
    // 累积已访问的策略概率质量
    // 这个值用于FPU计算和探索强度调节
    policyProbMassVisited += nnPolicyProb;

    // 获取边访问统计
    int64_t edgeVisits = childPointer.getEdgeVisits();
    
    // 计算子节点权重
    // getChildWeight()：将访问次数转换为权重值
    double childWeight = child->stats.getChildWeight(edgeVisits);

    // 更新统计信息
    totalChildWeight += childWeight;
    if(childWeight > maxChildWeight)
      maxChildWeight = childWeight;
    totalChildEdgeVisits += edgeVisits;
  }

  //========================================================================================
  // 14.4 人类SL网络混合策略处理 - 可选的双网络系统
  //========================================================================================
  
  bool useHumanSL = false;  // 是否使用人类监督学习网络
  
  // 检查是否启用了人类SL网络
  if(humanEvaluator != NULL &&
     (searchParams.humanSLProfile.initialized || !humanEvaluator->requiresSGFMetadata())
  ) {
    // 获取人类网络的输出
    const NNOutput* humanOutput = node.getHumanOutput();
    
    if(humanOutput != NULL) {
      // 根据节点类型和玩家确定使用人类网络的概率
      double weightlessProb;  // 无权重模式概率（不计算边访问）
      double weightfulProb;   // 有权重模式概率（正常计算边访问）
      
      if(isRoot) {
        // 根节点：使用根节点专用参数
        weightlessProb = searchParams.humanSLRootExploreProbWeightless;
        weightfulProb = searchParams.humanSLRootExploreProbWeightful;
      }
      else if(thread.pla == rootPla) {
        // 非根节点，己方回合：使用己方参数
        weightlessProb = searchParams.humanSLPlaExploreProbWeightless;
        weightfulProb = searchParams.humanSLPlaExploreProbWeightful;
      }
      else {
        // 非根节点，对方回合：使用对方参数
        weightlessProb = searchParams.humanSLOppExploreProbWeightless;
        weightfulProb = searchParams.humanSLOppExploreProbWeightful;
      }

      // 计算使用人类网络的总概率
      double totalHumanProb = weightlessProb + weightfulProb;
      
      if(totalHumanProb > 0.0) {
        // 随机决定是否使用人类网络
        double r = thread.rand.nextDouble();
        
        if(r < weightlessProb) {
          // 使用人类网络，无权重模式
          useHumanSL = true;
          countEdgeVisit = false;  // 不计算边访问，使用节点权重
        }
        else if(r < totalHumanProb) {
          // 使用人类网络，有权重模式
          useHumanSL = true;
          // countEdgeVisit保持true
        }
      }
    }

    // 如果决定使用人类网络，切换策略源并重新计算统计
    if(useHumanSL) {
      nnOutput = humanOutput;           // 切换到人类网络输出
      policyProbMassVisited = 0.0;      // 重置统计
      assert(nnOutput != NULL);
      
      // 获取人类网络的策略概率
      policyProbs = nnOutput->getPolicyProbsMaybeNoised();
      
      // 重新计算已访问的策略概率质量（基于人类网络）
      for(int i = 0; i<childrenCapacity; i++) {
        const SearchChildPointer& childPointer = children[i];
        const SearchNode* child = childPointer.getIfAllocated();
        if(child == NULL)
          break;
        
        Loc moveLoc = childPointer.getMoveLocRelaxed();
        int movePos = getPos(moveLoc);
        float nnPolicyProb = policyProbs[movePos];
        
        if(nnPolicyProb < 0)
          continue;
        
        policyProbMassVisited += nnPolicyProb;
      }
    }
  }

  //========================================================================================
  // 14.5 数据一致性验证
  //========================================================================================
  
  // 验证策略概率总和的合理性
  // 理论上所有合法着法的概率之和应该 ≤ 1.0
  // 给出慷慨的浮点误差容忍度
  
  //Probability mass should not sum to more than 1, giving a generous allowance
  //for floating point error.
  assert(policyProbMassVisited <= 1.0001);

  //========================================================================================
  // 14.6 权重模式调整 - 处理无权重访问
  //========================================================================================
  
  // 如果使用无权重模式，重新计算权重统计
  // 无权重模式：使用节点内部权重，而不是边访问权重
  // 主要用于人类网络的特殊搜索模式
  
  //If we're doing a weightless visit, then we should redo PUCT to operate on child node weight, not child edge weight
  if(!countEdgeVisit) {
    totalChildWeight = 0.0;
    maxChildWeight = 0.0;
    
    // 重新遍历，使用节点内部权重
    for(int i = 0; i<childrenCapacity; i++) {
      const SearchChildPointer& childPointer = children[i];
      const SearchNode* child = childPointer.getIfAllocated();
      if(child == NULL)
        break;
      
      // 使用节点的累积权重总和，而不是边权重
      double childWeight = child->stats.weightSum.load(std::memory_order_acquire);
      
      totalChildWeight += childWeight;
      if(childWeight > maxChildWeight)
        maxChildWeight = childWeight;
    }
  }

  //========================================================================================
  // 14.7 FPU值计算与探索参数准备
  //========================================================================================
  
  // 计算未探索子节点的FPU（First Play Urgency）值
  // 同时获得父节点的关键统计信息
  
  //First play urgency
  double parentUtility;              // 父节点效用值
  double parentWeightPerVisit;       // 父节点每次访问的平均权重
  double parentUtilityStdevFactor;   // 效用标准差缩放因子
  
  double fpuValue = getFpuValueForChildrenAssumeVisited(
    node,                    // 当前节点
    thread.pla,              // 当前玩家
    isRoot,                  // 是否为根节点
    policyProbMassVisited,   // 已访问策略概率质量
    parentUtility,           // 输出：父节点效用
    parentWeightPerVisit,    // 输出：每次访问权重
    parentUtilityStdevFactor // 输出：标准差缩放因子
  );

  // 初始化位置标记数组，记录哪些位置已有子节点
  // 用于避免重复考虑同一个位置
  bool posesWithChildBuf[NNPos::MAX_NN_POLICY_SIZE] = { }; // Initialize all to false
  
  // 检查是否启用反镜像策略
  // 条件：启用反镜像 && 有镜像玩家 && 自搜索开始以来一直处于镜像状态
  bool antiMirror = searchParams.antiMirror && mirroringPla != C_EMPTY && isMirroringSinceSearchStart(thread.history,0);

  // 计算探索缩放因子
  double exploreScaling;
  if(useHumanSL)
    // 人类网络使用专用的探索缩放计算
    exploreScaling = getExploreScalingHuman(totalChildWeight);
  else
    // 标准AI网络使用动态方差调节的探索缩放
    exploreScaling = getExploreScaling(totalChildWeight, parentUtilityStdevFactor);

  //========================================================================================
  // 14.8 已存在子节点的PUCT计算与选择
  //========================================================================================
  
  // 遍历所有已存在的子节点，计算其PUCT选择值
  
  //Try all existing children
  //Also count how many children we actually find
  numChildrenFound = 0;
  
  for(int i = 0; i<childrenCapacity; i++) {
    const SearchChildPointer& childPointer = children[i];
    const SearchNode* child = childPointer.getIfAllocated();
    if(child == NULL)
      break;
    
    numChildrenFound++;  // 统计实际找到的子节点数量
    
    // 获取边访问统计
    int64_t childEdgeVisits = childPointer.getEdgeVisits();

    // 获取着法位置
    Loc moveLoc = childPointer.getMoveLocRelaxed();
    
    // 标记此位置已有子节点
    posesWithChildBuf[getPos(moveLoc)] = true;
    
    // 计算该子节点的PUCT选择值
    bool isDuringSearch = true;  // 标记这是搜索过程中的计算
    
    double selectionValue = getExploreSelectionValueOfChild(
      node,                    // 父节点
      policyProbs,             // 策略概率数组
      child,                   // 当前子节点
      moveLoc,                 // 着法位置
      exploreScaling,          // 探索缩放因子
      totalChildWeight,        // 总子节点权重
      childEdgeVisits,         // 边访问次数
      fpuValue,                // FPU值
      parentUtility,           // 父节点效用
      parentWeightPerVisit,    // 父节点权重/访问比
      isDuringSearch,          // 是否在搜索中
      antiMirror,              // 是否启用反镜像
      maxChildWeight,          // 最大子节点权重
      countEdgeVisit,          // 是否计算边访问
      &thread                  // 搜索线程
    );
    
    // 更新最佳选择
    if(selectionValue > maxSelectionValue) {
      // 可选的评估状态惩罚（已注释）
      // 用于处理正在评估的节点，避免多线程冲突
      // if(child->state.load(std::memory_order_seq_cst) == SearchNode::STATE_EVALUATING) {
      //   selectionValue -= EVALUATING_SELECTION_VALUE_PENALTY;
      //   if(isRoot && child->prevMoveLoc == Location::ofString("K4",thread.board)) {
      //     out << "ouch" << "\n";
      //   }
      // }
      
      maxSelectionValue = selectionValue;
      bestChildIdx = i;
      bestChildMoveLoc = moveLoc;
    }
  }

  //========================================================================================
  // 14.9 避免着法检查 - 临时禁用某些着法
  //========================================================================================
  
  // 获取当前玩家的避免着法列表
  // 这是一个临时机制，可以在特定深度内禁用某些着法
  const std::vector<int>& avoidMoveUntilByLoc = thread.pla == P_BLACK ? avoidMoveUntilByLocBlack : avoidMoveUntilByLocWhite;

  //========================================================================================
  // 14.10 新子节点候选搜索 - 寻找最佳未创建子节点
  //========================================================================================
  
  // 在所有未创建的子节点中，找到策略概率最高的合法着法
  
  //Try the new child with the best policy value
  Loc bestNewMoveLoc = Board::NULL_LOC;        // 最佳新着法位置
  float bestNewNNPolicyProb = -1.0f;           // 最佳新着法的策略概率
  
  // 遍历所有可能的着法位置
  for(int movePos = 0; movePos<policySize; movePos++) {
    // 跳过已有子节点的位置
    bool alreadyTried = posesWithChildBuf[movePos];
    if(alreadyTried)
      continue;

    // 将策略数组索引转换为棋盘坐标
    Loc moveLoc = NNPos::posToLoc(movePos,thread.board.x_size,thread.board.y_size,nnXLen,nnYLen);
    if(moveLoc == Board::NULL_LOC)
      continue;

    //========================================================================================
    // 14.10.1 根节点特殊限制检查
    //========================================================================================
    
    //Special logic for the root
    if(isRoot) {
      // 确保棋盘状态与根节点一致
      assert(thread.board.pos_hash == rootBoard.pos_hash);
      assert(thread.pla == rootPla);
      
      // 检查是否为允许的根节点着法
      // isAllowedRootMove()：用户可能通过接口限制根节点的可选着法
      if(!isAllowedRootMove(moveLoc))
        continue;
    }
    
    //========================================================================================
    // 14.10.2 临时避免着法检查
    //========================================================================================
    
    // 检查是否需要临时避免此着法
    if(avoidMoveUntilByLoc.size() > 0) {
      assert(avoidMoveUntilByLoc.size() >= Board::MAX_ARR_SIZE);
      
      // 获取该位置的避免深度
      int untilDepth = avoidMoveUntilByLoc[moveLoc];
      
      // 如果当前深度小于避免深度，跳过此着法
      if(thread.history.moveHistory.size() - rootHistory.moveHistory.size() < untilDepth)
        continue;
    }

    //========================================================================================
    // 14.10.3 合法性检查与反镜像处理
    //========================================================================================
    
    // 获取该着法的策略概率
    float nnPolicyProb = policyProbs[movePos];
    
    //Quit immediately for illegal moves
    // 跳过非法着法（负概率）
    if(nnPolicyProb < 0)
      continue;

    // 应用反镜像策略调整
    if(antiMirror) {
      // 可能会修改nnPolicyProb的值
      maybeApplyAntiMirrorPolicy(nnPolicyProb, moveLoc, policyProbs, node.nextPla, &thread);
    }

    // 更新最佳新着法
    // 新节点的PUCT值排序完全等同于策略概率排序！利用新节点PUCT值与策略概率的单调关系
    if(nnPolicyProb > bestNewNNPolicyProb) {
      bestNewNNPolicyProb = nnPolicyProb;
      bestNewMoveLoc = moveLoc;
    }
  }
  
  //========================================================================================
  // 14.11 新子节点PUCT计算与比较
  //========================================================================================
  
  // 如果找到了合法的新着法候选，计算其PUCT值
  if(bestNewMoveLoc != Board::NULL_LOC) {
    double selectionValue = getNewExploreSelectionValue(
      node,                    // 父节点
      exploreScaling,          // 探索缩放因子
      bestNewNNPolicyProb,     // 新着法的策略概率
      fpuValue,                // FPU值（新节点的初始效用）
      parentWeightPerVisit,    // 父节点权重/访问比
      maxChildWeight,          // 最大子节点权重
      countEdgeVisit,          // 是否计算边访问
      &thread                  // 搜索线程
    );
    
    // 与已存在子节点比较，更新最佳选择
    if(selectionValue > maxSelectionValue) {
      maxSelectionValue = selectionValue;
      bestChildIdx = numChildrenFound;  // 新节点的索引
      bestChildMoveLoc = bestNewMoveLoc;
    }
  }

  //========================================================================================
  // 14.12 特殊传球处理 - 终局阶段的传球策略
  //========================================================================================
  
  // 在满足特定条件时，强制考虑传球着法
  // 这是围棋特有的终局处理机制
  if(totalChildEdgeVisits >= 2 && searchParams.enableMorePassingHacks && thread.history.passWouldEndPhase(thread.board,thread.pla)) {
    // 统计现有子节点中的传球和非传球着法
    bool hasPassMove = false;     // 是否已有传球子节点
    bool hasNonPassMove = false;  // 是否已有非传球子节点
    
    for(int i = 0; i<childrenCapacity; i++) {
      const SearchChildPointer& childPointer = children[i];
      const SearchNode* child = childPointer.getIfAllocated();
      if(child == NULL)
        break;
      
      Loc moveLoc = childPointer.getMoveLocRelaxed();
      if(moveLoc == Board::PASS_LOC)
        hasPassMove = true;
      else
        hasNonPassMove = true;
    }
    
    // 情况1：没有传球着法，且当前最佳不是传球，强制选择传球
    if(!hasPassMove && bestChildMoveLoc != Board::PASS_LOC) {
      bestChildIdx = numChildrenFound;
      bestChildMoveLoc = Board::PASS_LOC;
      countEdgeVisit = false;  // 特殊模式，不计算边访问
      
      // 这些特殊的传球搜索不计入访问/游戏限制
      thread.shouldCountPlayout = false;
    }
    // 情况2：只有传球着法，且当前最佳是传球，但有更好的非传球候选
    else if(!hasNonPassMove && bestChildMoveLoc == Board::PASS_LOC && bestNewMoveLoc != Board::PASS_LOC) {
      bestChildIdx = numChildrenFound;
      bestChildMoveLoc = bestNewMoveLoc;
      countEdgeVisit = false;  // 特殊模式，不计算边访问
      
      // 这些特殊的传球搜索不计入访问/游戏限制
      thread.shouldCountPlayout = false;
    }
  }

  //========================================================================================
  // 14.13 函数完成
  //========================================================================================
  
  // 至此，函数完成了最佳子节点的选择
  // 返回的信息包括：
  // - bestChildIdx：最佳子节点在容器中的索引
  // - bestChildMoveLoc：最佳着法的棋盘位置
  // - numChildrenFound：总共找到的子节点数量
  // - countEdgeVisit：访问统计模式
  // 
  // 这些信息将被用于：
  // 1. 如果选择了已存在子节点：直接下降到该节点
  // 2. 如果选择了新子节点：创建新节点然后下降
  // 3. 更新相应的访问统计
}
