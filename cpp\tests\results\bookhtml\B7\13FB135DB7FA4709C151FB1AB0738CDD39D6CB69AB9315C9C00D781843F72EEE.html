
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 4;
const board = [0,2,1,0,0,0,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,2],],'p':0.3575,'wl':0.1521,'ssM':-0.77,'wlRad':0.0774,'sRad':0.95,'v':103,'av':103,},{'xy':[[0,0],],'p':0.6653,'wl':0.0852,'ssM':0.66,'wlRad':0.0262,'sRad':1.24,'v':98,'av':98,},{'xy':[[1,1],],'p':0.7095,'wl':0.6143,'ssM':-0.17,'wlRad':0.0892,'sRad':0.74,'v':100,'av':100,},{'move':'other','p':0.0133,'wl':-0.1592,'ssM':-0.71,'wlRad':0.0225,'sRad':1.74,'v':18,'av':18,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
