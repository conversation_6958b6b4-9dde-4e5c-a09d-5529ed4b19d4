
DESCRIPTION: 

   Play different nets against each other with different search settings in
   a match or tournament.

USAGE: 

   match  -config <FILE> ...  [-log-file <FILE>] [-sgf-output-dir <DIR>]
          [...other flags...]


Where: 

   -config <FILE>  (accepted multiple times)
     (required)  Config file(s) to use, can be one or multiple files (see
     match_example.cfg or configs/match_example.cfg).

   -log-file <FILE>
     Log file to output to

   -sgf-output-dir <DIR>
     Dir to output sgf files

   -override-config <KEYVALUEPAIRS>  (accepted multiple times)
     Override config parameters. Format: "key=value, key=value,..."

   --,  -ignore_rest
     Ignores the rest of the labeled arguments following this flag.

   -version
     Displays version information and exits.

   -h,  -help
     Displays usage information and exits.



