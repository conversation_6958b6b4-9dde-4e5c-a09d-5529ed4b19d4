
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 2;
const pLink = '../56/417A1A4156ACBF58E79325D34F2A235DE4B1D62137F92AB42E507F3041D986CC.html';
const pSym = 6;
const board = [0,0,0,0,1,1,0,0,0,0,0,0,2,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,0],],'p':0.0145,'wl':0.5151,'ssM':-0.06,'wlRad':0.0366,'sRad':0.50,'v':121,'av':121,},{'xy':[[2,2],],'p':0.8717,'wl':0.2332,'ssM':-0.02,'wlRad':0.0716,'sRad':0.91,'v':85,'av':85,},{'xy':[[1,0],],'p':0.4977,'wl':-0.1630,'ssM':-0.57,'wlRad':0.0688,'sRad':1.00,'v':113,'av':113,},{'move':'other','p':0.1708,'wl':0.0212,'ssM':0.54,'wlRad':0.0525,'sRad':1.64,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
