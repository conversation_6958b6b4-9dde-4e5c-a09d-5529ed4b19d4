
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 2;
const board = [2,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,];
const links = {2:'../F8/2CBEAE01F8379B94767891CB8FAF9E4E8A83A111CB910265346815B4DFD4ECC3.html',};
const linkSyms = {2:5,};
const moves = [{'xy':[[0,3],],'p':0.0909,'wl':0.0191,'ssM':0.38,'wlRad':0.0377,'sRad':0.64,'v':108,'av':108,},{'xy':[[0,1],],'p':0.4033,'wl':0.2810,'ssM':0.31,'wlRad':0.1792,'sRad':0.68,'v':91,'av':91,},{'xy':[[3,1],],'p':0.1432,'wl':0.3524,'ssM':0.26,'wlRad':0.0091,'sRad':0.95,'v':149,'av':149,},{'xy':[[2,0],],'p':0.3394,'wl':0.5151,'ssM':0.91,'wlRad':0.0366,'sRad':1.03,'v':330,'av':330,},{'xy':[[1,2],],'p':0.0074,'wl':0.7703,'ssM':0.25,'wlRad':0.1382,'sRad':0.65,'v':102,'av':102,},{'move':'other','p':0.0381,'wl':0.2145,'ssM':0.48,'wlRad':0.1217,'sRad':1.23,'v':12,'av':12,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
