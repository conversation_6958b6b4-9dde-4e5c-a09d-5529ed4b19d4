# KataGo完整MCGS改进方法数学公式总结（最终版）

## 1. 核心理论基础

### 1.1 MCTS作为正则化策略优化
MCTS的访问分布近似求解：
$$\pi^* = \arg\max_\pi \left[ \sum_a \pi(a) Q(a) - \lambda_N D_{\text{KL}}(P \| \pi) \right]$$

### 1.2 图搜索的关键分离
**核心洞察**：分离边访问次数和节点访问次数
- $N(n,a)$：从节点$n$选择动作$a$的次数（边访问，用于策略优化）
- $N(n)$：节点$n$的总访问次数（用于正则化）
- 关系：$N(n) = 1 + \sum_a N(n,a)$

## 2. KataGo论文中的MCTS改进

### 2.1 首次游戏紧迫性(FPU)
对于未访问的子节点（$N(a) = 0$）：
$$Q(n,a) = Q(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}}$$

其中：
- $P_{\text{explored}} = \sum_{b: N(n,b)>0} P_{\text{final}}(n,b)$
- $c_{\text{FPU}} = 0.2$（非根节点），$c_{\text{FPU}} = 0$（根节点启用噪声时）

### 2.2 强制探索约束
当满足以下条件时，设置选择优先级为无穷大：
$$N(n,a) < \left(k \cdot P_{\text{final}}(n,a) \sum_{b} N(n,b)\right)^{1/2}$$
其中$k = 2$

### 2.3 综合效用函数
$$U_{\text{total}} = u_{\text{win}}(x) + u_{\text{score}}(x)$$

其中：
$$u_{\text{win}}(x) = \text{sign}(x) \in \{-1, 1\}$$
$$u_{\text{score}}(x) = c_{\text{score}} \frac{2}{\pi} \arctan\left(\frac{x - x_0}{b}\right)$$

得分效用期望：
$$E[u_{\text{score}}] = \int_{-\infty}^{\infty} u_{\text{score}}(x) \mathcal{N}(x; \hat{\mu}_s, \hat{\sigma}_s^2) dx$$

参数：$c_{\text{score}} = 0.4$，$x_0 = \hat{\mu}_s$（根节点预测得分均值）

## 3. KataGo文档中的高级MCTS改进

### 3.1 形状化Dirichlet噪声
$$\alpha_a^{\text{shaped}} = \alpha_{\text{uniform}} + \alpha_{\text{shaped}} \cdot \max(0, \log P_{\text{temp}}^{\text{opt}}(a) - \log P_{\text{threshold}})$$

其中：
- $\alpha_{\text{uniform}} = \frac{\alpha_{\text{total}}}{2 \cdot |\text{legal moves}|}$
- $\alpha_{\text{shaped}} = \frac{\alpha_{\text{total}}}{2 \cdot \sum_b \max(0, \log P_{\text{temp}}^{\text{opt}}(b) - \log P_{\text{threshold}})}$
- $\alpha_{\text{total}} = 10.83$，$P_{\text{threshold}} = 0.02$

### 3.2 根节点策略Softmax温度
$$P_{\text{temp}}^{\text{opt}}(a) = \frac{\exp(\text{logit}_{\text{opt}}(a)/T)}{\sum_b \exp(\text{logit}_{\text{opt}}(b)/T)}$$

温度衰减：
$$T = T_{\text{early}} \cdot \exp\left(-\frac{\text{turn}}{\text{board\_size}}\right) + T_{\text{late}}$$
参数：$T_{\text{early}} = 1.25$，$T_{\text{late}} = 1.1$

### 3.3 子树价值偏差纠正
偏差纠正的节点效用：
$$\text{NodeUtility}(n) = \text{NNUtility}(n) - \lambda \cdot \text{ObsBias}(B(n))$$

桶偏差：
$$\text{ObsBias}(B) = \frac{\sum_{n \in B} \text{ObsError}(n) \cdot \text{ChildVisits}(n)^\alpha}{\sum_{n \in B} \text{ChildVisits}(n)^\alpha}$$

观测误差：
$$\text{ObsError}(n) = \text{NNUtility}(n) - \frac{\sum_c \text{MCTSUtility}(c) \cdot \text{Visits}(c)}{\sum_c \text{Visits}(c)}$$

参数：$\lambda = 0.35$，$\alpha = 0.8$

### 3.4 动态方差缩放cPUCT
$$c_{\text{PUCT}}(n) = c_{\text{base}} \cdot \sqrt{\text{Var}_{\text{utility}}(n) + \epsilon_{\text{var}}}$$

效用方差：
$$\text{Var}_{\text{utility}}(n) = \frac{\sum_{i=1}^{N(n)} w_i (V_i^{\text{corrected}} - Q_{\text{weighted}}(n))^2}{\sum_{i=1}^{N(n)} w_i}$$

参数：$c_{\text{base}} = 1.1$，$\epsilon_{\text{var}} = 0.25$

### 3.5 不确定性加权
不确定性权重：
$$w_i = \frac{1}{\max(\epsilon_{\text{unc}}, \sqrt{\text{UncertaintyPred}_{\text{value}}^{(i)} + \text{UncertaintyPred}_{\text{score}}^{(i)}})}$$

其中：$\epsilon_{\text{unc}} = 0.01$

### 3.6 乐观策略
乐观策略训练权重：
$$w_{\text{opt}} = \text{clamp}(0, 1, \text{sigmoid}((z_{\text{value}} - 1.5) \times 3) + \text{sigmoid}((z_{\text{score}} - 1.5) \times 3))$$

标准化惊喜度：
$$z_{\text{value}} = \frac{\text{shortterm\_value\_outcome} - \text{shortterm\_value\_pred}}{\sqrt{\text{shortterm\_value\_error\_pred} + \epsilon}}$$

## 4. KataGo最终完整MCGS选择公式

### 4.1 核心选择公式（基于MCGS）
$$\boxed{
\text{Next action} = \arg\max_a \left[ Q_{\text{weighted}}(n,a) + c_{\text{dynamic}}(n) \cdot P_{\text{final}}(n,a) \frac{\sqrt{\sum_b N(n,b)}}{1 + N(n,a)} \right]
}$$

**关键改进**：使用边访问次数$N(n,a)$而非子节点访问次数$N(\text{child})$

### 4.2 加权价值函数$Q_{\text{weighted}}(n,a)$

#### 4.2.1 基础递归定义（来自MCGS理论）
$$Q(n) = \frac{1}{N(n)} \left( U(n) + \sum_a N(n,a) Q(n,a) \right)$$

其中$Q(n,a) = Q_{\text{weighted}}(\text{child}(n,a))$

#### 4.2.2 完整的加权价值（整合所有改进）
$$Q_{\text{weighted}}(n,a) = \begin{cases}
\frac{\sum_{i=1}^{N_{\text{child}}(a)} w_i \cdot V_i^{\text{total}}}{\sum_{i=1}^{N_{\text{child}}(a)} w_i} & \text{if } N_{\text{child}}(a) > 0 \\
Q_{\text{weighted}}(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}} & \text{if } N_{\text{child}}(a) = 0
\end{cases}$$

#### 4.2.3 总价值$V_i^{\text{total}}$的组成
$$V_i^{\text{total}} = V_i^{\text{corrected}} + E[u_{\text{score}}^{(i)}]$$

**偏差纠正价值**：
$$V_i^{\text{corrected}} = V_i^{\text{raw}} - \lambda \cdot \text{ObsBias}(B(n_i))$$

**得分效用期望**：
$$E[u_{\text{score}}^{(i)}] = c_{\text{score}} \int_{-\infty}^{\infty} \frac{2}{\pi} \arctan\left(\frac{x - \hat{\mu}_s^{(i)}}{b}\right) \mathcal{N}(x; \hat{\mu}_s^{(i)}, (\hat{\sigma}_s^{(i)})^2) dx$$

**不确定性权重**：
$$w_i = \frac{1}{\max(\epsilon_{\text{unc}}, \sqrt{\text{UncertaintyPred}_{\text{value}}^{(i)} + \text{UncertaintyPred}_{\text{score}}^{(i)}})}$$

### 4.3 动态探索系数$c_{\text{dynamic}}(n)$
$$c_{\text{dynamic}}(n) = c_{\text{base}} \cdot \sqrt{\text{Var}_{\text{utility}}(n) + \epsilon_{\text{var}}}$$

### 4.4 最终策略概率$P_{\text{final}}(n,a)$
$$P_{\text{final}}(n,a) = (1-\epsilon_{\text{noise}}) \cdot P_{\text{temp}}^{\text{opt}}(n,a) + \epsilon_{\text{noise}} \cdot \text{Dir}(\boldsymbol{\alpha}_{\text{shaped}})$$

其中$\epsilon_{\text{noise}} = 0.25$

### 4.5 边访问次数更新规则（MCGS核心）
每次选择动作$a$后：
$$N(n,a) \leftarrow N(n,a) + 1$$
$$N(n) = 1 + \sum_b N(n,b)$$

### 4.6 强制探索约束（来自论文）
当满足以下条件时，设置$\text{PUCT}(n,a) = +\infty$：
$$N(n,a) < \left(k \cdot P_{\text{final}}(n,a) \sum_{b} N(n,b)\right)^{1/2}$$

### 4.7 提前停止优化（来自MCGS文档）
可选择在以下条件下停止playout：
$$N_{\text{child}}(a) > N(n,a)$$

这基于子节点已有足够访问支持当前边权重的假设。

## 5. 算法伪代码（整合版）

```python
def perform_one_playout(node):
    if is_game_over(node):
        node.U = get_utility_of_game_outcome(node.game_state)
    elif node.N == 0:  # 首次访问
        node.U = get_utility_from_neural_net(node.game_state)
    else:
        action = select_action_via_enhanced_puct(node)  # 使用完整公式
        
        # MCGS: 检查转置
        if action not in node.children_and_edge_visits:
            new_game_state = node.game_state.play(action)
            if new_game_state.hash in nodes_by_hash:
                child = nodes_by_hash[new_game_state.hash]
            else:
                child = Node(N=0, game_state=new_game_state)
                nodes_by_hash[new_game_state.hash] = child
            node.children_and_edge_visits[action] = (child, 0)
        
        (child, edge_visits) = node.children_and_edge_visits[action]
        
        # 可选的提前停止
        if child.N <= edge_visits:
            perform_one_playout(child)
        
        # 更新边访问次数
        node.children_and_edge_visits[action] = (child, edge_visits + 1)

    # MCGS幂等更新
    children_and_edge_visits = node.children_and_edge_visits.values()
    node.N = 1 + sum(edge_visits for (_, edge_visits) in children_and_edge_visits)
    
    # 使用加权、偏差纠正的Q值计算
    node.Q = compute_weighted_corrected_q_value(node, children_and_edge_visits)
```

## 6. 改进效果总结

| 改进方法              | 核心解决问题       | 数学表现                                                     | 预估效果         |
| --------------------- | ------------------ | ------------------------------------------------------------ | ---------------- |
| **MCGS边访问分离**    | 转置共享计算       | $N(n,a) \neq N(\text{child})$                                | 搜索效率大幅提升 |
| **FPU**               | 未探索节点价值估计 | $Q = Q_{\text{parent}} - c_{\text{FPU}}\sqrt{P_{\text{explored}}}$ | 搜索稳定性提升   |
| **强制探索+策略剪枝** | 探索-学习解耦      | $N(a) < (kP(a)\sum N)^{1/2}$                                 | +30-50 Elo       |
| **综合效用函数**      | 多目标优化         | $U = u_{\text{win}} + u_{\text{score}}$                      | 分析质量显著提升 |
| **形状化噪声**        | 智能探索引导       | $\alpha_a \propto \max(0, \log P(a) - \log P_{\text{thresh}})$ | 盲点发现效率提升 |
| **子树偏差纠正**      | 系统性偏差消除     | $V^{\text{corrected}} = V^{\text{raw}} - \lambda \cdot \text{Bias}$ | +30-60 Elo       |
| **动态cPUCT**         | 自适应探索         | $c = c_{\text{base}} \sqrt{\text{Var} + \epsilon}$           | +25 Elo          |
| **不确定性加权**      | 置信度感知         | $w_i = 1/\max(\epsilon, \text{Uncertainty}_i)$               | +50 Elo          |
| **乐观策略**          | 战术发现优化       | $w_{\text{opt}} = f(z_{\text{surprise}})$                    | +40-90 Elo       |

**总体效果**：相比AlphaZero，KataGo通过MCGS和多项改进实现了约10-50倍的学习效率提升，在实际对弈中展现出更强的战术发现能力、转置处理能力和分析精度。MCGS的边访问分离是实现高效转置共享的理论基础，其他改进在此基础上进一步优化了探索策略、价值估计和学习效率。