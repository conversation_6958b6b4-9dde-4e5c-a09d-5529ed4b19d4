
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 1;
const board = [0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,1,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,1],[1,2],],'p':0.6147,'wl':-0.5162,'ssM':0.07,'wlRad':0.0085,'sRad':1.13,'v':84,'av':84,},{'xy':[[3,1],[1,3],],'p':0.4254,'wl':-0.3385,'ssM':0.05,'wlRad':0.0514,'sRad':0.70,'v':184,'av':184,},{'xy':[[3,2],[2,3],],'p':0.9022,'wl':0.3125,'ssM':0.77,'wlRad':0.1614,'sRad':0.34,'v':83,'av':83,},{'xy':[[1,0],[0,1],],'p':0.4157,'wl':0.7894,'ssM':0.19,'wlRad':0.1492,'sRad':0.78,'v':135,'av':135,},{'move':'other','p':0.3840,'wl':-0.6042,'ssM':0.18,'wlRad':0.0379,'sRad':0.70,'v':18,'av':18,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
