
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 2;
const pLink = '../5B/37E7FD895BD61EAECAF38886988533E6EDEAD3DB6F26475ABC3A3C50D5934001.html';
const pSym = 5;
const board = [0,0,1,0,0,0,0,1,0,0,0,0,0,2,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,1],],'p':0.5690,'wl':0.2990,'ssM':0.10,'wlRad':0.1121,'sRad':0.29,'v':154,'av':154,},{'move':'pass','p':0.3001,'wl':-0.1017,'ssM':0.72,'wlRad':0.0202,'sRad':1.59,'v':10,'av':10,},{'xy':[[1,0],],'p':0.9126,'wl':-0.2616,'ssM':-0.49,'wlRad':0.0591,'sRad':0.57,'v':52,'av':52,},{'move':'other','p':0.0112,'wl':0.4389,'ssM':0.51,'wlRad':0.4790,'sRad':0.78,'v':20,'av':20,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
