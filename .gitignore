*.[oa]
*.exe
*~
__pycache__
*.h5
*.log
*.cbp

tmp*.txt

cpp/write
cpp/runtests
cpp/example
cpp/gtp
cpp/gtp.log
cpp/match
cpp/matchcuda
cpp/matchtensorflow
cpp/match.log
cpp/main
cpp/maincuda
cpp/mainopencl
cpp/katago
cpp/configs
cpp/evalsgf
cpp/run*.sh
cpp/tests/scratch
cpp/program/gitinfo.h

cpp/tests/results/matchsgfs/games.sgfs
cpp/tests/results/matchsgfs2/games.sgfs

cpp/data/
versions/
cpp/build
cpp/out

export_model_cuda.sh
mixmodels.sh
runfindposes.sh
runtraining.sh
runlztest*.sh

python/upload_all*.sh
python/count_all*.sh

tmp
tmp.txt
test.txt
cpp/tmp
out.txt

# For clion IDE
.idea

# For vscode
.vscode

# For VS
.vs
cpp/CMakeSettings.json

# For cmake
CMakeCache.txt
CMakeFiles/
Makefile
cmake_install.cmake

.DS_Store
GPATH
GRTAGS
GTAGS

cpp/external/httplib/cpp-httplib/
cpp/external/nlohmann_json/nlohmann_json
gtp.cfg
katago_contribute/
tmpsgf/
watchgame.txt
models/
python/startposesupload.txt
for_release/

tests/results/matchsgfs/
tests/results/matchsgfs2/

# For Metal Backend
cpp/.ninja_deps
cpp/.ninja_log
cpp/build.ninja
cpp/KataGoSwift.*
cpp/include/KataGoSwift/KataGoSwift-swift.h
