
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 1;
const board = [0,0,0,0,1,0,0,0,0,0,2,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,0],],'p':0.8159,'wl':-0.1792,'ssM':0.22,'wlRad':0.0285,'sRad':0.59,'v':11,'av':11,},{'xy':[[0,2],],'p':0.9327,'wl':0.0232,'ssM':1.22,'wlRad':0.0555,'sRad':0.69,'v':94,'av':94,},{'xy':[[3,3],],'p':0.6678,'wl':0.7894,'ssM':0.19,'wlRad':0.1492,'sRad':0.78,'v':135,'av':135,},{'move':'other','p':0.0880,'wl':-0.6605,'ssM':0.01,'wlRad':0.2052,'sRad':0.18,'v':13,'av':13,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
