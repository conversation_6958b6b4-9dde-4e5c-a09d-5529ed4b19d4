
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 2;
const board = [0,2,0,0,0,0,1,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,3],],'p':0.1996,'wl':-0.3875,'ssM':0.92,'wlRad':0.2086,'sRad':1.35,'v':80,'av':80,},{'xy':[[0,1],],'p':0.8662,'wl':-0.0539,'ssM':0.69,'wlRad':0.0284,'sRad':1.89,'v':155,'av':155,},{'xy':[[0,0],],'p':0.3014,'wl':0.1080,'ssM':0.53,'wlRad':0.0013,'sRad':0.42,'v':187,'av':187,},{'xy':[[1,2],],'p':0.9381,'wl':0.5469,'ssM':-0.27,'wlRad':0.0173,'sRad':1.07,'v':93,'av':93,},{'xy':[[0,2],],'p':0.7732,'wl':0.6860,'ssM':-0.06,'wlRad':0.0882,'sRad':0.70,'v':186,'av':186,},{'xy':[[2,3],],'p':0.4513,'wl':0.6108,'ssM':0.91,'wlRad':0.1642,'sRad':0.32,'v':98,'av':98,},{'move':'other','p':0.0084,'wl':-0.0589,'ssM':0.24,'wlRad':0.0209,'sRad':2.02,'v':15,'av':15,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
