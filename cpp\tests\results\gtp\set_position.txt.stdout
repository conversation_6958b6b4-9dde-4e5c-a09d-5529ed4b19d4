= MoveNum: 0 HASH: CDCBC1F514D7E680FACD226074256633
   A B C D E F G H J K L M N O P Q R S T
19 . . . . . . . . . . . . . . . . . . .
18 . . . . . . . . . . . . . . . . . . .
17 . . . . . . . . . . . . . . . . . . .
16 . . . . . . . . . . . . . . . . . . .
15 . . . . . . . . . . . . . . . . . . .
14 . . . . . . . . . . . . . . . . . . .
13 . . . . . . . . . . . . . . . . . . .
12 . . . . . . . . . . . . . . . . . . .
11 . . . . . . . . . . . . . . . . . . .
10 . . . . . . . . . . . . . . . . . . .
 9 . . . . . . . . . . . . . . . . . . .
 8 . . . . . . . . . . . . . . . . . . .
 7 . . . . . . . . . . . . . . . . . . .
 6 . . . . . . . . . . . . . . . . . . .
 5 . . . . . . . . . . . . . . . . . . .
 4 . . . . . . . . . . . . . . . . . . .
 3 . . . . . . . . . . . . . . . . . . .
 2 . . . . . . . . . . . . . . . . . . .
 1 . . . . . . . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: A927404D59D756B072DFBA7052A05BF5
   A B C D E F G H J K L M N O P Q R S T
19 . . . . . . . . . . . . . . . . . . .
18 . . . . . . . . . . . . . . . . . . .
17 . . . . . . . . . . . . . . . X . . .
16 . . . . . . . . . . . . . . . O . . .
15 . . . . . . . . . . . . . . . . . . .
14 . . . . . . . . . . . . . . . . . . .
13 . . . . . . . . . . . . . . . O . . .
12 . . . . . . . . . . . . . . . . . . .
11 . . . . . . . . . . . . . . . . . . .
10 . . . . . . . . . X . . . . . . . . .
 9 . . . . . . . . . . . . . . . . . . .
 8 . . . . . . . . . . . . . . . . . . .
 7 . . . . . . . . . . . . . . . . . . .
 6 . . . . . . . . . . . . . . . . . . .
 5 . . X . . . . . . . . . . . . . . . .
 4 . . . . . . . . . . . . . . . . . . .
 3 . . X . . . . . . . . . . . . . . . .
 2 . . . . . . . . . . . . . . . . . . .
 1 . . . . . . . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: CDCBC1F514D7E680FACD226074256633
   A B C D E F G H J K L M N O P Q R S T
19 . . . . . . . . . . . . . . . . . . .
18 . . . . . . . . . . . . . . . . . . .
17 . . . . . . . . . . . . . . . . . . .
16 . . . . . . . . . . . . . . . . . . .
15 . . . . . . . . . . . . . . . . . . .
14 . . . . . . . . . . . . . . . . . . .
13 . . . . . . . . . . . . . . . . . . .
12 . . . . . . . . . . . . . . . . . . .
11 . . . . . . . . . . . . . . . . . . .
10 . . . . . . . . . . . . . . . . . . .
 9 . . . . . . . . . . . . . . . . . . .
 8 . . . . . . . . . . . . . . . . . . .
 7 . . . . . . . . . . . . . . . . . . .
 6 . . . . . . . . . . . . . . . . . . .
 5 . . . . . . . . . . . . . . . . . . .
 4 . . . . . . . . . . . . . . . . . . .
 3 . . . . . . . . . . . . . . . . . . .
 2 . . . . . . . . . . . . . . . . . . .
 1 . . . . . . . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: 6D7B9BD0653F4FE761D8EA5338E563E0
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . . . . . . . . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . . . . . . . . . . .
 4 . . . . . . . . . . . . .
 3 . . . . . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: 3BFA3DC657A6A5EFD2BEA7778146E5BE
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . O . . . . O . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . . . . . . . . . . .
 4 . . . . . . . . . . . . .
 3 . . . O . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: 5F4FCF10528E2A9D9AAD58BBDBB6C452
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . X . . . . X . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . . . . . . . . . . .
 4 . . . . . . . . . . . . .
 3 . . . X . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

? Expected a space-separated sequence of <COLOR> <VERTEX> pairs but got 'b d3 b d10 b j10 b'

= MoveNum: 0 HASH: 5F4FCF10528E2A9D9AAD58BBDBB6C452
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . X . . . . X . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . . . . . . . . . . .
 4 . . . . . . . . . . . . .
 3 . . . X . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

? Expected a space-separated sequence of <COLOR> <VERTEX> pairs but got 'w d3 w    d10  w  j10'

= MoveNum: 0 HASH: 5F4FCF10528E2A9D9AAD58BBDBB6C452
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . X . . . . X . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . . . . . . . . . . .
 4 . . . . . . . . . . . . .
 3 . . . X . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

? Illegal stone placements - overlapping stones or stones with no liberties?

= MoveNum: 0 HASH: 5F4FCF10528E2A9D9AAD58BBDBB6C452
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . X . . . . X . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . . . . . . . . . . .
 4 . . . . . . . . . . . . .
 3 . . . X . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

? Illegal stone placements - overlapping stones or stones with no liberties?

= MoveNum: 0 HASH: 5F4FCF10528E2A9D9AAD58BBDBB6C452
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . X . . . . X . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . . . . . . . . . . .
 4 . . . . . . . . . . . . .
 3 . . . X . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: 0060C2712F9F96DF15BC0200D6BF3DFD
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . . . . . . . . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . O . . . . . . . . .
 4 . . . X O . . . . . . . .
 3 . . . O . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

? Illegal stone placements - overlapping stones or stones with no liberties?

= MoveNum: 0 HASH: 0060C2712F9F96DF15BC0200D6BF3DFD
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . . . . . . . . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . O . . . . . . . . .
 4 . . . X O . . . . . . . .
 3 . . . O . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

? Expected a space-separated sequence of <COLOR> <VERTEX> pairs but got 'b pass': could not parse vertex: 'pass'

= MoveNum: 0 HASH: 0060C2712F9F96DF15BC0200D6BF3DFD
   A B C D E F G H J K L M N
13 . . . . . . . . . . . . .
12 . . . . . . . . . . . . .
11 . . . . . . . . . . . . .
10 . . . . . . . . . . . . .
 9 . . . . . . . . . . . . .
 8 . . . . . . . . . . . . .
 7 . . . . . . . . . . . . .
 6 . . . . . . . . . . . . .
 5 . . . O . . . . . . . . .
 4 . . . X O . . . . . . . .
 3 . . . O . . . . . . . . .
 2 . . . . . . . . . . . . .
 1 . . . . . . . . . . . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: 710F257116C1B48A7B9876BE820C87F2
   A B C
 3 . . .
 2 . . .
 1 . . .
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

= 

= MoveNum: 0 HASH: 3EF6B2A37C09369B375BF765A930F411
   A B C
 3 X X .
 2 X X X
 1 X X X
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

? Illegal stone placements - overlapping stones or stones with no liberties?

= MoveNum: 0 HASH: 3EF6B2A37C09369B375BF765A930F411
   A B C
 3 X X .
 2 X X X
 1 X X X
Next player: Black
Rules: {"friendlyPassOk":false,"hasButton":false,"ko":"POSITIONAL","komi":7.5,"scoring":"AREA","suicide":true,"tax":"NONE","whiteHandicapBonus":"0"}
B stones captured: 0
W stones captured: 0

