
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 2;
const pLink = '../48/00E5B7E44801D1632C1EE7142B798FC95FEC1A7A51D464655F956C4D42133016.html';
const pSym = 3;
const board = [0,1,1,0,0,2,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,2],],'p':0.8671,'wl':0.2626,'ssM':-0.82,'wlRad':0.1931,'sRad':0.53,'v':46,'av':46,},{'xy':[[3,1],],'p':0.4662,'wl':0.1025,'ssM':-0.31,'wlRad':0.0318,'sRad':0.53,'v':78,'av':78,},{'xy':[[0,1],],'p':0.6315,'wl':-0.9626,'ssM':0.06,'wlRad':0.3690,'sRad':0.67,'v':84,'av':84,},{'move':'other','p':0.1386,'wl':-0.6440,'ssM':-0.92,'wlRad':0.0732,'sRad':1.07,'v':12,'av':12,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
