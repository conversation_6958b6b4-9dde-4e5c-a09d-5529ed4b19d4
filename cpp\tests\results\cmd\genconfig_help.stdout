
DESCRIPTION: 

   Automatically generate and tune a new GTP config.

USAGE: 

   genconfig  [-model <FILE>] [-output <FILE>]


Where: 

   -model <FILE>
     Neural net model file. Defaults to: (dir containing katago.exe, or
     else ~/.katago)/default_model.bin.gz

   -output <FILE>
     Path to write new config (default gtp.cfg)

   --,  -ignore_rest
     Ignores the rest of the labeled arguments following this flag.

   -version
     Displays version information and exits.

   -h,  -help
     Displays usage information and exits.



