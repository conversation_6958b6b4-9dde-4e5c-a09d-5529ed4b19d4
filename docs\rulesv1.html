<html>
<head>

<style>
.flex-vertical {
  display: flex;
  flex-flow: column;
}
.flex-horizontal {
  display: flex;
  flex-flow: row;
}

.definition {
  font-weight: bold;
}
.condition {
  font-weight: bold;
}
.gameEnd {
  font-weight: bold;
  text-decoration: underline;
}
.note {
  margin-bottom: 1em;
}

.genericRule {
  color: #000000;
}
.koRule {
  color: #990000;
}
.scoringRule {
  color: #007755;
}
.taxRule {
  color: #4455ff;
}
.multiStoneSuicide {
  color: #aa00aa;
}
.hasButton {
  color: #666600;
}
.selfPlayOpts {
  color: #0088bb;
}
.whiteHandicapBonus {
  color: #666666;
}

button {
  margin: 5px;
}

ol {
  margin-top: 0.4em;
  margin-bottom: 0.4em;
}
ul {
  margin-top: 0.4em;
  margin-bottom: 0.4em;
}
li {
  margin-bottom: 0.5em;
}
li > ol li {
  margin-bottom: 0.2em;
}
li > ul li {
  margin-bottom: 0.2em;
}

body {
  margin-bottom: 2em;
  padding-left: 15px;
  padding-right: 15px;
}

.komi {
  color:
}

</style>

<script>

function updateName(name) {
  elts = document.getElementsByClassName(name);
  for(var j = 0; j<elts.length; j++) {
    var classList = elts[j].className.split(" ");
    var needRules = false;
    for(var k = 0; k<classList.length; k++) {
      var box = document.getElementById(classList[k]);
      if(box === null)
        continue;
      if(box.checked) {
        needRules = true;
        break;
      }
    }

    elts[j].style.display = needRules ? "" : "none";
  }
};

function updateEverything() {
  var inputs = document.getElementsByTagName("input");
  for(var i = 0; i<inputs.length; i++) {
    if(inputs[i].type == "checkbox") {
      updateName(inputs[i].id);
    }
  }
};

function checkEverything() {
  var inputs = document.getElementsByTagName("input");
  for(var i = 0; i<inputs.length; i++) {
    if(inputs[i].type == "checkbox") {
      inputs[i].checked = true;
    }
  }
  updateEverything();
};

function checkNames(names) {
  var inputs = document.getElementsByTagName("input");
  for(var i = 0; i<inputs.length; i++) {
    if(inputs[i].type == "checkbox")
      inputs[i].checked = names.includes(inputs[i].id);
  }
  updateEverything();
};

function checkChineseLike() {
  checkNames([
    "koSimple",
    "scoringArea",
    "taxNone",
    "multiStoneSuicideDisallowed",
    "buttonNotUsed",
    "whiteHandicapBonusN"
  ]);
};


function checkAGALike() {
  checkNames([
    "koSituational",
    "scoringArea",
    "taxNone",
    "multiStoneSuicideDisallowed",
    "buttonNotUsed",
    "whiteHandicapBonusN1"
  ]);
};

function checkStoneScoring() {
  checkNames([
    "koSimple",
    "scoringArea",
    "taxAll",
    "multiStoneSuicideDisallowed",
    "buttonNotUsed"
  ]);
};

function checkNZLike() {
  checkNames([
    "koSituational",
    "scoringArea",
    "taxNone",
    "multiStoneSuicideAllowed",
    "buttonNotUsed",
    "whiteHandicapBonus0"
  ]);
};

function checkJapaneseLike() {
  checkNames([
    "koSimple",
    "scoringTerritory",
    "taxSeki",
    "multiStoneSuicideDisallowed",
    "buttonNotUsed",
    "whiteHandicapBonus0"
  ]);
};

function checkTrompTaylor() {
  checkNames([
    "koPositional",
    "scoringArea",
    "taxNone",
    "multiStoneSuicideAllowed",
    "buttonNotUsed"
  ]);
};


window.onload = function() {
  let inputs = document.getElementsByTagName("input");
  for(let i = 0; i<inputs.length; i++) {
    if(inputs[i].type == "checkbox") {
      inputs[i].addEventListener("change", function() {
        updateName(inputs[i].id);
      });
    }
  }

  checkEverything();
};
</script>

</head>
<body>

<h1>KataGo's Supported Go Rules (Version 1) </h1>

This is NOT the latest version of these rules. The latest rules can be found <a href="./rules.html">here</a>.

This page describes the full rigorous rules implemented by KataGo. These rules are supported in KataGo version 1.3 and later.

<p>
A hope also is that this document can serve as a reference for anyone to implement any subset of such rules themselves - including a rigorous nearly-Japanese rules that bots can use for self-play or for competitive matches completely without need for outside adjudication or dispute resolution or any other protocol besides just the bots making ordinary plays.
<p>
I believe the nearly-Japanese rules should correctly handle a wide variety of details, so long as both players play to rationally maximize their score. For example:

<ul>
<li> Bent-four-in-the-corner will die regardless of external ko threats.</li>
<li> No territory in seki, including no territory for one-sided dame (a common quirk of simpler territory rules).
<li> Double-ko-seki and thousand-year-ko are sekis, double ko death is a death.</li>
<li> One should finish a direct ko rather than leave it open, even if there are enough ko threats to pass and still leave it open, but multi-step-ko need not be finished.</li>
</ul>
A known difference is with <a href="https://senseis.xmp.net/?TorazuSanmoku">three-points-without-capturing</a>, and a few other exotic rules beasts are also known that will cause a difference, but these should be very very rare.
<p>
Updated from <a href="./rulesv0.html">Version 0</a> are some details of the cleanup phase ko rules and some of the endnotes and commentary. The changes were to get slightly more consistency with Japanese rules in some very rare cases, probably less than 1 in 1000 games. Unless a major flaw is discovered, these rules should be considered reasonably stable and KataGo will be doing a full training run with them.

<hr>
<h2>Parameters</h2>
<hr>

Click the buttons or individually uncheck some of the boxes to hide parts of the logic for rules you don't care about.
<br/>
NOTE: Some online servers that claim to implement certain rules might actually differ. For example, OGS's implementation of Chinese rules uses positional superko, which differs from actual practice in Chinese Go tournaments that normally use just a simple ko rule. If you're implementing a bot to run on a server, always look up the details!
<br/>

<div class="flex-horizontal">
<button onclick="checkEverything()">Check All</button>
<button onclick="checkChineseLike()">Chinese-like Rules</button>
<button onclick="checkJapaneseLike()">Japanese-like Rules</button>
<button onclick="checkTrompTaylor()">Tromp-Taylor Rules</button>
<button onclick="checkAGALike()">AGA-like Rules</button>
<button onclick="checkNZLike()">New Zealand-like Rules</button>
<button onclick="checkStoneScoring()">Stone-Scoring Rules</button>
</div>

<ul>
<li class="genericRule"><span class="definition">X,Y</span>: <br/> Integers indicating the board size.</li>
<li class="genericRule"><span class="definition">Komi</span>: <br/> Integer or half-integer indicating compensation given to White for going second.</li>
<li class="koRule"><span class="definition">KoRule</span>: <br/> The variant of the rule prohibiting repetition. <a href="https://senseis.xmp.net/?KoRules">https://senseis.xmp.net/?KoRules</a>
<div class="flex-vertical">
<span><input type="checkbox" id="koSimple" name="koRule" value="koSimple"><label for="koSimple">Simple</label></span>
<span><input type="checkbox" id="koPositional" name="koRule" value="koPositional"><label for="koPositional">Positional Superko</label></span>
<span><input type="checkbox" id="koSituational" name="koRule" value="koSituational"><label for="koSituational">Situational Superko</label></span>
</div>
</li>
<li class="scoringRule"><span class="definition">ScoringRule</span>: <br/> Defines what the score of a finished game is. <a href="https://senseis.xmp.net/?Scoring">https://senseis.xmp.net/?Scoring</a>
<div class="flex-vertical">
<span><input type="checkbox" id="scoringArea" name="scoringRule" value="scoringArea"><label for="scoringArea">Area (count stones and surrounded empty points, "Chinese"-like)</label></span>
<span><input type="checkbox" id="scoringTerritory" name="scoringRule" value="scoringTerritory"><label for="scoringTerritory">Territory (count captures and surrounded empty points, "Japanese"-like)</label></span>
</div>
</li>
<li class="taxRule"><span class="definition">TaxRule</span>: <br/> Minor adjustments to scoring rule, indicating what, if any, empty points may not be scored.
<div class="flex-vertical">
<span><input type="checkbox" id="taxNone" name="taxRule" value="taxNone"><label for="taxNone">None (surrounded empty points always count)</label></span>
<span><input type="checkbox" id="taxSeki" name="taxRule" value="taxSeki"><label for="taxSeki">Seki (empty points surrounded by groups in seki do not count)</label></span>
<span><input type="checkbox" id="taxAll" name="taxRule" value="taxAll"><label for="taxAll">All (all groups are taxed up to 2 of their surrounded empty points - i.e. empty points surrounded by groups in seki do not count, and living groups are taxed 2 points each)</label></span>
</div>
</li>
<li class="multiStoneSuicide"><span class="definition">MultiStoneSuicide</span>: <br/> Whether suicide of multiple stones is allowed. (in these rules, a suicide move that kills only the stone just played and nothing else, leaving the board unchanged, is never allowed)
<div class="flex-vertical">
<span><input type="checkbox" id="multiStoneSuicideAllowed" name="multiStoneSuicide" value="multiStoneSuicideAllowed"><label for="multiStoneSuicideAllowed">Allowed</label></span>
<span><input type="checkbox" id="multiStoneSuicideDisallowed" name="multiStoneSuicide" value="multiStoneSuicideDisallowed"><label for="multiStoneSuicideDisallowed">Disallowed</label></span>
</div>
</li>
<li class="hasButton"><span class="definition">Button</span>: <br/> Whether a half-point is awarded to the first player to be able to pass. (e.g. slightly rewarding endgame efficiency, partially reconciling area and territory scoring).
<div class="flex-vertical">
<span><input type="checkbox" id="buttonUsed" name="hasButton" value="buttonUsed"><label for="buttonUsed">Used</label></span>
<span><input type="checkbox" id="buttonNotUsed" name="hasButton" value="buttonNotUsed"><label for="buttonNotUsed">Not Used</label></span>
</div>
</li>
<li class="whiteHandicapBonus"><span class="definition">WhiteHandicapBonus</span>: <br/> How many bonus points white receives during handicap games when black gets N stones. KataGo supports handicap games, but for simplicity, this rules document does NOT describe them. These checkboxes are included merely to provide a convenience reference as how this quirk of handicap game scoring differs between rulesets.
<div class="flex-vertical">
<span><input type="checkbox" id="whiteHandicapBonus0" name="whiteHandicapBonus" value="whiteHandicapBonus0"><label for="whiteHandicapBonus0">0</label></span>
<span><input type="checkbox" id="whiteHandicapBonusN1" name="whiteHandicapBonus" value="whiteHandicapBonusN1"><label for="whiteHandicapBonusN1">N-1</label></span>
<span><input type="checkbox" id="whiteHandicapBonusN" name="whiteHandicapBonus" value="whiteHandicapBonusN"><label for="whiteHandicapBonusN">N</label></span>
</div>
</li>
<li class="selfPlayOpts"><span class="definition">SelfPlayOpts</span>: <br/> Some optimizations that KataGo uses for self-play. These theoretically could make a difference to correct play in extremely contrived situations involving things like carefully constructed superko histories, but for all practical purposes these rules modifications are compatible with the rules without these modifications and simply speed up self-play a via faster game end, even if one or both players want to prolong the game. (Note: KataGo's own internal code and neural nets always use these optimizations, and then use various postprocessing to achieve other passing/game-end/cleanup behavior).
<div class="flex-vertical">
<span><input type="checkbox" id="selfPlayOptsEnabled" name="selfPlayOpts" value="selfPlayOptsEnabled"><label for="selfPlayOptsEnabled">Enabled</label></span>
</div>
</li>
</ul>

<hr>

<h2>
Rules
</h2>

<hr>

<h3>
Basic Definitions
</h3>

<ol>
<li> Go is played on an X by Y rectangular grid of points by two opposing players, Black and White. </li>
<li> Each point on the grid can be colored black or white or be empty. The coloring status of all points together is the <span class="definition">grid coloring</span>.</li>
<li> Points are <span class="definition">adjacent/bordering</span> if they are horizontally or vertically touching.</li>
<li> Within a set of points, two points are <span class="definition">connected</span> if one is adjacent to the other or is connected to a point adjacent to the other. The set itself is <span class="definition">connected</span> if every pair of points in it is connected within that set.</li>
<li> A set of points and a point not in that set are <span class="definition">adjacent/bordering</span> if that point is adjacent to some member of the set. Two disjoint sets of points are <span class="definition">adjacent/bordering</span> if one borders at least one point of the other. A set of points <span class="definition">borders a color</span> if it borders at least one point of that color.</li>
<li> A {black, white, empty, maximal-non-black, maximal-non-white} <span class="definition">region</span> is any maximal connected set of {black, white, empty, non-black, non-white} points, respectively. <span class="definition">Maximal</span> means there is no strictly larger connected set of such points that contains it.
</ol>

<hr>

<h3>
Pseudolegal moves
</h3>
<ol>
<li> A <span class="definition">liberty</span> of a black or white region is any empty point that borders it. </li>
<li> <span class="definition">Resolving captures</span> of a color consists of emptying all points of regions of that color with no liberties. Every emptying of a point this way during the game adds to the total <span class="definition">number of captures</span> of that color. </li>
<li> A <span class="definition">pseudolegal move</span> consists of a player performing the following:
  <ul>
  <li>Coloring an empty point with the player's color.</li>
  <li>Then resolving captures of the opponent's color.</li>
  <li>Then resolving captures of the player's color.</li>
  </ul>
...subject the restriction that this must NOT result in same grid coloring as prior to these steps.
<span class="multiStoneSuicide multiStoneSuicideDisallowed">
<br/>
...<span class="condition">(if MultiStoneSuicide is Disallowed)</span> and also subject to the restriction that resolving captures of the player's own color must NOT empty any points.
</span>
<br/>
<br/>
For any regions emptied by the steps of resolving captures, we say that the move <span class="definition">captures</span> those regions.
</li>
</ol>

<hr>

<h3>
Additional Definitions
</h3>

<span class="scoringTerritory taxSeki taxAll">
<ol>
<li>An empty region that borders both black and white is a <span class="definition">dame region</span>.</li>

<li>A black or white region is in <span class="definition">atari</span> if it is has exactly one liberty.</li>

<li>A {maximal-non-white, maximal-non-black} region is a {black, white} <span class="definition">independent-life-region</span> if it contains at least one {black,white} point and does NOT contain any dame regions or any regions in atari.<a href="#note1" id="ref1">[1]</a></li>
</ol>
</span>

<span class="selfPlayOpts selfPlayOptsEnabled">
<ol start="4">
<li>A black or white region R is a <span class="definition">pass-alive-group</span> if there does not exist any sequence of consecutive pseudolegal moves of the opposing color that results in emptying R.<a href="#note2" id="ref2">[2]</a></li>

<li>A {maximal-non-black, maximal-non-white} region R is <span class="definition">pass-alive-territory</span> for {Black, White} if all {black, white} regions bordering it are pass-alive-groups, and all or all but one point in R is adjacent to a {black, white} pass-alive-group, respectively.<a href="#note3" id="ref3">[3]</a>
</li>
</ol>
</span>

<hr>

<h3>
Main Phase
</h3>

The game begins with a main phase of play<span class="scoringTerritory"> and then possibly one or two cleanup phases</span>.

<p>
During the main phase the <span class="definition">state</span> consists of:
<ul>
<li>The grid coloring.</li>
<li class="koRule koSimple koSituational"><span class="condition">(if KoRule is Simple or Situational SuperKo)</span>: Additionally, the color of the player next to take a turn.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: Additionally, whether or not at least one pass has occurred.</li>
</ul>

<p>
Starting with an empty grid, the players alternate turns, starting with Black. A <span class="definition">turn</span> in the main phase is either a pass or a legal move.
<ul>
<li>A <span class="definition">pass</span> cedes the turn with no effect (but may possibly end the phase, as described below).</li>

<li>A <span class="definition">legal move</span> during the main phase is any pseudolegal move that...
<ul>
<li class="koRule koSimple"><span class="condition">(if KoRule is Simple)</span>: ...doesn't result in the state at the start of the opponent's previous turn.</li>
<li class="koRule koPositional koSituational"><span class="condition">(if KoRule is Positional or Situational SuperKo)</span>: ...doesn't result in any earlier state.</li>
</ul>
</li>
</ul>

<p>
The main phase ends after:
<ol>
<li class="hasButton buttonNotUsed"><span class="condition">(if Button is NotUsed)</span>: There are 2 consecutive passes.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: There are 2 consecutive passes, ignoring the first pass of the game.</li>
<li class="koRule koSimple"><span class="condition">(if KoRule is Simple)</span>: Additionally the game also ends if:
<ol>
<li>A player passes from a state that the player has already passed from once before.<a href="#note4" id="ref4">[4]</a></li>
<li>OR at the start of a player's turn, the current state has already occurred twice before since the most recent pass by either player. In this case the not only the main phase ends but the entire game immediately ends as well, with a result of "no result".<a href="#note5" id="ref5">[5]</a></li>
</ol>
</li>
<li class="selfPlayOpts selfPlayOptsEnabled"><span class="condition">(if SelfPlayOpts is Enabled)</span>: Additionally, the phase also ends at the end of a turn if every point on the board belongs to a pass-alive-group or pass-alive-territory. In this case the entire game ends immediately as well and is scored exactly as if the game had ended by both players repeatedly passing with no further legal moves or other actions.</li>
</ol>

<p>
After the main phase ends:
<p>

<span class="scoringRule scoringTerritory">
<span class="condition">(if ScoringRule is Territory)</span>
<br/>
The game is NOT ended or scored and instead continues with two cleanup phases (see "Cleanup Phases" section below).
</span>

<div class="scoringRule scoringArea">
<span class="condition">(if ScoringRule is Area)</span>
<br/>
The game ends and is scored as follows:
<ul>

<li class="selfPlayOpts selfPlayOptsEnabled"><span class="condition">(if SelfPlayOpts is Enabled)</span>: Before scoring, for each color, empty all points of that color within pass-alive-territory of the opposing color.</li>

<li class="taxRule taxNone"><span class="condition">(if TaxRule is None)</span>: A player's score is the sum of:
<ul>
<li>+1 for every point of their color.</li>
<li>+1 for every point in empty regions bordered by their color and not by the opposing color.</li>
<li>If the player is White, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass.</li>
</ul>
</li>

<li class="taxRule taxSeki"><span class="condition">(if TaxRule is Seki)</span>: A player's score is the sum of:
<ul>
<li>+1 for every point of their color.</li>
<li>+1 for every empty point within independent-life-regions of their color.</li>
<li>If the player is White, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass.</li>
</ul>
</li>

<li class="taxRule taxAll"><span class="condition">(if TaxRule is All)</span>: A player's score is the sum of:
<ul>
<li>+1 for every point of their color.</li>
<li>+1 for every empty point within independent-life-regions of their color.</li>
<li>-2 points for every independent-life-region of their color.</li>
<li>If the player is White, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass.</li>
</ul>
</li>

</ul>

<span class="note whiteHandicapBonus0 whiteHandicapBonusN1 whiteHandicapBonusN">
Although handicap games are not a focus of these rules, see <a href="#note11" id="ref11">[11]</a> for some notes about handicap game scoring.
</span>

<p>
<span class="gameEnd">The player with the higher score wins, or the game is a draw if equal score.</span>
</span>
</div>

<hr>

<div class="scoringTerritory">
<h3>
Cleanup Phases
</h3>
<span class="condition"> These phases only occur if <span class="scoringRule">scoringRule is Territory</span>.</span>

<p>
Cleanup is designed to try to match most of the ways that positions would be ruled and scored under normal Japanese rules, so long as players self-interestedly maximize their score during cleanup. Broadly, this is done by giving players 1 point of compensation per move during the (second) cleanup phase, such that the players can now capture dead stones and resolve all disputes without loss of points for filling in territory.

<p>
A variety of details are also managed to implement other quirks of Japanese rules. Including there-are-no-points-in-seki, and the Japanese conception of each position as "independent", such that ko threats in one part of the board do not affect the status of the rest of the board. For example, a bent-four-in-the-corner will still resolve as dead under optimal play with these rules even if there are unremovable ko threats on the rest of the board. A lot of the mechanism to do this is based on the Japanese rules themselves, attempting to formalize their spirit to try to make them rigorous enough for self-play.

<p>

We do not aim for a 100% perfect match, however. For example, under this ruleset, <a href="https://senseis.xmp.net/?TorazuSanmoku"> three-points-without-capturing</a> will (usually) entirely naturally be three points without capturing with no need for any special ruling, matching the traditional Japanese ruling (and in effect, justifying it). But the modern Japanese rules instead regard it as a seki, in which black must concede down to two points to get anything. More exotic kinds of positions will also differ between these rules and Japanese rules.

<h4> Cleanup Phase Basics and Definitions </h4>

<ol>
<li>A <span class="definition">ko-move</span> for a player in a position is any pseudolegal move M where the opponent would have a pseudolegal move in response, the <span class="definition">ko-reply</span>, that would result in exactly the grid coloring prior to M.</li>

<li>In addition to the grid coloring, points on the grid may be marked as <span class="definition">ko-recapture-blocked</span>.</li>

<li>The <span class="definition">state</span> during cleanup phases consists of the grid coloring together with the ko-recapture-blocked status of all points and the color of the player next to take a turn.</li>
</ol>

<h4> Cleanup Phase Play </h4>

<p>
Cleanup lasts for two phases<a href="#note6" id="ref6">[6]</a>. In each phase, starting with the grid coloring from the end of the previous phase, the players alternate turns, starting with the opponent of the player who took the last turn of the previous phase. A turn in the cleanup is either a pass, a legal move, or an unblock-ko-recapture action.<a href="#note7" id="ref7">[7]</a>
<ul>
<li>A <span class="definition">pass</span> cedes the turn with no effect (but may possibly end the phase, as described below).</li>
<li>A <span class="definition">legal move</span> by a player during a cleanup phase is any pseudolegal move that either...
  <ul>
  <li>Is NOT a ko-move.</li>
  <li>Is a ko-move that both...
    <ul>
    <li>Does NOT capture any region containing a point marked as ko-recapture-blocked.</li>
    <li>AND where that player did NOT on any earlier turn during the same cleanup phase make a legal move on exactly the same point with exactly the same grid coloring.<a href="#note8" id="ref8">[8]</a></li>
    </ul>
  Then, followed by marking the point colored by the move as ko-recapture-blocked.
  </li>
  </ul>
  Then, followed by unmarking all ko-recapture-blocked points whose grid color is empty.
</li>
<li>An <span class="definition">unblock-ko-recapture</span> action consists of a player choosing a ko-move for that player that would capture a region containing a point marked as ko-recapture-blocked, and removing that mark.</li>
</ul>

<h4> Cleanup Phase Ending and Scoring </h4>

<p>
A cleanup phase ends after any of:
<ol>
<li>There are two consecutive passes.</li>
<li>OR a player passes from a state that the player has already passed from once before during the same phase.<a href="#note9" id="ref9">[9]</a></li>
<li>OR at the start of a player's turn, the current state has already occurred twice before since the most recent pass by either player during this phase. In this case the not only the phase ends but the entire game immediately ends as well, with a result of "no result".</li>
</ol>

<p>
After the first cleanup phase ends, the second cleanup phase begins immediately with the same grid coloring but with all ko-recapture-blocks unmarked.

<p>
After the second cleanup phase ends, the game ends and is scored as follows:
<ul>

<li class="selfPlayOpts selfPlayOptsEnabled"><span class="condition">(if SelfPlayOpts is Enabled)</span>: Before scoring, for each color, empty all points of that color within pass-alive-territory of the opposing color. Points emptied this way also add to the total number of captures of that point's color.</li>

<li class="taxRule taxNone"><span class="condition">(if TaxRule is None)</span>: A player's score is the sum of:
<ul>
<li>+1 for every point in empty regions bordered by their color and not by the opposing color.</li>
<li>+ The total number of captures of the opposing color.</li>
<li>+1 for every move made by that player during the second cleanup phase.</li>
<li>-1 for every point of their color not within independent-life-regions and that was not their color at the start of the second cleanup phase.</li>
<li>If the player is white, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass during the main phase.</li>
</ul>
</li>
<li class="taxRule taxSeki"><span class="condition">(if TaxRule is Seki)</span>: A player's score is the sum of:
<ul>
<li>+1 for every empty point within independent-life-regions of their color.</li>
<li>+ The total number of captures of the opposing color.</li>
<li>+1 for every move made by that player during the second cleanup phase.</li>
<li>-1 for every point of their color not within independent-life-regions and that was not their color at the start of the second cleanup phase.</li>
<li>If the player is white, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass during the main phase.</li>
</ul>
</li>
<li class="taxRule taxAll"><span class="condition">(if TaxRule is All)</span>: A player's score is the sum of:
<ul>
<li>+1 for every empty point within independent-life-regions of their color.</li>
<li>+ The total number of captures of the opposing color.</li>
<li>+1 for every move made by that player during the second cleanup phase.</li>
<li>-1 for every point of their color not within independent-life-regions and that was not their color at the start of the second cleanup phase.</li>
<li>-2 points for every independent-life-region of their color.</li>
<li>If the player is white, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass during the main phase.</li>
</ul>
</li>
</ul>

See <a href="#note10" id="ref10">[10]</a> for some remarks about the scoring.
<br/>
<span class="note whiteHandicapBonus0 whiteHandicapBonusN1 whiteHandicapBonusN">
Although handicap games are not a focus of these rules, see <a href="#note11" id="ref11">[11]</a> for some notes about handicap game scoring.
</span>

<p>
<span class="gameEnd">The player with the higher score wins, or the game is a draw if equal score.</span>

<hr>

<p>
<span class="condition">For computer AI training</span>, the following equivalent formulation for a player's score could also be used if desired.
This formulation is much more similar to area scoring, in that it factors over the board as simply a sum of +1/0/-1 for each point on the board, and moves within independent-life-regions by either player do not affect this "ownership" sum whatsoever (so long as dead stones are cleaned up and borders and dame are finished).

<ul>
<li class="taxRule taxNone"><span class="condition">(if TaxRule is None)</span>: A player's score is the sum of:
<ul>
<li>-1 for every move made by that player in the main phase OR first cleanup phase.</li>
<li>+1 for every point in empty regions bordered by their color and not by the opposing color.</li>
<li>+1 for every point of their color that is within independent-life-regions OR that was their color at the start of the second cleanup phase.</li>
<li>If the player is white, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass during the main phase.</li>
</ul>
</li>
<li class="taxRule taxSeki"><span class="condition">(if TaxRule is Seki)</span>: A player's score is the sum of:
<ul>
<li>-1 for every move made by that player in the main phase OR first cleanup phase.</li>
<li>+1 for every empty point within independent-life-regions of their color.</li>
<li>+1 for every point of their color that is within independent-life-regions OR that was their color at the start of the second cleanup phase.</li>
<li>If the player is white, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass during the main phase.</li>
</ul>
</li>
<li class="taxRule taxAll"><span class="condition">(if TaxRule is All)</span>: A player's score is the sum of:
<ul>
<li>-1 for every move made by that player in the main phase OR first cleanup phase.</li>
<li>+1 for every empty point within independent-life-regions of their color.</li>
<li>+1 for every point of their color that is within independent-life-regions OR that was their color at the start of the second cleanup phase.</li>
<li>-2 points for every independent-life-region of their color.</li>
<li>If the player is white, Komi.</li>
<li class="hasButton buttonUsed"><span class="condition">(if Button is Used)</span>: +0.5 if this player was the first to pass during the main phase.</li>
</ul>
</li>
</ul>

</div>

<hr>

<div class="flex-vertical">
<span class="note scoringTerritory taxSeki taxAll">
<span><hr></span>
<span id="note1">[1]
The intent is "independent-life-regions" indicate regions that are not seki, so long as both players finish all borders and fill all dame. This is motivated by the way Japanese rules attempt to define "seki" using dame. Using the presence of dame to determine seki is actually a pretty clever solution - my original idea had only been to use ability-to-make-pass-alive-ness, but this is considerably more awkward in practice than using dame.
<br/><br/>
We also include the condition of "atari" to handle groups that have no dame but still survive without two eyes by virtue of having ko mouths. This handles <a href="https://senseis.xmp.net/?DoubleKoSeki">double ko seki</a>.
(<a href="#ref1">Back</a>)</span></span>

<span class="note selfPlayOptsEnabled">
<span><hr></span>
<span id="note2">[2]
Pass-aliveness can be computed by a straightforward algorithm: <a href="https://en.wikipedia.org/wiki/Benson%27s_algorithm_(Go)">https://en.wikipedia.org/wiki/Benson%27s_algorithm_(Go)</a>. Note that a slight adjustment to the algorithm presented is technically needed if multi-stone suicide is allowed.
(<a href="#ref3">Back</a>)</span></span>

<span class="note selfPlayOptsEnabled">
<span><hr></span>
<span id="note3">[3]
Under this definition, it is possible that a region with one completely interior point is pass-alive-territory but the addition of a single stone on that interior point results in the region no longer being considered pass-alive-territory because the single stone is not a pass-alive-group. We ignore this minor "flaw" since it makes for a simpler definition and algorithmic implementation.
(<a href="#ref3">Back</a>)</span></span>

<span class="note koSimple">
<span><hr></span>
<span id="note4">[4]
This <a href="https://senseis.xmp.net/?SpightRules">Spight-style</a> termination condition ensures that sending-two-returning-one-like positions will terminate, even under area scoring where the cycle does not "cost" points. It also cuts it shorter under territory scoring, so that a badly behaving bot doesn't lose by ~infinity.
<br/><br/>
The approach taken taken by many Chinese tournaments is to simply prohibit sending-two-returning-one. (Chinese written rules appear to say positional superko, but this written rule is often not used for real tournaments). This would also be easy to implement and KataGo could easily choose to support it in the future, since for all practical purposes a neural net trained under simple ko rules should work fine without modification in an engine that bans sending-two-returning-one.
<br/><br/>
However, there is also <a href="https://senseis.xmp.net/?SendingThreeReturningOne"> sending-three-returning-one</a> - and perhaps there are others messy cases too, that one would imagine professional players balking at allowing despite not having formally listed and prohibited them ahead of time in written rules. Spight's condition is a much cleaner way to handle them for now.
<br/><br/>
Some <a href="https://senseis.xmp.net/?ComputerOlympiadRules"> computer tournament rules</a> handle this by simply declaring long cycles to be draws/wins/losses depending on number of stones captured. It would be easy trivial to support these too in the future if needed and probably would not in practice require retraining a neural net either. But for now, no actual human rulesets use this rule, and even in the computer world, positional or situational superko are often more popular.
(<a href="#ref4">Back</a>)</span></span>

<span class="note koSimple">
<span><hr></span>
<span id="note5">[5]
Under some real-life human rules, an unbounded cycle would not end the game in and of itself at exactly such a point, rather the game may be manually adjudicated as a no-result. But our goal here is to get a formalization of Japanese-like ko rules for computer self-play, so dictating a precise ending point is necessary. The requirement for no intervening passes makes absolutely sure that we do not no-resultify sending-two-returning-one style positions, even with weird unforeseen move orderings.
(<a href="#ref5">Back</a>)</span></span>

<span class="note scoringTerritory">
<span><hr></span>
<span id="note6">[6]
Why have two phases instead of just one?
<br/><br/>
The intent is that the first phase introduces changes to the ko rules alone, allowing any positions destabilized by it to settle down. Then, the second phase additionally introduces a +1 point per move that allows players to actually begin capturing dead stones without loss of points. If both changes were introduced at once, in some cases, this leads to a highly non-intuitive "pass fight" that is absent from true Japanese rules. This can occur if a protective move becomes necessary once the ko rules change - then we may see players exchange ko threats to try to be not the second to pass and therefore to be first to play in cleanup, since being first to play in cleanup would enable making the protective move with +1 point instead of with +0 points.
<br/><br/>
Introducing the ko rule and score bonus changes in separate phases eliminates this issue.
(<a href="#ref6">Back</a>)</span></span>

<span class="note scoringTerritory">
<span><hr></span>
<span id="note7">[7]
The unblock-ko-recapture action is effectively the Japanese rules's "pass for ko". We name it this way to avoid calling it a pass, since it shares little else in common with a pass with regard to the rules necessary to make cleanup work. Also, highly conveniently, an unblock-ko-recapture for a ko-move location is always mutually exclusive with a legal move for that location, which means we have no need to change the protocol for GTP or introduce new move encodings. We can continue to use the exact same 19x19 + 1 encodings in all existing protocols to represent moves.
(<a href="#ref7">Back</a>)</span></span>

<span class="note scoringTerritory">
<span><hr></span>
<span id="note8">[8]
This condition prevents a <a href="https://senseis.xmp.net/?DoubleKoSeki">double ko seki</a> from looping forever in the cleanup phase, at least in the simplest cases, in theory. It must depend on the exact grid coloring rather than be a general prohibition on continuing to unblock and recapture a ko or kos over and over because if the seki is temporary such that one side can capture a surrounding group to collapse it, we must make sure capturing into the ko is not prohibited at that point.
<br/><br/>
Unfortunately, as stated, this rule still allows quite a large amount of game-prolonging due to double-ko-seki, which makes it not ideal for selfplay. Is there a better formulation that is still clean to state and implement, that limits the ability of the attacker to fruitlessly cycle the double-ko-seki?
(<a href="#ref8">Back</a>)</span></span>

<span class="note scoringTerritory">
<span><hr></span>
<span id="note9">[9]
This <a href="https://senseis.xmp.net/?SpightRules">Spight-style</a> termination condition ensures that sending-two-returning-one-type positions will terminate, even during the second cleanup phase when the cycle no longer "costs" points.
(<a href="#ref9">Back</a>)</span></span>

<span class="note scoringTerritory">
<span><hr></span>
<span id="note10">[10]
The "color at the start of the second cleanup phase" condition prevents one-sided dame from granting points to the side able to fill the dame.
<br/><br/>
We go ahead and have an allowance for Button Go here too. This may seem odd, since normally the intent is as a way obtain territory-scoring granularity with area-scoring, so if already using territory-scoring, why would one want such a rule? But at least when we came to implement it in KataGo, it seemed programatically no more complex (simpler, even) to just have it as an option always, and formulating it this way makes for more natural extension to Coupon Go if desired, which *does* make sense in territory scoring. KataGo for now does NOT actually support territory + button though.
(<a href="#ref10">Back</a>)</span></span>

<span class="note whiteHandicapBonus0 whiteHandicapBonusN1 whiteHandicapBonusN">
<span><hr></span>
<span id="note11">[11]
A number of human rulesets allow for Black to begin with N >= 2 stones on the board when playing a game between two differently-skilled players, with these stones being placed in a ruleset-specified way, and with White making the first actual game move instead of Black. For various reasons, during handicap games many of these rulesets give White bonus points based on N, additionally on top of any komi or other settings. Some rulesets give 0 points, some give N-1 points, and some give N points.
(<a href="#ref11">Back</a>)</span></span>

</div>

</body>
</html>
