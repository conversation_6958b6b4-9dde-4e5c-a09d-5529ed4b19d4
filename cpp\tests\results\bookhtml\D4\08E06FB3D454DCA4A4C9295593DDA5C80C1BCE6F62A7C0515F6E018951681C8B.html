
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 6;
const board = [0,0,2,0,0,0,0,0,0,1,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,2],],'p':0.0048,'wl':-0.5030,'ssM':0.62,'wlRad':0.1151,'sRad':0.99,'v':95,'av':95,},{'xy':[[3,0],],'p':0.5031,'wl':-0.1605,'ssM':0.10,'wlRad':0.0810,'sRad':0.81,'v':105,'av':105,},{'xy':[[1,1],],'p':0.1710,'wl':-0.2212,'ssM':0.73,'wlRad':0.2242,'sRad':0.78,'v':125,'av':125,},{'xy':[[3,3],],'p':0.1366,'wl':0.0701,'ssM':0.39,'wlRad':0.0417,'sRad':0.60,'v':153,'av':153,},{'xy':[[1,3],],'p':0.4507,'wl':0.5399,'ssM':0.82,'wlRad':0.0621,'sRad':0.54,'v':30,'av':30,},{'xy':[[0,1],],'p':0.7686,'wl':0.7799,'ssM':0.74,'wlRad':0.0739,'sRad':0.51,'v':32,'av':32,},{'move':'other','p':0.3140,'wl':0.6559,'ssM':-0.50,'wlRad':0.0982,'sRad':0.67,'v':13,'av':13,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
