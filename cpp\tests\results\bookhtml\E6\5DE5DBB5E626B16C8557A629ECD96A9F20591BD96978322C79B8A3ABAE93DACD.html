
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 4;
const board = [0,0,1,0,0,0,0,0,0,0,0,0,0,0,2,0,];
const links = {};
const linkSyms = {};
const moves = [{'move':'pass','p':0.8713,'wl':-0.7922,'ssM':-0.44,'wlRad':0.1422,'sRad':0.72,'v':15,'av':15,},{'xy':[[0,1],],'p':0.5977,'wl':-0.6695,'ssM':-0.27,'wlRad':0.0656,'sRad':1.23,'v':10,'av':10,},{'xy':[[1,2],],'p':0.3565,'wl':0.2836,'ssM':-1.31,'wlRad':0.0114,'sRad':0.62,'v':13,'av':13,},{'xy':[[0,3],],'p':0.5973,'wl':0.2970,'ssM':1.10,'wlRad':0.2502,'sRad':1.11,'v':68,'av':68,},{'xy':[[3,1],],'p':0.9432,'wl':0.8085,'ssM':1.08,'wlRad':0.0256,'sRad':0.71,'v':75,'av':75,},{'xy':[[2,1],],'p':0.0701,'wl':0.9792,'ssM':0.62,'wlRad':0.1167,'sRad':0.50,'v':146,'av':146,},{'move':'other','p':0.4392,'wl':0.4909,'ssM':0.54,'wlRad':0.1690,'sRad':1.49,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
