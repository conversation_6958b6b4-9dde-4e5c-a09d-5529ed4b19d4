
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 2;
const board = [0,0,0,0,2,0,0,0,0,0,0,1,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,0],],'p':0.4013,'wl':-0.6440,'ssM':-0.92,'wlRad':0.0732,'sRad':1.07,'v':12,'av':12,},{'xy':[[0,2],],'p':0.6699,'wl':-0.4042,'ssM':-0.67,'wlRad':0.0296,'sRad':0.95,'v':10,'av':10,},{'xy':[[1,3],],'p':0.3724,'wl':-0.4651,'ssM':0.16,'wlRad':0.0041,'sRad':0.43,'v':15,'av':15,},{'move':'pass','p':0.1730,'wl':-0.4581,'ssM':0.79,'wlRad':0.1356,'sRad':0.77,'v':17,'av':17,},{'xy':[[2,0],],'p':0.7250,'wl':-0.3937,'ssM':1.03,'wlRad':0.0140,'sRad':1.03,'v':19,'av':19,},{'xy':[[0,3],],'p':0.6969,'wl':0.2278,'ssM':0.49,'wlRad':0.2750,'sRad':0.89,'v':22,'av':22,},{'xy':[[0,0],],'p':0.5157,'wl':0.4138,'ssM':0.74,'wlRad':0.0317,'sRad':1.42,'v':66,'av':66,},{'xy':[[1,2],],'p':0.6444,'wl':0.5110,'ssM':0.70,'wlRad':0.4631,'sRad':1.21,'v':47,'av':47,},{'xy':[[1,1],],'p':0.8520,'wl':0.5660,'ssM':1.09,'wlRad':0.0967,'sRad':1.21,'v':32,'av':32,},{'xy':[[1,0],],'p':0.9735,'wl':0.5711,'ssM':1.34,'wlRad':0.1112,'sRad':0.55,'v':15,'av':15,},{'xy':[[2,3],],'p':0.6426,'wl':0.8282,'ssM':1.30,'wlRad':0.0398,'sRad':0.44,'v':142,'av':142,},{'xy':[[3,1],],'p':0.6904,'wl':0.9272,'ssM':0.42,'wlRad':0.0424,'sRad':1.05,'v':89,'av':89,},{'move':'other','p':0.0107,'wl':0.2824,'ssM':0.07,'wlRad':0.0449,'sRad':0.88,'v':18,'av':18,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
