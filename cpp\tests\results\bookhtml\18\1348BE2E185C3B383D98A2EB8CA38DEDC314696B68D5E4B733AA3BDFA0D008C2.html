
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 2;
const board = [0,0,0,0,0,0,0,0,0,0,0,1,0,0,2,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,0],],'p':0.5543,'wl':-0.7917,'ssM':-0.02,'wlRad':0.0768,'sRad':0.47,'v':19,'av':19,},{'xy':[[0,3],],'p':0.1456,'wl':-0.6769,'ssM':0.94,'wlRad':0.0152,'sRad':0.65,'v':17,'av':17,},{'xy':[[1,2],],'p':0.2405,'wl':-0.1911,'ssM':1.16,'wlRad':0.1862,'sRad':1.54,'v':30,'av':30,},{'xy':[[0,2],],'p':0.1740,'wl':0.0728,'ssM':0.49,'wlRad':0.0932,'sRad':1.96,'v':13,'av':13,},{'xy':[[3,1],],'p':0.8399,'wl':0.2937,'ssM':-0.01,'wlRad':0.0353,'sRad':1.25,'v':10,'av':10,},{'move':'pass','p':0.9074,'wl':0.3823,'ssM':1.14,'wlRad':0.0494,'sRad':0.66,'v':31,'av':31,},{'xy':[[2,2],],'p':0.6891,'wl':0.6007,'ssM':-0.64,'wlRad':0.0902,'sRad':0.57,'v':17,'av':17,},{'xy':[[0,1],],'p':0.6320,'wl':0.7279,'ssM':-0.22,'wlRad':0.2371,'sRad':0.66,'v':14,'av':14,},{'xy':[[0,0],],'p':0.8093,'wl':0.7511,'ssM':-0.16,'wlRad':0.0162,'sRad':0.69,'v':80,'av':80,},{'move':'other','p':0.0451,'wl':0.7497,'ssM':-0.10,'wlRad':0.0810,'sRad':0.86,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
