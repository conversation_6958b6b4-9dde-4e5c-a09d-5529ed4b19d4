
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 0;
const board = [2,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,3],],'p':0.5235,'wl':-0.9953,'ssM':-1.24,'wlRad':0.1629,'sRad':0.27,'v':19,'av':19,},{'xy':[[3,1],],'p':0.3360,'wl':-0.2561,'ssM':0.00,'wlRad':0.0209,'sRad':1.30,'v':20,'av':20,},{'xy':[[3,2],],'p':0.6556,'wl':-0.3196,'ssM':1.15,'wlRad':0.0794,'sRad':0.66,'v':30,'av':30,},{'xy':[[1,0],],'p':0.7647,'wl':0.3337,'ssM':0.79,'wlRad':0.0168,'sRad':0.77,'v':85,'av':85,},{'xy':[[1,2],],'p':0.7379,'wl':0.4987,'ssM':1.39,'wlRad':0.0030,'sRad':0.94,'v':32,'av':32,},{'xy':[[0,1],],'p':0.1203,'wl':0.5602,'ssM':0.77,'wlRad':0.0362,'sRad':0.91,'v':55,'av':55,},{'xy':[[3,0],],'p':0.3666,'wl':0.7118,'ssM':-0.72,'wlRad':0.0086,'sRad':0.74,'v':18,'av':18,},{'xy':[[2,2],],'p':0.8217,'wl':0.6678,'ssM':0.52,'wlRad':0.2777,'sRad':0.36,'v':19,'av':19,},{'xy':[[2,3],],'p':0.3928,'wl':0.8925,'ssM':0.02,'wlRad':0.1080,'sRad':0.80,'v':13,'av':13,},{'xy':[[2,1],],'p':0.9702,'wl':0.9696,'ssM':1.57,'wlRad':0.0696,'sRad':2.05,'v':88,'av':88,},{'move':'other','p':0.0289,'wl':0.1556,'ssM':1.60,'wlRad':0.0286,'sRad':1.46,'v':15,'av':15,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
