本文档整理了KataGo论文中所有与蒙特卡洛树搜索(Monte-Carlo Tree Search, MCTS)相关的内容。

## 2 Basic Architecture and Parameters

Although varying in many minor details, KataGo's overall architecture resembles the AlphaGoZero and AlphaZero architectures [18, 17].

KataGo plays games against itself using Monte-Carlo tree search (MCTS) guided by a neural net to generate training data. Search consists of growing a game tree by repeated playouts. Playouts start from the root and descend the tree, at each node $n$ choosing the child $c$ that maximizes:

$\text{PUCT}(c) = V(c) + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$

where $V(c)$ is the average predicted utility of all nodes in $cs subtree, $P(c)$ is the policy prior of $c$ from the neural net, $N(c)$ is the number of playouts previously sent through child $c$, and $c_{\text{PUCT}} = 1.1$. Upon reaching the end of the tree and finding that the next chosen child is not

---

²https://github.com/lightvector/KataGo. Using our code, it is possible to reach strong or top human amateur strength starting from nothing on even single GPUs in mere days, and several people have in fact already done so.

allocated, the playout terminates by appending that single child to the tree³.

Like AlphaZero, to aid discovery of unexpected moves, KataGo adds noise to the policy prior at the root:

$P(c) = 0.75 P_{\text{raw}}(c) + 0.25 \eta$

where $\eta$ is a draw from a Dirichlet distribution on legal moves with parameter $\alpha = 0.03 \cdot 19^2/N(c)$ where $N$ is the total number of legal moves. This matches AlphaZero's $\alpha = 0.03$ on the empty $19 \times 19$ Go board while scaling to other sizes. KataGo also applies a softmax temperature at the root of 1.03, an idea to improve policy convergence stability from SAI, another AlphaGoZero replication [13].

The neural net guiding search is a convolutional residual net with a preactivation architecture [7], with a trunk of $b$ residual blocks with $c$ channels. Similar to Leela Zero [14], KataGo began with small nets and progressively increased their size, concurrently training the next larger size on the same data and switching when its average loss caught up to the smaller size. In KataGo's main 19-day run, $(b, c)$ began at $(6, 96)$ and switched to $(10, 128)$, $(15, 192)$, and $(20, 256)$, at roughly 0.75 days, 1.75 days, and 7.5 days, respectively. The final size matches that of AlphaZero and ELF.

The neural net has several output heads. Sampling positions from the self-play games, a policy head predicts probable good moves while a game outcome value head predicts if the game was ultimately won or lost. The loss function is:

$L = -c_g \sum_r z(r) \log(\hat{z}(r)) - \sum_m \pi(m) \log(\hat{\pi}(m)) + c_{L2} \|\theta\|^2$

where $r \in \{\text{win}, \text{loss}\}$ is the outcome for the current player, $z$ is a one-hot encoding of it, $\hat{z}$ is the neural net's prediction of $z$, $m$ ranges over the set of possible moves, $\pi$ is a target policy distribution derived from the playouts of the MCTS search, $\hat{\pi}$ is the prediction of $\pi$, $c_{L2} = 3e-5$ sets an L2 penalty on the model parameters $\theta$, and $c_g = 1.5$ is a scaling constant. As described in later sections, we also add additional terms corresponding to other heads that predict auxiliary targets.

Training uses stochastic gradient descent with a momentum decay of 0.9 and a batch size of 256 (the largest size fitting on one GPU). It uses a fixed per-sample learning rate of 6e-5, except that the first 5 million samples (merely a few percent of the total steps) use a rate of 2e-5 to reduce instability from early large gradients. In KataGo's main run, the per-sample learning rate was also dropped to 6e-6 starting at about 17.5 days to maximize final strength. Samples are drawn uniformly from a growing moving window of the most recent data, with window size beginning at 250,000 samples and increasing to about 22 million by the end of the main run. See Appendix C for details.

Training uses a version of stochastic weight averaging [9]. Every roughly 250,000 training samples, a snapshot of the weights is saved, and every four snapshots, a new candidate neural net is produced by taking an exponential moving average of snapshots with decay $\lambda = 0.75$ (averaging four snapshots of lookback). Candidate nets must pass a gating test by winning at least 100 out of 200 test games against the current net to become the new net for self-play. See Appendix E for details.

In total, KataGo's main run lasted for 19 days using a maximum of 28 V100 GPUs at any time (averaging 26-27) and generated about 241 million training samples across 4.2 million games. Self-

---

³When $N(c) = 0$ and $V(c)$ is undefined, unlike AlphaZero but like Leela Zero, we define: $V(c) = V(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}}$ where $P_{\text{explored}} = \sum_{c': N(c')>0} P(c')$ is the total policy of explored children and $c_{\text{FPU}} = 0.2$ is a first-play-urgency reduction coefficient, except $c_{\text{FPU}} = 0$ at the root if Dirichlet noise is enabled.

play games used Tromp-Taylor rules [21] modified to not require capturing stones within pass-alive territory⁴. Ko, suicide, and komi rules also varied from Tromp-Taylor randomly, and some proportion of games were randomly played on smaller boards⁵. See Appendix D for other details.

## 3 Major General Improvements

### 3.1 Playout Cap Randomization

One of the major improvements in KataGo's training process over AlphaZero is to randomly vary the number of playouts on different turns to relieve a major tension between policy and value training.

In the AlphaZero process, the game outcome value target is highly data-limited, with only one noisy binary result per entire game. Holding compute fixed, it would likely be beneficial for value training to use only a small number of playouts per turn to generate more games, even if those games are of slightly lower quality. For example, in the first version of AlphaGo, self-play using only a single playout per turn (i.e., directly using the policy) was still of sufficient quality to train a decent value net [16].

However, informal prior research by Forsten (2019) [6] has suggested that at least in Go, ideal numbers of playouts for policy learning are much larger, not far from AlphaZero's choice of 800 playouts per move [17]. Although the policy gets many samples per game, unless the number of playouts is larger than ideal for value training, the search usually does not deviate much from the policy prior, so the policy does not readily improve.

We introduce playout cap randomization to mitigate this tension. On a small proportion $p$ of turns, we perform a full search, stopping when the tree reaches a cap of $N$ nodes, and for all other turns we perform a fast search with a much smaller cap of $n < N$. Only turns with a full search are recorded for training. For fast searches, we also disable Dirichlet noise and other explorative settings, maximizing strength. For KataGo's main 19-day run, we chose $p = 0.25$ and $(N, n) = (600, 100)$ initially, annealing up to $(1000, 200)$ after the first two days of training.

Because most moves use a fast search, more games are played, improving value training. But since $n$ is small, fast searches cost only a limited fraction of the computation time, so the drop in the number of good policy samples per computation time is not large. The ablation studies presented in section 5.2 indicate that playout cap randomization indeed outperforms a variety of fixed numbers of playouts.

### 3.2 Forced Playouts and Policy Target Pruning

Like AlphaZero and other implementations such as ELF and Leela Zero, KataGo uses the final root playout distribution from MCTS to produce the policy target for training. However, KataGo does

---

⁴In Go, a version of Benson's algorithm [1] can prove areas safe even given unboundedly many consecutive opponent moves ("pass-alive"), enabling this minor optimization.

⁵Almost all major AlphaZero reproductions in Go have been hardcoded to fixed board sizes and rules. Although not the focus of this paper, KataGo's randomization allows training a single model that generalizes across all these variations.

not use the raw distribution. Instead, we introduce policy target pruning, a new method which enables improved exploration via forced playouts.

We observed in informal tests that even if a Dirichlet noise move was good, its initial evaluation might be negative, preventing further search and leaving the move undiscovered. Therefore, for each child $c$ of the root that has received any playouts, we ensure it receives a minimum number of forced playouts based on the noised policy and the total sum of playouts so far:

$n_{\text{forced}}(c) = \left(kP(c) \sum_{c'} N(c')\right)^{1/2}$

We do this by setting the MCTS selection urgency PUCT$(c)$ to infinity whenever a child of the root has fewer than this many playouts. The exponent of $1/2 < 1$ ensures that forced playouts scale with search but asymptotically decay to a zero proportion for bad moves, and $k = 2$ is large enough to actually force a small percent of playouts in practice.

However, the vast majority of the time, noise moves are bad moves, and in AlphaZero since the policy target is the playout distribution, we would train the policy to predict these extra bad playouts. Therefore, we perform a policy target pruning step. In particular, we identify the child $c^*$ with the most playouts, and then from each other child $c$, we subtract up to $n_{\text{forced}}$ playouts so long as it does not cause PUCT$(c) \geq$ PUCT$(c^*)$, holding constant the final utility estimate for both. This subtracts all extra playouts that normal PUCT would not have chosen on its own, unless a move was found to be good. Additionally, we outright prune children that are reduced to a single playout. See Figure 1 for a visualization of the effect on the learned policy.

![Figure 1: Log policy of 10-block nets, white to play. Left: trained with forced playouts and policy target pruning. Right: trained without. Dark/red through bright green ranges from about p = 2e-4 to p = 1. Pruning reduces the policy mass on many bad moves near the edges.]

The critical feature of such pruning is that it allows decoupling the policy target in AlphaZero from the dynamics of MCTS or the use of explorative noise. There is no reason to expect the optimal level of playout dispersion in MCTS to also be optimal for the policy target and the long-term convergence of the neural net. Our use of policy target pruning with forced playouts, though an improvement, is only a simple application of this method. We are eager to explore others in the future, including alterations to the PUCT formula itself⁶.

### PUCT公式特性

The PUCT formula $V(c) + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1+N(c)}$ has the property that if $V$ is constant, then playouts will be roughly proportional to $P$. Informal tests suggest this is important to the convergence of $P$, and without something like target pruning, alternate formulas can disrupt training even when improving match strength.

### 5.1 Testing Versus ELF and Leela Zero

We tested KataGo against ELF and Leela Zero 0.17 using their publicly-available source code and trained networks.

We sampled roughly every fifth Leela Zero neural net over its training history from LZ30 through LZ225, the last several networks well exceeding even ELF's strength. Between every pair of Leela Zero nets fewer than 35 versions apart, we played about 45 games to establish approximate relative strengths of the Leela Zero nets as a benchmark.

We also sampled KataGo over its training history, for each version playing batches of games versus random Leela Zero nets with frequency proportional to the predicted variance $p(1-p)$ of the game result. The winning chance $p$ was continuously estimated from the global Bayesian maximum likelihood Elo based on all game results so far⁹. This ensured that games would be varied yet informative. We also ran ELF's final V2 neural network using Leela Zero's engine¹⁰, with ELF playing against both Leela Zero and KataGo using the same opponent sampling.

Games used a 19×19 board with a fixed 7.5 komi under Tromp-Taylor rules, with a fixed 1600 visits, resignation below 2% winrate, and multithreading disabled. To encourage opening variety, both bots randomized with a temperature of 0.2 in the first 20 turns. Both also used a lower confidence-bound move selection method to improve match strength [15]. Final Elo ratings were based on the final set of about 21000 games.

To compare the efficiency of training, we computed a crude indicative metric of total self-play computation by modeling a neural net with $b$ blocks and $c$ channels as having cost $bc^2$ per query¹¹. For KataGo we just counted self-play queries for each size and multiplied. For ELF, we approximated queries by sampling the average game length from its public training data and multiplied by ELF's 1600 playouts per move, discounting by 20% to roughly account for transposition caching. For Leela Zero we estimated it similarly, also interpolating costs where data was missing¹². Leela Zero also generated data using ELF's prototype networks, but we did not attempt to estimate this cost¹³.

KataGo compares highly favorably with both ELF and Leela Zero. Shown in Figure 4 is a plot of Elo ratings versus estimated compute for all three. KataGo outperforms ELF in learning efficiency under this metric by about a factor of 50. Leela Zero appears to outperform ELF as well, but the Elo ratings would be expected to unfairly favor Leela since its final network size is 40 blocks, double

---

⁹Using a custom implementation of BayesElo [5].

¹⁰ELF and Leela Zero neural nets are inter-compatible.

¹¹This metric was chosen in part as a very rough way to normalize out hardware and engineering differences. For KataGo, we also conservatively computed costs under this metric as if all queries were on the full 19×19 board.

¹²Due to online hosting issues, some Leela Zero training data is no longer publicly available.

¹³At various points, Leela Zero also used data from stronger ELF OpenGo nets, likely causing it to learn faster than it would unaided. We did not attempt to count the cost of this additional data.

![Figure 4: 1600-visit Elo progression of KataGo (blue, leftmost) vs. Leela Zero (red, center) and ELF (green diamond). X-axis: self-play cost in billions of equivalent 20 block × 256 channel queries. Note the log-scale. Leela Zero's costs are highly approximate.]

| Match Settings                | Wins v ELF | Elo Diff |
| ----------------------------- | ---------- | -------- |
| 1600 playouts/mv, no batching | 239 / 400  | 69 ± 36  |
| 9.0 secs/mv, ELF batchsize 16 | 246 / 400  | 81 ± 36  |
| 7.5 secs/mv, ELF batchsize 32 | 254 / 400  | 96 ± 37  |

**Table 1:** KataGo match results versus ELF, with the implied Elo difference (plus or minus two std. deviations of confidence).

that of ELF, and the ratings are based on equal search nodes rather than GPU cost. Additionally, Leela Zero's training occurred over multiple years rather than ELF's two weeks, reducing latency and parallelization overhead. Yet KataGo still outperforms Leela Zero by a factor of 10 despite the same network size as ELF and a similarly short training time. Early on, the improvement factor appears larger, but partly this is because the first 10%-15% of Leela Zero's run contained some bugs that slowed learning.

We also ran three 400-game matches on a single V100 GPU against ELF using ELF's native engine. In the first, both sides used 1600 playouts/move with no batching. In the second, KataGo used 9s/move (16 threads, max batch size 16) and ELF used 16,000 playouts/move (batch size 16), which ELF performs in 9 seconds. In the third, we doubled ELF's batch size, improving its nominal speed to 7.5s/move, and lowered KataGo to 7.5s/move. As summarized in Table 1, in all three matches KataGo defeated ELF, confirming its strength level at both low and high playouts and at both fixed search and fixed wall clock time settings.

## Appendix D Game Randomization and Termination

KataGo randomizes in a variety of ways to ensure diverse training data so as to generalize across a wide range of rulesets, board sizes, and extreme match conditions, including handicap games and positions arising from mistakes or alternative moves in human games that would not occur in self-play.

• Games are randomized uniformly between positional versus situational superko rules, and between suicide moves allowed versus disallowed.

• Games are randomized in board size, with 37.5% of games on 19×19 and increasing in KataGo's main run to 50% of games after two days of training. The remaining games are triangularly distributed from 9×9 to 18×18, with frequency proportional to $1, 2, \ldots, 10$.

• Rather than using only a standard komi of 7.5, komi is randomized by drawing from a normal distribution with mean 7 and standard deviation 1, truncated to 3 standard deviations and rounding to the nearest integer or half-integer. However, 5% of the time, a standard deviation of 10 is used instead, to give experience with highly unusual values of komi.

• To enable experience with handicap game positions, 5% of games are played as handicap games, where Black gets a random number of additional free moves at the start of the game, chosen randomly using the raw policy probabilities. Of those games, 90% adjust komi to compensate White for Black's advantage based on the neural net's predicted final score difference. The maximum number of free Black moves is 0 (no handicap) for board sizes 9 and 10, 1 for board sizes 11 to 14, 2 for board sizes 15 to 18, and 3 for board size 19.

• To initialize each game and ensure opening variety, the first $r$ moves of a game are played randomly directly proportionally to the raw policy distribution of the net, where $r$ is drawn from an exponential distribution with mean $0.04 \times b^2$. where $b$ is the width of the board, and during the game, moves are selected proportionally to the target-pruned MCTS playout distribution raised to the power of $1/T$ where $T$ is a temperature constant. $T$ begins at 0.8 and decays smoothly to 0.2, with a halflife in turns equal to the width of the board $b$. These achieve essentially the same result to AlphaZero or ELF's temperature scaling in the first 30 moves of the game, except scaling with board size and varying more smoothly.

• In 2.5% of positions, the game is branched to try an alternative move drawn randomly from the policy of the net 70% of the time with temperature 1, 25% of the time with temperature 2, and otherwise with temperature infinity. A full search is performed to produce a policy training sample (the MCTS search winrate is used for the game outcome target and the score and ownership targets are left unconstrained). This ensures that there is a small percentage of training data on how to respond to or refute moves that a full search might not play. Recursively, a random quarter of these branches are continued for an additional move.

• In 5% of games, the game is branched after the first $r$ turns where $r$ is drawn from an exponential distribution with mean $0.025 \times b^2$. Between 3 and 10 moves are chosen uniformly at random, each given a single neural net evaluation, and the best one is played. Komi is adjusted to be fair. The game is then played to completion as normal. This ensures that there is always a small percentage of games with highly unusual openings.

Except for introducing a minimum necessary amount of entropy, the above settings very likely have only a limited effect on overall learning efficiency and strength. They were used primarily so that KataGo would have experience with alternate rules, komi values, handicap openings, and positions where both sides have played highly suboptimally in ways that would never normally occur in high-level play, making it more effective as a tool for human amateur game analysis.

Additionally, unlike in AlphaZero or in ELF, games are played to completion without resignation. However, during self-play if for 5 consecutive turns, the MCTS winrate estimate $p$ for the losing side has been less than 5%, then to finish the game faster the number of visits is capped to $n + (1-\lambda)N$ where $n$ and $N$ are the small and large limits used in playout cap randomization and $\lambda = p/0.05$ is the proportion of the way that $p$ is from 5% to 0%. Additionally, training samples are recorded with only $0.1 + 0.9\lambda$ probability, stochastically downweighting training on positions where AlphaZero would have resigned.

Relative to resignation, continuing play with reduced visit caps costs only slightly more but results in cleaner and less biased training targets, reduces infrastructural complexity such as monitoring for the rate of incorrect resignations, and enables the final ownership and final score targets to be easily computed. Since KataGo secondarily optimizes for score rather than just win/loss (see Appendix F), continued play itself also still provides some learning value since optimizing score can give a good signal even in won/lost positions.

## Appendix E Gating

Similar to AlphaGoZero, candidate neural nets must pass a gating test to become the new net for self-play. Gating in KataGo is fairly lightweight - candidates need only win at least 100 out of 200 games against the current self-play neural net. Gating games use a fixed cap of 300 search tree nodes (increasing in KataGo's main run to 400 after 2 days), with the following parameter changes to minimize noise and maximize performance:

• The rules and board size are still randomized but komi is not randomized and is fixed at 7.5.

• Handicap games and branching are disabled.

• From the first turn, moves are played using full search rather than using the raw policy to play some of the first moves.

• The temperature $T$ for selecting a move based on the MCTS playout distribution starts at 0.5 instead of 0.8.

• Dirichlet noise and forced playouts and visit cap oscillation are disabled, tree reuse is enabled.

• The root uses $c_{\text{FPU}} = 0.2$ just the same as the rest of the search tree instead of $c_{\text{FPU}} = 0.0$.

• Resignation is enabled, occurring if both sides agree that for the last 5 turns, the worst MCTS winrate estimate $p$ for the losing side has on each turn been less than 5%.

## Appendix F Score Maximization

Unlike most other Go bots learning from self-play, KataGo puts nonzero utility on maximizing (a dynamic monotone function of) the score difference, to improve use for human game analysis and handicap game play.

Letting $x$ be the final score difference of a game, in addition to the utility for winning/losing:
$u_{\text{win}}(x) = \text{sign}(x) \in \{-1, 1\}$

We also define the score utility:
$u_{\text{score}}(x) = c_{\text{score}} f\left(\frac{x - x_0}{b}\right)$

where $c_{\text{score}}$ is a parameter controlling the relative importance of score, $x_0$ is a parameter for centering the utility curve, $b \in [9, 19]$ is the width of the board and $f : \mathbb{R} \to (-1, 1)$ is the function:
$f(x) = \frac{2}{\pi} \arctan(x)$

![Figure 8: Total utility as a function of score difference, when $x_0 = 0$ and $b = 19$ and $c_{\text{score}} = 0.5$.]

At the start of each search, the utility is re-centered by setting $x_0$ to the mean $\hat{\mu}_s$ of the neural net's predicted score distribution at the root node. The search proceeds with the aim to maximize the sum of $u_{\text{win}}$ and $u_{\text{score}}$ instead of only $u_{\text{win}}$. Estimates of $u_{\text{win}}$ are obtained using the game outcome value prediction of the net as usual, and estimates of $u_{\text{score}}$ are obtained by querying the neural net for the mean and variance $\hat{\mu}_s$ and $\hat{\sigma}_s^2$ of its predicted score distribution, and computing:

$E(u_{\text{score}}) \approx \int_{-\infty}^{\infty} u_{\text{score}}(x) N(x; \hat{\mu}_s, \hat{\sigma}_s^2) dx$

where the integral on the right is estimated quickly by interpolation in a precomputed lookup table.

Since similar to a sigmoid $f$ saturates far from 0, this provides an incentive for improving the score in simple and likely ways near $x_0$ without awarding overly large amounts of expected utility for pursuing unlikely but large gains in score or shying away from unlikely but large losses in score. For KataGo's main run, $c_{\text{score}}$ was initialized to 0.5, then adjusted 0.4 after the first two days of training.

# KataGo中的蒙特卡洛树搜索(MCTS)数学公式总结

## 1. AlphaZero原始MCTS公式

### 1.1 PUCT选择公式
在每个节点n选择子节点c时，使用PUCT公式：

$$\text{PUCT}(c) = V(c) + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$$

其中：
- $V(c)$：子节点c子树中所有节点的平均预测效用
- $P(c)$：神经网络给出的策略先验概率
- $N(c)$：通过子节点c的模拟次数
- $c_{\text{PUCT}} = 1.1$：PUCT常数

### 1.2 根节点噪声添加
为了促进探索意外走法，在根节点添加Dirichlet噪声：

$$P(c) = 0.75 P_{\text{raw}}(c) + 0.25 \eta$$

其中$\eta$是参数为$\alpha = 0.03 \cdot 19^2/N(c)$的Dirichlet分布采样。

### 1.3 损失函数
神经网络的损失函数为：

$$L = -c_g \sum_r z(r) \log(\hat{z}(r)) - \sum_m \pi(m) \log(\hat{\pi}(m)) + c_{L2} \|\theta\|^2$$

其中：
- $c_g = 1.5$：游戏结果缩放常数
- $c_{L2} = 3e-5$：L2正则化系数

## 2. KataGo的MCTS改进

### 2.1 首次游戏紧迫性(FPU)
当$N(c) = 0$且$V(c)$未定义时：

$$V(c) = V(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}}$$

其中：
- $P_{\text{explored}} = \sum_{c': N(c')>0} P(c')$：已探索子节点的总策略概率
- $c_{\text{FPU}} = 0.2$：首次游戏紧迫性降低系数
- 在根节点启用Dirichlet噪声时，$c_{\text{FPU}} = 0$

### 2.2 强制模拟次数
对于根节点的每个子节点c，确保其获得基于噪声策略和总模拟次数的最小强制模拟次数：

$$n_{\text{forced}}(c) = \left(kP(c) \sum_{c'} N(c')\right)^{1/2}$$

其中$k = 2$是强制参数。

### 2.3 策略目标剪枝
识别模拟次数最多的子节点$c^*$，然后从其他子节点c中减去最多$n_{\text{forced}}$次模拟，条件是不会导致PUCT$(c) \geq$ PUCT$(c^*)$。

### 2.4 模拟上限随机化
- 在比例$p$的回合中执行完整搜索（上限$N$个节点）
- 其他回合执行快速搜索（上限$n < N$个节点）
- KataGo主要运行中：$p = 0.25$，$(N, n) = (600, 100)$初始值，退火至$(1000, 200)$

## 3. 得分最大化扩展

### 3.1 效用函数
除了胜负效用外，KataGo还定义得分效用：

$$u_{\text{win}}(x) = \text{sign}(x) \in \{-1, 1\}$$

$$u_{\text{score}}(x) = c_{\text{score}} f\left(\frac{x - x_0}{b}\right)$$

其中：
- $c_{\text{score}}$：得分重要性参数
- $x_0$：效用曲线中心参数
- $b \in [9, 19]$：棋盘宽度
- $f(x) = \frac{2}{\pi} \arctan(x)$：饱和函数

### 3.2 得分效用期望
通过积分计算得分效用的期望：

$$E(u_{\text{score}}) \approx \int_{-\infty}^{\infty} u_{\text{score}}(x) N(x; \hat{\mu}_s, \hat{\sigma}_s^2) dx$$

其中$\hat{\mu}_s$和$\hat{\sigma}_s^2$分别是神经网络预测的得分分布均值和方差。

## 4. 完整的KataGo MCTS公式

### 4.1 增强的PUCT选择公式
结合所有改进的完整PUCT公式：

$$\text{PUCT}(c) = \begin{cases}
V(c) + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)} & \text{if } N(c) > 0 \\
V(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}} + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)} & \text{if } N(c) = 0
\end{cases}$$

当$N(c) < n_{\text{forced}}(c)$时，设置PUCT$(c) = \infty$以强制探索。

### 4.2 根节点策略概率
在根节点应用噪声和温度：

$$P(c) = 0.75 P_{\text{raw}}(c) + 0.25 \eta$$

然后应用softmax温度1.03。

### 4.3 综合效用函数
搜索目标是最大化：

$$U_{\text{total}} = u_{\text{win}}(x) + u_{\text{score}}(x)$$

其中$x_0$在每次搜索开始时重新设置为根节点的预测得分均值$\hat{\mu}_s$。

### 4.4 动态访问上限
在游戏接近结束时，如果连续5回合败方胜率$p < 5\%$，则访问次数上限调整为：

$$\text{visit\_cap} = n + (1-\lambda)N$$

其中$\lambda = p/0.05$，训练样本记录概率为$0.1 + 0.9\lambda$。

这些公式共同构成了KataGo的完整MCTS系统，相比AlphaZero在探索效率、训练稳定性和实用性方面都有显著改进。

# KataGo中的蒙特卡洛树搜索(MCTS)数学公式总结

## 1. AlphaZero原始MCTS公式

### 1.1 PUCT选择公式
在每个节点n选择子节点c时，使用PUCT公式：

$$\text{PUCT}(c) = V(c) + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$$

其中：
- $V(c)$：子节点c子树中所有节点的平均预测效用
- $P(c)$：神经网络给出的策略先验概率
- $N(c)$：通过子节点c的模拟次数
- $c_{\text{PUCT}} = 1.1$：PUCT常数

### 1.2 根节点噪声添加
为了促进探索意外走法，在根节点添加Dirichlet噪声：

$$P(c) = 0.75 P_{\text{raw}}(c) + 0.25 \eta$$

其中$\eta$是参数为$\alpha = 0.03 \cdot 19^2/N(c)$的Dirichlet分布采样。

### 1.3 损失函数
神经网络的损失函数为：

$$L = -c_g \sum_r z(r) \log(\hat{z}(r)) - \sum_m \pi(m) \log(\hat{\pi}(m)) + c_{L2} \|\theta\|^2$$

其中：
- $c_g = 1.5$：游戏结果缩放常数
- $c_{L2} = 3e-5$：L2正则化系数

## 2. KataGo的MCTS改进

### 2.1 首次游戏紧迫性(FPU)
当$N(c) = 0$且$V(c)$未定义时：

$$V(c) = V(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}}$$

其中：
- $P_{\text{explored}} = \sum_{c': N(c')>0} P(c')$：已探索子节点的总策略概率
- $c_{\text{FPU}} = 0.2$：首次游戏紧迫性降低系数
- 在根节点启用Dirichlet噪声时，$c_{\text{FPU}} = 0$

### 2.2 强制模拟次数
对于根节点的每个子节点c，确保其获得基于噪声策略和总模拟次数的最小强制模拟次数：

$$n_{\text{forced}}(c) = \left(kP(c) \sum_{c'} N(c')\right)^{1/2}$$

其中$k = 2$是强制参数。

### 2.3 策略目标剪枝
识别模拟次数最多的子节点$c^*$，然后从其他子节点c中减去最多$n_{\text{forced}}$次模拟，条件是不会导致$\text{PUCT}(c) \geq \text{PUCT}(c^*)$。

### 2.4 模拟上限随机化
- 在比例$p$的回合中执行完整搜索（上限$N$个节点）
- 其他回合执行快速搜索（上限$n < N$个节点）
- KataGo主要运行中：$p = 0.25$，$(N, n) = (600, 100)$初始值，退火至$(1000, 200)$

## 3. 得分最大化扩展

### 3.1 效用函数
除了胜负效用外，KataGo还定义得分效用：

$$u_{\text{win}}(x) = \text{sign}(x) \in \{-1, 1\}$$

$$u_{\text{score}}(x) = c_{\text{score}} f\left(\frac{x - x_0}{b}\right)$$

其中：
- $c_{\text{score}}$：得分重要性参数
- $x_0$：效用曲线中心参数
- $b \in [9, 19]$：棋盘宽度
- $f(x) = \frac{2}{\pi} \arctan(x)$：饱和函数

### 3.2 得分效用期望
通过积分计算得分效用的期望：

$$E(u_{\text{score}}) \approx \int_{-\infty}^{\infty} u_{\text{score}}(x) N(x; \hat{\mu}_s, \hat{\sigma}_s^2) dx$$

其中$\hat{\mu}_s$和$\hat{\sigma}_s^2$分别是神经网络预测的得分分布均值和方差。

## 4. 完整的KataGo MCTS公式

### 4.1 增强的PUCT选择公式
结合所有改进的完整PUCT公式：

当$N(c) > 0$时：
$$\text{PUCT}(c) = V(c) + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$$

当$N(c) = 0$时：
$$\text{PUCT}(c) = V(n) - c_{\text{FPU}} \sqrt{P_{\text{explored}}} + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$$

当$N(c) < n_{\text{forced}}(c)$时，设置$\text{PUCT}(c) = \infty$以强制探索。

### 4.2 根节点策略概率
在根节点应用噪声和温度：

$$P(c) = 0.75 P_{\text{raw}}(c) + 0.25 \eta$$

然后应用softmax温度1.03。

### 4.3 综合效用函数
搜索目标是最大化：

$$U_{\text{total}} = u_{\text{win}}(x) + u_{\text{score}}(x)$$

其中$x_0$在每次搜索开始时重新设置为根节点的预测得分均值$\hat{\mu}_s$。

### 4.4 动态访问上限
在游戏接近结束时，如果连续5回合败方胜率$p < 5\%$，则访问次数上限调整为：

$$\text{visit\_cap} = n + (1-\lambda)N$$

其中$\lambda = p/0.05$，训练样本记录概率为$0.1 + 0.9\lambda$。

这些公式共同构成了KataGo的完整MCTS系统，相比AlphaZero在探索效率、训练稳定性和实用性方面都有显著改进。

## 综合效用函数与PUCT公式的联系

### 核心联系机制

综合效用函数 $U_{\text{total}} = u_{\text{win}}(x) + u_{\text{score}}(x)$ 与PUCT公式通过 **$V(c)$值的计算** 紧密联系：

### 1. $V(c)$值的重新定义

在标准AlphaZero中，$V(c)$仅表示胜负概率的平均值。在KataGo中，$V(c)$被扩展为综合效用的平均值：

$$V(c) = \frac{1}{N(c)} \sum_{i=1}^{N(c)} U_{\text{total}}^{(i)}$$

其中 $U_{\text{total}}^{(i)}$ 是第$i$次通过节点$c$时计算的综合效用值。

### 2. 具体计算流程

#### 步骤1：叶节点评估
当MCTS到达叶节点时，神经网络同时输出：
- 胜负预测：$\hat{z}$ → 计算 $u_{\text{win}}(x)$
- 得分预测：$(\hat{\mu}_s, \hat{\sigma}_s^2)$ → 计算 $E[u_{\text{score}}(x)]$

#### 步骤2：综合效用计算
$$U_{\text{total}} = u_{\text{win}}(x) + E[u_{\text{score}}(x)]$$

#### 步骤3：反向传播更新
这个 $U_{\text{total}}$ 值沿着搜索路径向上传播，更新路径上每个节点的 $V(c)$ 值。

### 3. PUCT选择中的作用

更新后的 $V(c)$ 值直接影响PUCT公式的选择：

$$\text{PUCT}(c) = \underbrace{V(c)}_{\text{包含综合效用}} + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$$

### 4. 动态重新中心化

在每次搜索开始时：
1. 设置 $x_0 = \hat{\mu}_s$（根节点的预测得分均值）
2. 这确保了得分效用 $u_{\text{score}}(x)$ 围绕当前位置的预期得分进行优化

### 5. 实际影响

这种联系使得MCTS不仅考虑胜负，还考虑得分差异：

- **胜势时**：倾向于选择能扩大得分优势的走法
- **劣势时**：倾向于选择能减少得分劣势的走法
- **均势时**：在胜负和得分之间找到平衡

### 6. 数学表达

完整的联系可以表达为：

$$\text{PUCT}(c) = \underbrace{\mathbb{E}[u_{\text{win}} + u_{\text{score}}]}_{\text{V(c)的新定义}} + c_{\text{PUCT}} P(c) \frac{\sqrt{\sum_{c'} N(c')}}{1 + N(c)}$$

这样，综合效用函数通过重新定义节点价值 $V(c)$，直接影响了PUCT公式的选择决策，使得搜索同时优化胜负和得分两个目标。