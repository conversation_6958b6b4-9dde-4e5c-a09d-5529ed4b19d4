
DESCRIPTION: 

   Run KataGo parallel JSON-based analysis engine.

USAGE: 

   analysis  -config <FILE> ...  [-model <FILE>] [-human-model <FILE>]
             [...other flags...]


Where: 

   -config <FILE>  (accepted multiple times)
     (required)  Config file(s) to use, can be one or multiple files (see
     analysis_example.cfg or configs/analysis_example.cfg).

   -model <FILE>
     Neural net model file. Defaults to: (dir containing katago.exe, or
     else ~/.katago)/default_model.bin.gz

   -human-model <FILE>
     Human SL neural net model file

   -override-config <KEYVALUEPAIRS>  (accepted multiple times)
     Override config parameters. Format: "key=value, key=value,..."

   -analysis-threads <THREADS>
     Analyze up to this many positions in parallel. Equivalent to
     numAnalysisThreads in the config.

   -quit-without-waiting
     When stdin is closed, quit quickly without waiting for queued tasks

   --,  -ignore_rest
     Ignores the rest of the labeled arguments following this flag.

   -version
     Displays version information and exits.

   -h,  -help
     Displays usage information and exits.



