
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 4;
const board = [0,0,1,0,2,0,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,1],],'p':0.5049,'wl':-0.9449,'ssM':0.02,'wlRad':0.0878,'sRad':0.44,'v':20,'av':20,},{'xy':[[3,0],],'p':0.1324,'wl':-0.8146,'ssM':0.60,'wlRad':0.1621,'sRad':1.20,'v':19,'av':19,},{'xy':[[1,2],],'p':0.3854,'wl':-0.4511,'ssM':0.53,'wlRad':0.0138,'sRad':0.38,'v':15,'av':15,},{'xy':[[0,2],],'p':0.2980,'wl':-0.4060,'ssM':0.34,'wlRad':0.2273,'sRad':1.24,'v':13,'av':13,},{'move':'pass','p':0.0375,'wl':0.4068,'ssM':0.78,'wlRad':0.0953,'sRad':0.47,'v':34,'av':34,},{'xy':[[3,1],],'p':0.1329,'wl':0.8223,'ssM':1.93,'wlRad':0.0212,'sRad':0.18,'v':82,'av':82,},{'xy':[[2,2],],'p':0.7997,'wl':0.8949,'ssM':1.30,'wlRad':0.0833,'sRad':1.16,'v':66,'av':66,},{'move':'other','p':0.0749,'wl':0.1816,'ssM':0.45,'wlRad':0.0871,'sRad':0.83,'v':19,'av':19,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
