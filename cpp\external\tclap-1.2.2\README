
(This version of tclap is MODIFIED slightly by <PERSON> ("lightvector") to
make some functions virtual to allow hooking in for a bit more customization
of help output.)

TCLAP - Templatized Command Line Argument Parser

This is a simple C++ library that facilitates parsing command line
arguments in a type independent manner.  It doesn't conform exactly
to either the GNU or POSIX standards, although it is close.  See
docs/manual.html for descriptions of how things work or look at the
simple examples in the examples dir.

To find out what the latest changes are read the NEWS file in this directory.


Any and all feedback is welcome to:  <PERSON> <<EMAIL>>
