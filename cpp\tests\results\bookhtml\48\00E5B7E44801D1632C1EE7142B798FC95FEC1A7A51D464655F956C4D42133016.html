
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 7;
const board = [0,0,0,0,0,0,0,0,0,0,2,0,0,1,0,0,];
const links = {14:'../1E/085669C61E2E1EAB4C5E86C130D6133C3DEFD1FBE8D842ACBC986A7AD59AD46C.html',};
const linkSyms = {14:3,};
const moves = [{'xy':[[0,0],],'p':0.9422,'wl':-0.3438,'ssM':-0.10,'wlRad':0.0110,'sRad':0.77,'v':82,'av':82,},{'xy':[[3,3],],'p':0.3033,'wl':-0.3385,'ssM':0.05,'wlRad':0.0514,'sRad':0.70,'v':184,'av':184,},{'xy':[[2,1],],'p':0.7857,'wl':0.0472,'ssM':0.38,'wlRad':0.0860,'sRad':0.68,'v':192,'av':192,},{'xy':[[2,3],],'p':0.5446,'wl':0.2626,'ssM':0.06,'wlRad':0.1925,'sRad':0.67,'v':220,'av':220,},{'xy':[[3,0],],'p':0.5172,'wl':0.3462,'ssM':0.56,'wlRad':0.0912,'sRad':0.57,'v':148,'av':148,},{'xy':[[3,1],],'p':0.0014,'wl':0.7755,'ssM':0.96,'wlRad':0.2516,'sRad':1.22,'v':74,'av':74,},{'move':'other','p':0.0981,'wl':0.5322,'ssM':-0.48,'wlRad':0.0317,'sRad':0.71,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
