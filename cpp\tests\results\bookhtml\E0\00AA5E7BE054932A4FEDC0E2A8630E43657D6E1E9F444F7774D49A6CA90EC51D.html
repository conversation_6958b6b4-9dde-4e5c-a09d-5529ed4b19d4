
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 3;
const board = [0,0,0,0,0,0,0,1,0,0,0,0,0,0,2,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,3],],'p':0.6582,'wl':0.0599,'ssM':0.21,'wlRad':0.0050,'sRad':0.09,'v':83,'av':83,},{'xy':[[2,2],],'p':0.3128,'wl':0.3715,'ssM':0.99,'wlRad':0.1109,'sRad':1.39,'v':27,'av':27,},{'xy':[[1,0],],'p':0.8199,'wl':0.6321,'ssM':0.14,'wlRad':0.1408,'sRad':0.68,'v':84,'av':84,},{'xy':[[1,2],],'p':0.7908,'wl':0.6860,'ssM':-0.06,'wlRad':0.0882,'sRad':0.70,'v':186,'av':186,},{'xy':[[2,0],],'p':0.4133,'wl':0.9925,'ssM':-0.03,'wlRad':0.0780,'sRad':0.95,'v':68,'av':68,},{'move':'other','p':0.1703,'wl':-0.4131,'ssM':0.64,'wlRad':0.1354,'sRad':1.53,'v':12,'av':12,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
