
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 2;
const pLink = '../B6/04BBDFC9B6B45290032FC42860DB2A04A03622A9FFC4E364BFE570886B3C26E0.html';
const pSym = 3;
const board = [0,0,1,0,0,1,0,0,0,0,0,0,0,2,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,2],],'p':0.5966,'wl':0.4038,'ssM':-0.65,'wlRad':0.0294,'sRad':1.00,'v':73,'av':73,},{'xy':[[3,2],],'p':0.5913,'wl':0.2599,'ssM':-0.93,'wlRad':0.1060,'sRad':0.79,'v':92,'av':92,},{'xy':[[1,2],],'p':0.5802,'wl':-0.5133,'ssM':-1.42,'wlRad':0.0564,'sRad':0.19,'v':57,'av':57,},{'move':'other','p':0.0251,'wl':0.9102,'ssM':-0.69,'wlRad':0.2452,'sRad':0.57,'v':12,'av':12,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
