
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 0;
const board = [0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,1],[1,2],],'p':0.3222,'wl':-0.3225,'ssM':1.28,'wlRad':0.0858,'sRad':0.72,'v':25,'av':25,},{'xy':[[3,3],],'p':0.9388,'wl':0.0286,'ssM':-0.07,'wlRad':0.0335,'sRad':0.44,'v':67,'av':67,},{'xy':[[3,0],[0,3],],'p':0.4578,'wl':0.1990,'ssM':0.59,'wlRad':0.0118,'sRad':0.34,'v':93,'av':93,},{'move':'pass','p':0.9775,'wl':0.5257,'ssM':-0.68,'wlRad':0.0991,'sRad':1.46,'v':17,'av':17,},{'xy':[[1,0],[0,1],],'p':0.2497,'wl':0.4982,'ssM':1.20,'wlRad':0.1940,'sRad':1.09,'v':86,'av':86,},{'move':'other','p':0.2185,'wl':-0.1760,'ssM':0.14,'wlRad':0.0270,'sRad':0.37,'v':14,'av':14,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
