
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 1;
const board = [0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,2,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,2],],'p':0.9073,'wl':-0.5166,'ssM':-0.62,'wlRad':0.1694,'sRad':0.15,'v':12,'av':12,},{'xy':[[1,0],],'p':0.2189,'wl':-0.4578,'ssM':0.45,'wlRad':0.1026,'sRad':0.46,'v':18,'av':18,},{'xy':[[1,2],],'p':0.8718,'wl':-0.3196,'ssM':1.15,'wlRad':0.0794,'sRad':0.66,'v':30,'av':30,},{'xy':[[2,1],],'p':0.5705,'wl':-0.1105,'ssM':0.04,'wlRad':0.0607,'sRad':0.57,'v':20,'av':20,},{'move':'pass','p':0.2082,'wl':0.5613,'ssM':1.06,'wlRad':0.2505,'sRad':0.76,'v':78,'av':78,},{'xy':[[3,1],],'p':0.3925,'wl':0.7041,'ssM':0.15,'wlRad':0.0248,'sRad':1.06,'v':26,'av':26,},{'xy':[[0,3],],'p':0.8273,'wl':0.9185,'ssM':0.80,'wlRad':0.0134,'sRad':0.78,'v':55,'av':55,},{'xy':[[2,3],],'p':0.0255,'wl':0.8662,'ssM':2.28,'wlRad':0.0701,'sRad':2.26,'v':75,'av':75,},{'move':'other','p':0.1097,'wl':-0.3329,'ssM':0.54,'wlRad':0.0498,'sRad':1.71,'v':12,'av':12,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
