: Running with following config:
cudaUseFP16 = false
cudaUseNHWC = false
forDeterministicTesting = true
logAllRequests = true
logAllResponses = true
logFile = tests/results/analysis/badoverride.txt.log
logSearchInfo = true
logTimeStamp = false
maxPlayouts = 10000
maxVisits = 100
nnCacheSizePowerOfTwo = 23
nnMaxBatchSize = 64
nnMutexPoolSizePowerOfTwo = 17
nnRandSeed = analysisTest
nnRandomize = false
numAnalysisThreads = 1
numSearchThreads = 1
openclUseFP16 = false
reportAnalysisWinratesAs = BLACK
rootSymmetryPruning = false
trtUseFP16 = false

: Analysis Engine starting...
: KataGo v1.16.2
: nnRandSeed0 = analysisTest
: After dedups: nnModelFile0 = tests/models/g170-b6c96-s175395328-d26788732.bin.gz useFP16 false useNHWC false
: Initializing neural net buffer to be size 19 * 19 allowing smaller boards
: Cuda backend thread 0: Found GPU NVIDIA RTX A5000 memory 25425608704 compute capability major 8 minor 6
: Cuda backend thread 0: Model version 8 useFP16 = false useNHWC = false
: Cuda backend thread 0: Model name: g170-b6c96-s175395328-d26788732
: Loaded config configs/analysis_example.cfg and/or command-line and query overrides
: Loaded model tests/models/g170-b6c96-s175395328-d26788732.bin.gz
: Config override: cudaUseFP16 = false
: Config override: cudaUseNHWC = false
: Config override: forDeterministicTesting = true
: Config override: logAllRequests = true
: Config override: logAllResponses = true
: Config override: logDir = 
: Config override: logFile = tests/results/analysis/badoverride.txt.log
: Config override: logSearchInfo = true
: Config override: logTimeStamp = false
: Config override: maxPlayouts = 10000
: Config override: maxVisits = 100
: Config override: nnRandSeed = analysisTest
: Config override: nnRandomize = false
: Config override: numAnalysisThreads = 1
: Config override: numSearchThreadsPerAnalysisThread = 1
: Config override: openclUseFP16 = false
: Config override: rootSymmetryPruning = false
: Config override: trtUseFP16 = false
: Analyzing up to 1 positions at a time in parallel
: Started, ready to begin handling requests
: Request: {"id":"badoverride","initialStones":[],"moves":[],"rules":"tromp-taylor","komi":7.5,"boardXSize":19,"boardYSize":19,"overrideSettings":{"sldjfslkjf":1234}}
: Warning: {"field":"overrideSettings","id":"badoverride","warning":"Unknown config params: sldjfslkjf"}
: Request: {"id":"badfield","initialStones":[],"moves":[],"rules":"tromp-taylor","komi":7.5,"boardXSize":19,"boardYSize":19,"overrideSettings":{},"myUnusedField":3,"boardxsize":13}
: Warning: {"field":"boardxsize","id":"badfield","warning":"Unexpected or unused field, do you have a typo? (set warnUnusedFields=false in the config to disable this warning)"}
: Warning: {"field":"myUnusedField","id":"badfield","warning":"Unexpected or unused field, do you have a typo? (set warnUnusedFields=false in the config to disable this warning)"}
: Response: {"field":"overrideSettings","id":"badoverride","warning":"Unknown config params: sldjfslkjf"}
: Response: {"field":"boardxsize","id":"badfield","warning":"Unexpected or unused field, do you have a typo? (set warnUnusedFields=false in the config to disable this warning)"}
: Response: {"field":"myUnusedField","id":"badfield","warning":"Unexpected or unused field, do you have a typo? (set warnUnusedFields=false in the config to disable this warning)"}
: MoveNum: 0 HASH: CDCBC1F514D7E680FACD226074256633
   A B C D E F G H J K L M N O P Q R S T
19 . . . . . . . . . . . . . . . . . . .
18 . . . . . . . . . . . . . . . . . . .
17 . . . . . . . . . . . . . . . . . . .
16 . . . . . . . . . . . . . . . . . . .
15 . . . . . . . . . . . . . . . . . . .
14 . . . . . . . . . . . . . . . . . . .
13 . . . . . . . . . . . . . . . . . . .
12 . . . . . . . . . . . . . . . . . . .
11 . . . . . . . . . . . . . . . . . . .
10 . . . . . . . . . . . . . . . . . . .
 9 . . . . . . . . . . . . . . . . . . .
 8 . . . . . . . . . . . . . . . . . . .
 7 . . . . . . . . . . . . . . . . . . .
 6 . . . . . . . . . . . . . . . . . . .
 5 . . . . . . . . . . . . . . . . . . .
 4 . . . . . . . . . . . . . . . . . . .
 3 . . . . . . . . . . . . . . . . . . .
 2 . . . . . . . . . . . . . . . . . . .
 1 . . . . . . . . . . . . . . . . . . .

koPOSITIONALscoreAREAtaxNONEsui1komi7.5
Root visits: 100
New playouts: 100
NN rows: 90
NN batches: 90
NN avg batch size: 1
PV: Q3 R16 D3 D17
Tree:
: T  -0.06c W  -0.53c S   0.26c ( +0.1 L  +0.1) N     100  --  Q3 R16 D3 D17
---Black(^)---
Q3  : T   0.46c W   0.29c S   0.38c ( +0.2 L  +0.2) LCB   -4.46c P  8.99% WF  12.0 PSV      17 N      12  --  Q3 R16 D3 D17
Q17 : T   0.71c W  -0.03c S   0.41c ( +0.3 L  +0.3) LCB   -5.49c P  8.74% WF  11.0 PSV      11 N      11  --  Q17 D3 C16 Q3 E16
D3  : T   0.27c W   0.14c S   0.39c ( +0.2 L  +0.3) LCB   -7.51c P  8.44% WF  10.0 PSV      10 N      10  --  D3 R16 C16 Q3 E16
R16 : T   0.80c W   0.09c S   0.34c ( +0.2 L  +0.1) LCB   -6.74c P  7.35% WF  10.0 PSV      10 N      10  --  R16 Q3 C4 D17
C4  : T  -0.17c W  -0.63c S   0.26c ( +0.1 L  +0.1) LCB   -7.56c P  8.60% WF  10.0 PSV       9 N      10  --  C4 R16 C16 Q3 E16
C16 : T   0.49c W  -0.10c S   0.34c ( +0.2 L  +0.2) LCB   -8.80c P  8.22% WF   9.0 PSV       9 N       9  --  C16 R16 D3 Q3 E16
R4  : T  -0.45c W  -0.51c S   0.19c ( -0.0 L  +0.1) LCB  -11.75c P  8.89% WF   8.0 PSV       8 N       8  --  R4 R16 D3 D17
D17 : T  -1.10c W  -1.55c S   0.15c ( -0.1 L  -0.0) LCB  -21.40c P  8.00% WF   6.0 PSV       6 N       6  --  D17 D3 Q3 Q17 Q5
Q4  : T  -0.92c W  -1.53c S   0.13c ( -0.1 L  +0.0) LCB  -20.50c P  5.83% WF   6.0 PSV       5 N       6  --  Q4 R16 D3 D17
D4  : T   0.24c W  -0.61c S   0.15c ( -0.1 L  -0.0) LCB  -27.79c P  4.67% WF   5.0 PSV       5 N       5  --  D4 R16 C16

: Response: {"id":"badoverride","isDuringSearch":false,"moveInfos":[{"edgeVisits":12,"edgeWeight":12.0,"lcb":0.483877081,"move":"Q3","order":0,"playSelectionValue":16.9814872,"prior":0.0898519158,"pv":["Q3","R16","D3","D17"],"scoreLead":0.248628101,"scoreMean":0.248628101,"scoreSelfplay":0.231792776,"scoreStdev":30.5220515,"utility":0.00461999752,"utilityLcb":-0.0446212085,"visits":12,"weight":12.0,"winrate":0.501463226},{"edgeVisits":11,"edgeWeight":11.0,"lcb":0.477692168,"move":"Q17","order":1,"playSelectionValue":11.0,"prior":0.087358892,"pv":["Q17","D3","C16","Q3","E16"],"scoreLead":0.268432726,"scoreMean":0.268432726,"scoreSelfplay":0.269621466,"scoreStdev":30.5407491,"utility":0.00713240604,"utilityLcb":-0.0548637759,"visits":11,"weight":11.0,"winrate":0.499833661},{"edgeVisits":10,"edgeWeight":10.0,"lcb":0.472937329,"move":"D3","order":2,"playSelectionValue":10.0,"prior":0.0843569487,"pv":["D3","R16","C16","Q3","E16"],"scoreLead":0.288789571,"scoreMean":0.288789571,"scoreSelfplay":0.240520366,"scoreStdev":30.5160123,"utility":0.00268994917,"utilityLcb":-0.075080249,"visits":10,"weight":10.0,"winrate":0.500712399},{"edgeVisits":10,"edgeWeight":10.0,"lcb":0.473536848,"move":"R16","order":3,"playSelectionValue":10.0,"prior":0.0734942779,"pv":["R16","Q3","C4","D17"],"scoreLead":0.145596848,"scoreMean":0.145596848,"scoreSelfplay":0.162836279,"scoreStdev":30.574287,"utility":0.00804808155,"utilityLcb":-0.0673575168,"visits":10,"weight":10.0,"winrate":0.500467418},{"edgeVisits":10,"edgeWeight":10.0,"lcb":0.470459758,"move":"C4","order":4,"playSelectionValue":9.0,"prior":0.0860261694,"pv":["C4","R16","C16","Q3","E16"],"scoreLead":0.100047535,"scoreMean":0.100047535,"scoreSelfplay":0.0566140471,"scoreStdev":30.4856088,"utility":-0.00170933219,"utilityLcb":-0.0756138589,"visits":10,"weight":10.0,"winrate":0.496854232},{"edgeVisits":9,"edgeWeight":9.0,"lcb":0.466309236,"move":"C16","order":5,"playSelectionValue":9.0,"prior":0.08215639,"pv":["C16","R16","D3","Q3","E16"],"scoreLead":0.161077769,"scoreMean":0.161077769,"scoreSelfplay":0.170122183,"scoreStdev":30.5315474,"utility":0.00489880463,"utilityLcb":-0.0879887334,"visits":9,"weight":9.0,"winrate":0.499483357},{"edgeVisits":8,"edgeWeight":8.0,"lcb":0.457086059,"move":"R4","order":6,"playSelectionValue":8.0,"prior":0.0889108479,"pv":["R4","R16","D3","D17"],"scoreLead":0.0679317547,"scoreMean":0.0679317547,"scoreSelfplay":-0.0443149692,"scoreStdev":30.5790047,"utility":-0.00448483269,"utilityLcb":-0.117495542,"visits":8,"weight":8.0,"winrate":0.497447027},{"edgeVisits":6,"edgeWeight":6.0,"lcb":0.419750504,"move":"D17","order":7,"playSelectionValue":6.0,"prior":0.0799723491,"pv":["D17","D3","Q3","Q17","Q5"],"scoreLead":-0.0402675782,"scoreMean":-0.0402675782,"scoreSelfplay":-0.103841721,"scoreStdev":30.6026468,"utility":-0.0110113496,"utilityLcb":-0.213994458,"visits":6,"weight":6.0,"winrate":0.492244471},{"edgeVisits":6,"edgeWeight":6.0,"lcb":0.422440309,"move":"Q4","order":8,"playSelectionValue":5.0,"prior":0.0582824014,"pv":["Q4","R16","D3","D17"],"scoreLead":0.0256258084,"scoreMean":0.0256258084,"scoreSelfplay":-0.142777991,"scoreStdev":30.5509251,"utility":-0.00923155421,"utilityLcb":-0.204983625,"visits":6,"weight":6.0,"winrate":0.492351763},{"edgeVisits":5,"edgeWeight":5.0,"lcb":0.396829168,"move":"D4","order":9,"playSelectionValue":5.0,"prior":0.0466991886,"pv":["D4","R16","C16"],"scoreLead":-0.0467663745,"scoreMean":-0.0467663745,"scoreSelfplay":-0.106127075,"scoreStdev":30.7064169,"utility":0.00244520235,"utilityLcb":-0.277890879,"visits":5,"weight":5.0,"winrate":0.496949197},{"edgeVisits":5,"edgeWeight":5.0,"lcb":0.387020306,"move":"D16","order":10,"playSelectionValue":3.0,"prior":0.0466381721,"pv":["D16","D3","Q17","Q3"],"scoreLead":-0.191494825,"scoreMean":-0.191494825,"scoreSelfplay":-0.319814981,"scoreStdev":30.6021442,"utility":-0.0118850765,"utilityLcb":-0.296915923,"visits":5,"weight":5.0,"winrate":0.488817037},{"edgeVisits":4,"edgeWeight":4.0,"lcb":0.338120228,"move":"Q16","order":11,"playSelectionValue":3.0,"prior":0.0390564427,"pv":["Q16","D3","D17"],"scoreLead":-0.119422938,"scoreMean":-0.119422938,"scoreSelfplay":-0.204894532,"scoreStdev":30.6878977,"utility":-0.00613889616,"utilityLcb":-0.437831326,"visits":4,"weight":4.0,"winrate":0.492296096},{"edgeVisits":3,"edgeWeight":3.0,"lcb":0.229660191,"move":"C3","order":12,"playSelectionValue":2.0,"prior":0.0324283391,"pv":["C3","R16","C16"],"scoreLead":-0.663534159,"scoreMean":-0.663534159,"scoreSelfplay":-0.482369016,"scoreStdev":30.7817247,"utility":-0.0138861483,"utilityLcb":-0.755015698,"visits":3,"weight":3.0,"winrate":0.494349316}],"rootInfo":{"currentPlayer":"B","rawLead":-0.0361211784,"rawNoResultProb":0.0,"rawScoreSelfplay":-0.502035618,"rawScoreSelfplayStdev":31.2238158,"rawStScoreError":-1.0,"rawStWrError":-0.5,"rawVarTimeLeft":-1.0,"rawWinrate":0.472509012,"scoreLead":0.112026512,"scoreSelfplay":0.0697717485,"scoreStdev":30.5694986,"symHash":"47E1EBDAE69A5DED3FF8DB1FA4E91845","thisHash":"47E1EBDAE69A5DED3FF8DB1FA4E91845","utility":-4.12549165e-05,"visits":100,"weight":100.0,"winrate":0.497645323},"turnNumber":0}
: MoveNum: 0 HASH: CDCBC1F514D7E680FACD226074256633
   A B C D E F G H J K L M N O P Q R S T
19 . . . . . . . . . . . . . . . . . . .
18 . . . . . . . . . . . . . . . . . . .
17 . . . . . . . . . . . . . . . . . . .
16 . . . . . . . . . . . . . . . . . . .
15 . . . . . . . . . . . . . . . . . . .
14 . . . . . . . . . . . . . . . . . . .
13 . . . . . . . . . . . . . . . . . . .
12 . . . . . . . . . . . . . . . . . . .
11 . . . . . . . . . . . . . . . . . . .
10 . . . . . . . . . . . . . . . . . . .
 9 . . . . . . . . . . . . . . . . . . .
 8 . . . . . . . . . . . . . . . . . . .
 7 . . . . . . . . . . . . . . . . . . .
 6 . . . . . . . . . . . . . . . . . . .
 5 . . . . . . . . . . . . . . . . . . .
 4 . . . . . . . . . . . . . . . . . . .
 3 . . . . . . . . . . . . . . . . . . .
 2 . . . . . . . . . . . . . . . . . . .
 1 . . . . . . . . . . . . . . . . . . .

koPOSITIONALscoreAREAtaxNONEsui1komi7.5
Root visits: 100
New playouts: 100
NN rows: 99
NN batches: 99
NN avg batch size: 1
PV: Q17 Q3 C16 D3 E16
Tree:
: T  -0.15c W  -0.63c S   0.23c ( +0.0 L  +0.0) N     100  --  Q17 Q3 C16 D3 E16
---Black(^)---
Q17 : T   0.54c W   0.04c S   0.34c ( +0.2 L  +0.1) LCB   -5.24c P  8.74% WF  11.0 PSV      15 N      11  --  Q17 Q3 C16 D3 E16
Q3  : T   0.53c W   0.12c S   0.39c ( +0.2 L  +0.3) LCB   -7.01c P  8.99% WF  10.0 PSV      10 N      10  --  Q3 R16 D3 D17
D3  : T   0.28c W   0.14c S   0.39c ( +0.2 L  +0.3) LCB   -7.48c P  8.44% WF  10.0 PSV      10 N      10  --  D3 R16 C16 Q3 E16
C4  : T  -0.12c W  -0.49c S   0.25c ( +0.0 L  +0.1) LCB   -8.18c P  8.60% WF  10.0 PSV       9 N      10  --  C4 R16 C16 Q3 E16
C16 : T  -0.07c W  -0.45c S   0.31c ( +0.1 L  +0.1) LCB   -6.27c P  8.22% WF  11.0 PSV       8 N      11  --  C16 R16 D3 Q3 E16
R4  : T  -0.57c W  -0.65c S   0.18c ( -0.1 L  -0.0) LCB   -9.60c P  8.89% WF   9.0 PSV       8 N       9  --  R4 D3 C16 Q17 E16
D17 : T  -0.07c W  -0.90c S   0.18c ( -0.1 L  -0.1) LCB  -14.98c P  8.00% WF   7.0 PSV       7 N       7  --  D17 D3 Q3 Q17 Q5
R16 : T   0.49c W  -0.19c S   0.29c ( +0.1 L  +0.0) LCB  -14.09c P  7.35% WF   7.0 PSV       7 N       7  --  R16 D3 C16 Q3 E16
Q4  : T  -0.70c W  -1.40c S   0.09c ( -0.2 L  -0.1) LCB  -28.60c P  5.83% WF   5.0 PSV       5 N       5  --  Q4 R16 D3
Q16 : T   0.32c W  -0.97c S   0.12c ( -0.2 L  -0.1) LCB  -27.59c P  3.91% WF   5.0 PSV       4 N       5  --  Q16 R4 D3

: Response: {"id":"badfield","isDuringSearch":false,"moveInfos":[{"edgeVisits":11,"edgeWeight":11.0,"lcb":0.479593238,"move":"Q17","order":0,"playSelectionValue":14.9695933,"prior":0.087358892,"pv":["Q17","Q3","C16","D3","E16"],"scoreLead":0.0823578003,"scoreMean":0.0823578003,"scoreSelfplay":0.165987202,"scoreStdev":30.6140542,"utility":0.00535850014,"utilityLcb":-0.0523847835,"visits":11,"weight":11.0,"winrate":0.50021584},{"edgeVisits":10,"edgeWeight":10.0,"lcb":0.473688842,"move":"Q3","order":1,"playSelectionValue":10.0,"prior":0.0898519158,"pv":["Q3","R16","D3","D17"],"scoreLead":0.301488727,"scoreMean":0.301488727,"scoreSelfplay":0.242209366,"scoreStdev":30.4931327,"utility":0.00526109213,"utilityLcb":-0.0700543624,"visits":10,"weight":10.0,"winrate":0.500587219},{"edgeVisits":10,"edgeWeight":10.0,"lcb":0.472990208,"move":"D3","order":2,"playSelectionValue":10.0,"prior":0.0843569487,"pv":["D3","R16","C16","Q3","E16"],"scoreLead":0.288792621,"scoreMean":0.288792621,"scoreSelfplay":0.240520055,"scoreStdev":30.5160142,"utility":0.00281490025,"utilityLcb":-0.0748073803,"visits":10,"weight":10.0,"winrate":0.500712451},{"edgeVisits":10,"edgeWeight":10.0,"lcb":0.468753163,"move":"C4","order":3,"playSelectionValue":9.0,"prior":0.0860261694,"pv":["C4","R16","C16","Q3","E16"],"scoreLead":0.0576027945,"scoreMean":0.0576027945,"scoreSelfplay":0.0416162095,"scoreStdev":30.5272471,"utility":-0.00123721304,"utilityLcb":-0.0817986023,"visits":10,"weight":10.0,"winrate":0.497525088},{"edgeVisits":11,"edgeWeight":11.0,"lcb":0.475613112,"move":"C16","order":4,"playSelectionValue":8.0,"prior":0.08215639,"pv":["C16","R16","D3","Q3","E16"],"scoreLead":0.12968317,"scoreMean":0.12968317,"scoreSelfplay":0.127629349,"scoreStdev":30.5111738,"utility":-0.000683861226,"utilityLcb":-0.0626603224,"visits":11,"weight":11.0,"winrate":0.497747563},{"edgeVisits":9,"edgeWeight":9.0,"lcb":0.464513387,"move":"R4","order":5,"playSelectionValue":8.0,"prior":0.0889108479,"pv":["R4","D3","C16","Q17","E16"],"scoreLead":-0.0210538585,"scoreMean":-0.0210538585,"scoreSelfplay":-0.0670248397,"scoreStdev":30.5969648,"utility":-0.00570723808,"utilityLcb":-0.096007239,"visits":9,"weight":9.0,"winrate":0.496763387},{"edgeVisits":7,"edgeWeight":7.0,"lcb":0.442268117,"move":"D17","order":6,"playSelectionValue":7.0,"prior":0.0799723491,"pv":["D17","D3","Q3","Q17","Q5"],"scoreLead":-0.0507826747,"scoreMean":-0.0507826747,"scoreSelfplay":-0.0612131616,"scoreStdev":30.6558836,"utility":-0.000718232427,"utilityLcb":-0.149826134,"visits":7,"weight":7.0,"winrate":0.495520939},{"edgeVisits":7,"edgeWeight":7.0,"lcb":0.446989004,"move":"R16","order":7,"playSelectionValue":7.0,"prior":0.0734942779,"pv":["R16","D3","C16","Q3","E16"],"scoreLead":0.00452339008,"scoreMean":0.00452339008,"scoreSelfplay":0.101269214,"scoreStdev":30.6136303,"utility":0.00486243311,"utilityLcb":-0.14092741,"visits":7,"weight":7.0,"winrate":0.499056805},{"edgeVisits":5,"edgeWeight":5.0,"lcb":0.393341469,"move":"Q4","order":8,"playSelectionValue":5.0,"prior":0.0582824014,"pv":["Q4","R16","D3"],"scoreLead":-0.0880811603,"scoreMean":-0.0880811603,"scoreSelfplay":-0.20389015,"scoreStdev":30.6626776,"utility":-0.00696163316,"utilityLcb":-0.285953169,"visits":5,"weight":5.0,"winrate":0.492981303},{"edgeVisits":5,"edgeWeight":5.0,"lcb":0.395486019,"move":"Q16","order":9,"playSelectionValue":4.0,"prior":0.0390564427,"pv":["Q16","R4","D3"],"scoreLead":-0.122715383,"scoreMean":-0.122715383,"scoreSelfplay":-0.1527898,"scoreStdev":30.6991986,"utility":0.00316580389,"utilityLcb":-0.275939923,"visits":5,"weight":5.0,"winrate":0.495166635},{"edgeVisits":4,"edgeWeight":4.0,"lcb":0.337991497,"move":"D4","order":10,"playSelectionValue":4.0,"prior":0.0466991886,"pv":["D4","R4","C16"],"scoreLead":-0.00804725386,"scoreMean":-0.00804725386,"scoreSelfplay":-0.166622937,"scoreStdev":30.6392772,"utility":-0.00452435831,"utilityLcb":-0.439549837,"visits":4,"weight":4.0,"winrate":0.493357739},{"edgeVisits":5,"edgeWeight":5.0,"lcb":0.387145551,"move":"D16","order":11,"playSelectionValue":3.0,"prior":0.0466381721,"pv":["D16","D3","Q17","Q3"],"scoreLead":-0.191473151,"scoreMean":-0.191473151,"scoreSelfplay":-0.319737122,"scoreStdev":30.6021703,"utility":-0.0142241095,"utilityLcb":-0.298910589,"visits":5,"weight":5.0,"winrate":0.488819293},{"edgeVisits":3,"edgeWeight":3.0,"lcb":0.225512751,"move":"C17","order":12,"playSelectionValue":2.0,"prior":0.0303785279,"pv":["C17","R16","D3"],"scoreLead":-0.778894524,"scoreMean":-0.778894524,"scoreSelfplay":-0.63037465,"scoreStdev":30.8120521,"utility":-0.0141431948,"utilityLcb":-0.757043409,"visits":3,"weight":3.0,"winrate":0.490834256},{"edgeVisits":2,"edgeWeight":2.0,"lcb":-0.0429246442,"move":"C3","order":13,"playSelectionValue":2.0,"prior":0.0324283391,"pv":["C3","R16"],"scoreLead":-0.50357797,"scoreMean":-0.50357797,"scoreSelfplay":-0.481991544,"scoreStdev":30.7171438,"utility":-0.0195607489,"utilityLcb":-1.5138214,"visits":2,"weight":2.0,"winrate":0.490739875}],"rootInfo":{"currentPlayer":"B","rawLead":-0.0361211784,"rawNoResultProb":0.0,"rawScoreSelfplay":-0.502035618,"rawScoreSelfplayStdev":31.2238158,"rawStScoreError":-1.0,"rawStWrError":-0.5,"rawVarTimeLeft":-1.0,"rawWinrate":0.472509012,"scoreLead":0.0410269446,"scoreSelfplay":0.0198884252,"scoreStdev":30.5953624,"symHash":"47E1EBDAE69A5DED3FF8DB1FA4E91845","thisHash":"47E1EBDAE69A5DED3FF8DB1FA4E91845","utility":-0.00109901875,"visits":100,"weight":100.0,"winrate":0.497058766},"turnNumber":0}
: tests/models/g170-b6c96-s175395328-d26788732.bin.gz
: NN rows: 99
: NN batches: 99
: NN avg batch size: 1
: GPU -1 finishing, processed 99 rows 99 batches
: All cleaned up, quitting
