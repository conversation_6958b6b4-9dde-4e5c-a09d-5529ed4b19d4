
Primary author and project maintainer (https://github.com/lightvector/KataGo):
<PERSON> ("lightvector")

Many thanks to these additional authors:
<PERSON><PERSON> ("tychota") - for documentation and build improvements
<PERSON><PERSON> ("bernds") - for minor build improvements
<PERSON><PERSON> ("lpuchallafiore") - for a wide variety of code refactors and fixes
"TFiFiE" - for minor build improvements
"iopq" - for a fix to python script imports
<PERSON> - coauthor of part of the original game board code
<PERSON> ("featurecat") - slight readme edits
<PERSON> ("dwt") - minor bugfix, MacOS support, commandline improvements
"yenw" - simple compile fix on MacOS.
<PERSON> ("apetresc") - MacOS support
<PERSON><PERSON> ("farmersrice") - frontend features and logic
Sander Land ("sanderland") - many options for analysis engine, various bugfixes and other work
<PERSON> ("shammyx") - minor customization for resign logic
"yffbit" - minor optimization for <PERSON><PERSON>'s algorithm
<PERSON> ("OmnipotentEntity") - workaround for CUDA on some cmake versions and other work
<PERSON><PERSON><PERSON> ("kaorahi") - for help optimizing Eigen backend and other work
<PERSON><PERSON><PERSON> ("anoek") - for some minor performance and api suggestions, minor cuda work
<PERSON> - for some work on CUDNN8 support
"inbae" - for removing boost filesystem dependencies, major contributions to pytorch training
"rimathia" - for a minor bugfix
"Jonathan_Go" - for help with client proxy support
Jonathan Roy ("roy7") - for various search refactors
Kensuke Matsuzaki ("zakki") - catching a buffer overrun bug
Kuang-che Wu ("kcwu") - minor improvement to distributed logging
"yzyray" - minor bugfix, improvements to gtp extensions
"fuhaoda" - code optimizations, testing, and other improvements
"y-ich" - for GPU optimization suggestions and other minor features
Yule Hou ("hyln9") - For implementing TensorRT backend and various testing and work.
"rooklift" - various documentation improvements
Viktor Pogrebniak ("avtomaton") - for improvements on the config system
Adam Gleave - for improvements on the config system
Emanuel-de-Jong - For minor improvement to GTP command.
"simon300000" - For minor update to .gitignore.
Dave Jarvis - For improved inline documentation for GTP example config.
Yuji Ichikiwa - For a minor fix to include paths.
"hzyhhzy" - For suggestions/improvements for training speed, and for a minor fix for rules parsing.
Thaddee Tyl ("espadrine") - Reporting and fixing of issue with half library intrinsics.
Sebastian H ("nerai") - Minor code cleanups and fixes.
Jochen Voss ("seehuhn") - Typo fix in doc
"kinfkong" - Added trt build configuration option
"TTXS123OK" - Minor code style improvement.
Chin-Chang Yang - For maintaining and implementing the Metal backend for MacOS and a variety of other GPU-related testing.
"tterava" - For improvements to build instructions.
"Stanley" - Minor typo fixes.
"Traveler" - Minor readme edits.

Separately from the authors of the content in this repo, additional special thanks to:
Junyan Xu ("alreadydone") - for much testing and troubleshooting for Windows support
"petgo3" - for bugfinding and advice on supporting KGS api
"sbbdms" - for much testing of cpuct and uncertainty parameters
"Medwin", "shengke" and others - for many tests and ideas and parameters
"lionfenfen", and Yi XiaoTian - For major ideas and techniques for improving neural net training in KataGo.
Reid McIlroy-Young ("reidmcy") - for discussion of AlphaZero and policy conditioning and optimism
"jaysephjw", and "gcmutator" - for finding some typos.