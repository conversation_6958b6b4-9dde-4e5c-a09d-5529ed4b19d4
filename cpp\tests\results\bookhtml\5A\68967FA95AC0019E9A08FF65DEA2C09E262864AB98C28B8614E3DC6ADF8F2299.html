
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 2;
const board = [1,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,1],[1,3],],'p':0.4714,'wl':-0.3438,'ssM':-0.10,'wlRad':0.0110,'sRad':0.77,'v':82,'av':82,},{'xy':[[1,1],],'p':0.4592,'wl':0.3005,'ssM':-0.82,'wlRad':0.0745,'sRad':0.05,'v':12,'av':12,},{'xy':[[3,2],[2,3],],'p':0.8703,'wl':0.9666,'ssM':0.07,'wlRad':0.1252,'sRad':1.03,'v':66,'av':66,},{'xy':[[3,0],[0,3],],'p':0.3545,'wl':0.9990,'ssM':-0.40,'wlRad':0.0369,'sRad':1.18,'v':56,'av':56,},{'move':'other','p':0.0503,'wl':-0.4825,'ssM':-0.08,'wlRad':0.1924,'sRad':0.90,'v':16,'av':16,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
