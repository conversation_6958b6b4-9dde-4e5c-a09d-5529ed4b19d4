
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 0;
const board = [0,0,0,1,0,0,0,0,0,0,0,0,0,2,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,2],],'p':0.9110,'wl':-0.3875,'ssM':0.92,'wlRad':0.2086,'sRad':1.35,'v':80,'av':80,},{'xy':[[2,1],],'p':0.4944,'wl':0.2067,'ssM':0.18,'wlRad':0.0158,'sRad':0.69,'v':18,'av':18,},{'xy':[[3,1],],'p':0.4103,'wl':0.1702,'ssM':0.55,'wlRad':0.0213,'sRad':0.96,'v':69,'av':69,},{'xy':[[3,2],],'p':0.4807,'wl':0.2299,'ssM':-0.03,'wlRad':0.0864,'sRad':1.15,'v':99,'av':99,},{'xy':[[1,1],],'p':0.0832,'wl':0.2286,'ssM':0.69,'wlRad':0.0871,'sRad':0.72,'v':170,'av':170,},{'xy':[[0,2],],'p':0.3729,'wl':0.8362,'ssM':-0.26,'wlRad':0.0358,'sRad':0.90,'v':118,'av':118,},{'xy':[[0,1],],'p':0.1633,'wl':0.8275,'ssM':0.93,'wlRad':0.0058,'sRad':1.03,'v':60,'av':60,},{'xy':[[0,3],],'p':0.6215,'wl':0.8724,'ssM':1.30,'wlRad':0.1223,'sRad':0.84,'v':107,'av':107,},{'move':'other','p':0.0198,'wl':0.3212,'ssM':1.24,'wlRad':0.1915,'sRad':0.78,'v':19,'av':19,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
