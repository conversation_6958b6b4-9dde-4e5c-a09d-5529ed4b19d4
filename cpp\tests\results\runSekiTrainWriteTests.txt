Running test for how a seki gets recorded
: Initializing neural net buffer to be size 19 * 19 allowing smaller boards
: <PERSON>uda backend thread 0: Found GPU NVIDIA RTX A5000 memory 25425608704 compute capability major 8 minor 6
: Cuda backend thread 0: Model version 5 useFP16 = false useNHWC = false
: Cuda backend thread 0: Model name: b6c96-s103408384-d26419149
seedBase: abc
HASH: 2AE066C40DF5CAE139EFE3E7240C5D23
   A B C D E F G H J K L M N
13 . . . . . . . . . O O O O
12 . . . . . . . . . O O X X
11 . . O O . . O . O O O X X
10 . . . . . . . O O X X X X
 9 . . . . . O O O O X X . .
 8 . . O . . O O X X X . . .
 7 . . . O O O X X X . . . .
 6 . . . O X X X X . X . . .
 5 O O O O O X X . . . . . .
 4 X X O O X X . . . . . . .
 3 . X X O O X . . . X X . .
 2 X O X X O X . . . . . . .
 1 . O . X O X . . . . . . .


Initial pla Black
Encore phase 0
Turns this phase 76
Approx valid turns this phase 76
Approx consec valid turns this game 76
Rules koSIMPLEscoreAREAtaxNONEsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves A4 B5 B3 C4 C2 D3 D1 E2 D2 E3 C3 D4 B4 C5 A2 B1 F1 E1 F2 A5 F3 D6 F4 B2 G5 E7 H6 F8 J7 G9 K8 H10 F5 D5 G6 D7 H7 F9 L9 J11 M10 K12 F6 H9 K9 K11 L10 K13 N10 M13 N12 L13 N11 G11 K6 C11 L3 C8 K3 D11 E4 E5 E6 F7 G7 G8 H8 J9 J8 J10 K10 L11 M12 L12 M11 N13 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800070030CB80300781300700200F80180060090048000000000C00600F00600E00E01E806066019196801400000000000000000000000000000000000000000000000000000000000000000000000000006001801E005000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000010000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FFF7FF3FF9FF0FF87F03F01E00F80180060090048000000000C00600F00780FC0FE1FF07FE7FF9FF6FF17F800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 7 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0 1 0 -1 0.00241864 0.997581 0 -1.17307 0.00710615 0.992894 0 -1.50849 0.0200753 0.979925 0 -2.43651 0.0743588 0.925641 0 -6.32083 -1 0 0.0221169 0 0 1 1 1 0 0 0.607056 2.94681 0.552665 1 1 0 1 1 1 1 1 2.9786e+06 3.45079e+06 562425 3.7395e+06 3.07181e+06 485823 0 1 0 0 75 1 75 0 0 0 -0.947012 -5.43077 2.94681 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 1 0 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 0 0 0 0 0 0 0 0 0 1 1 -1 -1 0 0 1 1 0 0 1 0 1 1 1 -1 -1 0 0 0 0 0 0 0 1 1 -1 -1 -1 -1 0 0 0 0 0 1 1 1 1 -1 -1 0 0 0 0 1 0 0 1 1 -1 -1 -1 0 0 0 0 0 0 1 1 1 -1 -1 -1 0 0 0 0 0 0 0 1 -1 -1 -1 -1 0 -1 0 0 0 1 1 1 1 1 -1 -1 0 0 0 0 0 0 -1 -1 1 1 -1 -1 0 0 0 0 0 0 0 0 -1 -1 1 1 -1 0 0 0 -1 -1 0 0 -1 1 -1 -1 1 -1 0 0 0 0 0 0 0 0 1 0 -1 1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 0 0 0 0 0 0 0 0 0 1 1 -1 -1 0 0 1 1 0 0 1 0 1 1 1 -1 -1 0 0 0 0 0 0 0 1 1 -1 -1 -1 -1 0 0 0 0 0 1 1 1 1 -1 -1 0 0 0 0 1 0 0 1 1 -1 -1 -1 0 0 0 0 0 0 1 1 1 -1 -1 -1 0 0 0 0 0 0 0 1 -1 -1 -1 -1 0 -1 0 0 0 1 1 1 1 1 -1 -1 0 0 0 0 0 0 -1 -1 1 1 -1 -1 0 0 0 0 0 0 0 0 -1 -1 1 1 -1 0 0 0 -1 -1 0 0 -1 1 -1 -1 1 -1 0 0 0 0 0 0 0 0 1 0 -1 1 -1 0 0 0 0 0 0 0 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 -120 -120 -120 -120 120 120 120 120 120 120 120 120 120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 -120 -120 120 -120 -120 -120 -120 -120 -120 -120 -120 0 120 0 -120 120 -120 -120 -120 -120 -120 -120 -120 -120 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 -26777 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -28312 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -368 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -426 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 7 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: abc
HASH: 9942C1270064011D1AE4EFE13F04B6F9
   A B C D E F G H J K L M N
13 O O . . X . O . O O O O O
12 . O O O O O O . . O O X X
11 O O O O O O O O O O O X X
10 . O O . O . O O O X X X X
 9 O O . O O O O O O X X . .
 8 O O O . . O O X X X X X .
 7 O . . O O O X X X . X X X
 6 . O . O X X X X X X X . X
 5 O O O O O X X X X . X . X
 4 X X O O X X . X X X . . .
 3 . X X O O X . . X X X X X
 2 X X X X O X X X X X . X .
 1 . O X X O X . X . . . . .


Initial pla Black
Encore phase 1
Turns this phase 1
Approx valid turns this phase 1
Approx consec valid turns this game 1
Rules koSIMPLEscoreTERRITORYtaxNONEsui0komi12
Ko recap block hash 00000000000000000000000000000000
White bonus score -2
White handicap bonus score 0
Has button 0
Presumed next pla White
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves pass 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80080000C00600F00600F80EE1FD07AE6E193FEFA35000C2FBF33FF8D70DF87302700A00F8018006001004800000000000000000000000000000000000000000040000080000000000000000000000000006001801E00300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000004000008000000000000000000000000000000000000040000080000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 -0.5 0 0 0 1 0 0 1 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 28 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0 1 0 -3 0.00181983 0.99818 0 -2.99389 0.00534679 0.994653 0 -2.98205 0.015105 0.984895 0 -2.94928 0.0559488 0.944051 0 -2.81215 -3 0 0.0125211 0 0 1 1 1 0 0 0.149456 0.835817 0 1 1 0 1 1 1 1 1 2.9786e+06 3.45079e+06 562425 3.7395e+06 3.07181e+06 485823 -10 0 0 0 0 1 0 0 1 0 0.371037 1.03536 0.835817 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 1 0 -1 0 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 0 -1 -1 0 -1 0 -1 -1 -1 1 1 1 1 -1 -1 0 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 0 0 -1 -1 1 1 1 1 1 0 -1 0 0 -1 -1 -1 1 1 1 0 1 1 1 0 -1 0 -1 1 1 1 1 1 1 1 0 1 -1 -1 -1 -1 -1 1 1 1 1 0 1 0 1 1 1 -1 -1 1 1 0 1 1 1 0 0 0 0 1 1 -1 -1 1 0 0 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 0 1 0 0 -1 1 1 -1 1 0 1 0 0 0 0 0 -1 -1 0 0 1 0 -1 0 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 0 -1 -1 0 -1 0 -1 -1 -1 1 1 1 1 -1 -1 0 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 0 0 -1 -1 1 1 1 1 1 0 -1 0 0 -1 -1 -1 1 1 1 0 1 1 1 0 -1 0 -1 1 1 1 1 1 1 1 0 1 -1 -1 -1 -1 -1 1 1 1 1 0 1 0 1 1 1 -1 -1 1 1 0 1 1 1 0 0 0 0 1 1 -1 -1 1 0 0 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 0 1 0 0 -1 1 1 -1 1 0 1 0 0 0 0 0 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 120 120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 120 120 120 -120 120 120 120 120 120 120 120 120 120 120 120 120 -120 120 120 120 120 120 120 120 120 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -31410 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -29753 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -194 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -176 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 28 


seedBase: abc
HASH: 2AE066C40DF5CAE139EFE3E7240C5D23
   A B C D E F G H J K L M N
13 . . . . . . . . . O O O O
12 . . . . . . . . . O O X X
11 . . O O . . O . O O O X X
10 . . . . . . . O O X X X X
 9 . . . . . O O O O X X . .
 8 . . O . . O O X X X . . .
 7 . . . O O O X X X . . . .
 6 . . . O X X X X . X . . .
 5 O O O O O X X . . . . . .
 4 X X O O X X . . . . . . .
 3 . X X O O X . . . X X . .
 2 X O X X O X . . . . . . .
 1 . O . X O X . . . . . . .


Initial pla Black
Encore phase 0
Turns this phase 76
Approx valid turns this phase 76
Approx consec valid turns this game 76
Rules koSIMPLEscoreAREAtaxSEKIsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves A4 B5 B3 C4 C2 D3 D1 E2 D2 E3 C3 D4 B4 C5 A2 B1 F1 E1 F2 A5 F3 D6 F4 B2 G5 E7 H6 F8 J7 G9 K8 H10 F5 D5 G6 D7 H7 F9 L9 J11 M10 K12 F6 H9 K9 K11 L10 K13 N10 M13 N12 L13 N11 G11 K6 C11 L3 C8 K3 D11 E4 E5 E6 F7 G7 G8 H8 J9 J8 J10 K10 L11 M12 L12 M11 N13 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800070030CB80300781300700200F80180060090048000000000C00600F00600E00E01E806066019196801400000000000000000000000000000000000000000000000000000000000000000000000000006001801E0050000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000100000000000000000000000000000000000000000000800000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000070030CB80300781300700200F80180060090048000000000C00600F00600E00E01E80606601919680140000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 7 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0.5 0.5 0 0 0.486155 0.513845 0 -0.205595 0.459323 0.540677 0 -0.604055 0.385086 0.614914 0 -1.70649 0.0743588 0.925641 0 -6.32083 0 0 0.724682 0 0 1 1 1 0 0 0.607056 2.94681 0.552665 1 1 0 1 1 1 1 1 2.9786e+06 3.45079e+06 562425 3.7395e+06 3.07181e+06 485823 0 1 0 0 75 1 75 0 0 0 -0.947012 -5.43077 2.94681 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 1 0 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 0 0 0 0 0 0 0 0 0 1 1 -1 -1 0 0 1 1 0 0 1 0 1 1 1 -1 -1 0 0 0 0 0 0 0 1 1 -1 -1 -1 -1 0 0 0 0 0 1 1 1 1 -1 -1 0 0 0 0 1 0 0 1 1 -1 -1 -1 0 0 0 0 0 0 1 1 1 -1 -1 -1 0 0 0 0 0 0 0 1 -1 -1 -1 -1 0 -1 0 0 0 1 1 1 1 1 -1 -1 0 0 0 0 0 0 -1 -1 1 1 -1 -1 0 0 0 0 0 0 0 0 -1 -1 1 1 -1 0 0 0 -1 -1 0 0 -1 1 -1 -1 1 -1 0 0 0 0 0 0 0 0 1 0 -1 1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 0 0 0 0 0 0 0 0 0 1 1 -1 -1 0 0 1 1 0 0 1 0 1 1 1 -1 -1 0 0 0 0 0 0 0 1 1 -1 -1 -1 -1 0 0 0 0 0 1 1 1 1 -1 -1 0 0 0 0 1 0 0 1 1 -1 -1 -1 0 0 0 0 0 0 1 1 1 -1 -1 -1 0 0 0 0 0 0 0 1 -1 -1 -1 -1 0 -1 0 0 0 1 1 1 1 1 -1 -1 0 0 0 0 0 0 -1 -1 1 1 -1 -1 0 0 0 0 0 0 0 0 -1 -1 1 1 -1 0 0 0 -1 -1 0 0 -1 1 -1 -1 1 -1 0 0 0 0 0 0 0 0 1 0 -1 1 -1 0 0 0 0 0 0 0 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 -120 -120 -120 -120 120 120 120 120 120 120 120 120 120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 0 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 -120 -120 120 -120 -120 -120 -120 -120 -120 -120 -120 0 120 0 -120 120 -120 -120 -120 -120 -120 -120 -120 -120 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 -26777 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -28312 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -368 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -426 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 7 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: abc
HASH: 9942C1270064011D1AE4EFE13F04B6F9
   A B C D E F G H J K L M N
13 O O . . X . O . O O O O O
12 . O O O O O O . . O O X X
11 O O O O O O O O O O O X X
10 . O O . O . O O O X X X X
 9 O O . O O O O O O X X . .
 8 O O O . . O O X X X X X .
 7 O . . O O O X X X . X X X
 6 . O . O X X X X X X X . X
 5 O O O O O X X X X . X . X
 4 X X O O X X . X X X . . .
 3 . X X O O X . . X X X X X
 2 X X X X O X X X X X . X .
 1 . O X X O X . X . . . . .


Initial pla Black
Encore phase 1
Turns this phase 1
Approx valid turns this phase 1
Approx consec valid turns this game 1
Rules koSIMPLEscoreTERRITORYtaxSEKIsui0komi12
Ko recap block hash 00000000000000000000000000000000
White bonus score -2
White handicap bonus score 0
Has button 0
Presumed next pla White
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves pass 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80080000C00600F00600F80EE1FD07AE6E193FEFA35000C2FBF33FF8D70DF87302700A00F8018006001004800000000000000000000000000000000000000000040000080000000000000000000000000006001801E00300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000004000008000000000000000000000000000000000000040000080000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 -0.5 0 0 0 1 1 0 1 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 28 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0 1 0 -3 0.00181983 0.99818 0 -2.99389 0.00534679 0.994653 0 -2.98205 0.015105 0.984895 0 -2.94928 0.0559488 0.944051 0 -2.81215 -3 0 0.0125211 0 0 1 1 1 0 0 0.149456 0.835817 0 1 1 0 1 1 1 1 1 2.9786e+06 3.45079e+06 562425 3.7395e+06 3.07181e+06 485823 -10 0 0 0 0 1 0 0 1 0 0.371037 1.03536 0.835817 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 1 0 -1 0 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 0 -1 -1 0 -1 0 -1 -1 -1 1 1 1 1 -1 -1 0 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 0 0 -1 -1 1 1 1 1 1 0 -1 0 0 -1 -1 -1 1 1 1 0 1 1 1 0 -1 0 -1 1 1 1 1 1 1 1 0 1 -1 -1 -1 -1 -1 1 1 1 1 0 1 0 1 1 1 -1 -1 1 1 0 1 1 1 0 0 0 0 1 1 -1 -1 1 0 0 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 0 1 0 0 -1 1 1 -1 1 0 1 0 0 0 0 0 -1 -1 0 0 1 0 -1 0 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 0 -1 -1 0 -1 0 -1 -1 -1 1 1 1 1 -1 -1 0 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 0 0 -1 -1 1 1 1 1 1 0 -1 0 0 -1 -1 -1 1 1 1 0 1 1 1 0 -1 0 -1 1 1 1 1 1 1 1 0 1 -1 -1 -1 -1 -1 1 1 1 1 0 1 0 1 1 1 -1 -1 1 1 0 1 1 1 0 0 0 0 1 1 -1 -1 1 0 0 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 0 1 0 0 -1 1 1 -1 1 0 1 0 0 0 0 0 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 120 120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 120 120 120 -120 -120 120 120 120 120 120 120 120 120 120 120 120 120 -120 120 120 120 120 120 120 120 120 120 120 120 120 -120 120 120 120 120 120 120 120 120 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -31410 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -29753 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -194 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -176 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 28 


seedBase: abc
HASH: 2AE066C40DF5CAE139EFE3E7240C5D23
   A B C D E F G H J K L M N
13 . . . . . . . . . O O O O
12 . . . . . . . . . O O X X
11 . . O O . . O . O O O X X
10 . . . . . . . O O X X X X
 9 . . . . . O O O O X X . .
 8 . . O . . O O X X X . . .
 7 . . . O O O X X X . . . .
 6 . . . O X X X X . X . . .
 5 O O O O O X X . . . . . .
 4 X X O O X X . . . . . . .
 3 . X X O O X . . . X X . .
 2 X O X X O X . . . . . . .
 1 . O . X O X . . . . . . .


Initial pla Black
Encore phase 0
Turns this phase 76
Approx valid turns this phase 76
Approx consec valid turns this game 76
Rules koSIMPLEscoreAREAtaxALLsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves A4 B5 B3 C4 C2 D3 D1 E2 D2 E3 C3 D4 B4 C5 A2 B1 F1 E1 F2 A5 F3 D6 F4 B2 G5 E7 H6 F8 J7 G9 K8 H10 F5 D5 G6 D7 H7 F9 L9 J11 M10 K12 F6 H9 K9 K11 L10 K13 N10 M13 N12 L13 N11 G11 K6 C11 L3 C8 K3 D11 E4 E5 E6 F7 G7 G8 H8 J9 J8 J10 K10 L11 M12 L12 M11 N13 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800070030CB80300781300700200F80180060090048000000000C00600F00600E00E01E806066019196801400000000000000000000000000000000000000000000000000000000000000000000000000006001801E0050000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000100000000000000000000000000000000000000000000800000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000070030CB80300781300700200F80180060090048000000000C00600F00600E00E01E80606601919680140000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 7 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0.5 0.5 0 0 0.486156 0.513844 0 -0.191562 0.459326 0.540674 0 -0.562822 0.385093 0.614907 0 -1.59 0.0743851 0.925615 0 -5.88937 0 0 0.724592 0 0 1 1 1 0 0 0.607056 2.94681 0.552665 1 1 0 1 1 1 1 1 2.9786e+06 3.45079e+06 562425 3.7395e+06 3.07181e+06 485823 0 1 0 0 75 1 75 0 0 0 -0.947012 -5.43077 2.94681 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 1 0 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 0 0 0 0 0 0 0 0 0 1 1 -1 -1 0 0 1 1 0 0 1 0 1 1 1 -1 -1 0 0 0 0 0 0 0 1 1 -1 -1 -1 -1 0 0 0 0 0 1 1 1 1 -1 -1 0 0 0 0 1 0 0 1 1 -1 -1 -1 0 0 0 0 0 0 1 1 1 -1 -1 -1 0 0 0 0 0 0 0 1 -1 -1 -1 -1 0 -1 0 0 0 1 1 1 1 1 -1 -1 0 0 0 0 0 0 -1 -1 1 1 -1 -1 0 0 0 0 0 0 0 0 -1 -1 1 1 -1 0 0 0 -1 -1 0 0 -1 1 -1 -1 1 -1 0 0 0 0 0 0 0 0 1 0 -1 1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 0 0 0 0 0 0 0 0 0 1 1 -1 -1 0 0 1 1 0 0 1 0 1 1 1 -1 -1 0 0 0 0 0 0 0 1 1 -1 -1 -1 -1 0 0 0 0 0 1 1 1 1 -1 -1 0 0 0 0 1 0 0 1 1 -1 -1 -1 0 0 0 0 0 0 1 1 1 -1 -1 -1 0 0 0 0 0 0 0 1 -1 -1 -1 -1 0 -1 0 0 0 1 1 1 1 1 -1 -1 0 0 0 0 0 0 -1 -1 1 1 -1 -1 0 0 0 0 0 0 0 0 -1 -1 1 1 -1 0 0 0 -1 -1 0 0 -1 1 -1 -1 1 -1 0 0 0 0 0 0 0 0 1 0 -1 1 -1 0 0 0 0 0 0 0 114 114 115 115 115 114 115 115 114 120 120 120 120 114 115 115 115 115 115 115 115 115 120 120 -120 -120 114 115 120 120 114 114 120 115 120 120 120 -120 -120 114 115 115 115 114 114 114 120 120 -120 -120 -120 -120 115 115 115 114 115 120 120 120 120 -120 -120 -114 -115 115 115 120 115 115 120 120 -120 -120 -120 -115 -114 -115 115 114 115 120 120 120 -120 -120 -120 -115 -115 -115 -115 115 115 115 120 -120 -120 -120 -120 -114 -120 -115 -115 -114 120 120 120 120 120 -120 -120 -115 -114 -114 -114 -114 -115 -120 -120 120 120 -120 -120 -114 -114 -115 -115 -115 -114 -114 0 -120 -120 120 120 -120 -115 -115 -115 -120 -120 -115 -114 -120 120 -120 -120 120 -120 -114 -114 -115 -115 -115 -115 -114 0 120 0 -120 120 -120 -115 -114 -115 -114 -114 -115 -115 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 -26776 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -28312 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -332 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -426 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 7 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: abc
HASH: 9942C1270064011D1AE4EFE13F04B6F9
   A B C D E F G H J K L M N
13 O O . . X . O . O O O O O
12 . O O O O O O . . O O X X
11 O O O O O O O O O O O X X
10 . O O . O . O O O X X X X
 9 O O . O O O O O O X X . .
 8 O O O . . O O X X X X X .
 7 O . . O O O X X X . X X X
 6 . O . O X X X X X X X . X
 5 O O O O O X X X X . X . X
 4 X X O O X X . X X X . . .
 3 . X X O O X . . X X X X X
 2 X X X X O X X X X X . X .
 1 . O X X O X . X . . . . .


Initial pla Black
Encore phase 1
Turns this phase 1
Approx valid turns this phase 1
Approx consec valid turns this game 1
Rules koSIMPLEscoreTERRITORYtaxALLsui0komi12
Ko recap block hash 00000000000000000000000000000000
White bonus score -2
White handicap bonus score 0
Has button 0
Presumed next pla White
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves pass 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80080000C00600F00600F80EE1FD07AE6E193FEFA35000C2FBF33FF8D70DF87302700A00F8018006001004800000000000000000000000000000000000000000040000080000000000000000000000000006001801E00300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000004000008000000000000000000000000000000000000040000080000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 -0.5 0 0 0 1 1 1 1 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 28 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0 1 0 -5 0.00181735 0.998183 0 -4.9801 0.00533951 0.99466 0 -4.94153 0.0150844 0.984916 0 -4.83481 0.0558726 0.944127 0 -4.38813 -5 0 0.012487 0 0 1 1 1 0 0 0.149456 0.835817 0 1 1 0 1 1 1 1 1 2.9786e+06 3.45079e+06 562425 3.7395e+06 3.07181e+06 485823 -10 0 0 0 0 1 0 0 1 0 0.371037 1.03536 0.835817 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 1 0 -1 0 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 0 -1 -1 0 -1 0 -1 -1 -1 1 1 1 1 -1 -1 0 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 0 0 -1 -1 1 1 1 1 1 0 -1 0 0 -1 -1 -1 1 1 1 0 1 1 1 0 -1 0 -1 1 1 1 1 1 1 1 0 1 -1 -1 -1 -1 -1 1 1 1 1 0 1 0 1 1 1 -1 -1 1 1 0 1 1 1 0 0 0 0 1 1 -1 -1 1 0 0 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 0 1 0 0 -1 1 1 -1 1 0 1 0 0 0 0 0 -1 -1 0 0 1 0 -1 0 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 0 -1 -1 0 -1 0 -1 -1 -1 1 1 1 1 -1 -1 0 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 0 0 -1 -1 1 1 1 1 1 0 -1 0 0 -1 -1 -1 1 1 1 0 1 1 1 0 -1 0 -1 1 1 1 1 1 1 1 0 1 -1 -1 -1 -1 -1 1 1 1 1 0 1 0 1 1 1 -1 -1 1 1 0 1 1 1 0 0 0 0 1 1 -1 -1 1 0 0 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 1 0 1 0 0 -1 1 1 -1 1 0 1 0 0 0 0 0 -120 -120 -107 -107 -106 -106 -120 -106 -120 -120 -120 -120 -120 -107 -120 -120 -120 -120 -120 -120 -106 -106 -120 -120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 -107 -120 -120 -107 -120 -106 -120 -120 -120 120 120 120 120 -120 -120 -106 -120 -120 -120 -120 -120 -120 120 120 108 109 -120 -120 -120 -106 -107 -120 -120 120 120 120 120 120 109 -120 -106 -107 -120 -120 -120 120 120 120 109 120 120 120 -107 -120 -107 -120 120 120 120 120 120 120 120 109 120 -120 -120 -120 -120 -120 120 120 120 120 108 120 109 120 120 120 -120 -120 120 120 108 120 120 120 108 108 108 40 120 120 -120 -120 120 108 108 120 120 120 120 120 120 120 120 120 -120 120 120 120 120 120 109 120 109 40 40 120 120 -120 120 108 120 109 109 109 109 109 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -31410 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -29758 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -194 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -277 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 28 


seedBase: def
HASH: 452B5D971BCF17F15C7FDE4562BD0A78
   A B C D E F G H J K L M N
13 X . X O . . . . O X X X .
12 . X X O . . . O . O O X O
11 . X O . O O O O O O O X O
10 . X O . O X X O X X O X O
 9 X X O O O X X X X O O X .
 8 X O . . O O X . X O O X X
 7 O O O O O X . . . X X O X
 6 . . . O X X . . . . X O O
 5 . . O X X . . . . X X O O
 4 . . O X . . . . . X O O .
 3 . . O X . X . . . X O O O
 2 . . O O X . . . . X O . X
 1 . . . O O X . . . X O X .


Initial pla Black
Encore phase 0
Turns this phase 112
Approx valid turns this phase 112
Approx consec valid turns this game 112
Rules koSIMPLEscoreAREAtaxNONEsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves K4 L10 M11 L11 M10 L9 M12 L12 M13 N12 M9 L8 M8 M7 L7 M6 K7 N11 N8 N10 K5 L3 K3 L4 K2 L2 L5 M5 J9 H10 J10 H11 H9 J11 G9 F11 F3 E9 F6 D7 D3 C5 C12 C11 B11 C10 B10 D12 B12 C9 A13 C3 D4 C4 L13 K12 K13 J13 N7 N6 L6 M3 B9 B8 C8 B7 C13 D13 D11 E11 D10 D9 E5 F8 N2 N3 M1 L1 N4 M4 K1 D2 E2 C2 G8 N5 F7 E7 F9 E8 F10 E10 A8 A7 A9 K8 J8 E1 F1 D1 D5 D6 G10 G11 K10 K11 E6 K9 D8 C7 J12 H12 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF801080834BFA5253862633E04203201901880E60418200A0730490048DAC79414C11A18418608205101090450000800400000000000000000000000000000000000000807000C006003001000C006003001801800E1050430010008000000D807801400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000008000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001800000000000000000000000000000000000000000000000000000000000000000000001080834FFA7253863E33E05E03E01F01F80FE04F8200E07704B0058DAC7941CC1FA1FC1FE0FE07F01F907D800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0.5 0.5 0 0 0.506306 0.493694 0 0.207597 0.518526 0.481474 0 0.609935 0.552338 0.447662 0 1.7231 0.693859 0.306141 0 6.38236 0 0 0.150326 0 0 1 1 1 0 0 0.296945 1.65148 0.46922 1 1 0 1 1 1 1 1 794402 2.00896e+06 549276 3.26873e+06 1.02836e+06 505939 0 1 0 0 111 1 111 0 0 0 0.42747 7.64275 1.65148 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 0 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 0 -1 1 1 1 1 1 -1 -1 -1 -1 -1 1 -1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 0 -120 -120 -120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 -120 -120 120 -120 -120 120 -120 120 -120 -120 120 120 120 -120 -120 -120 -120 120 120 -120 0 -120 120 120 120 120 120 -120 -120 -120 120 120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 0 -120 120 120 120 120 120 -120 -120 -120 -120 -120 120 -120 -120 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 14195 0 0 0 0 0 0 0 0 0 0 0 0 11857 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 404 0 0 0 0 0 0 0 0 0 0 0 0 374 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 6 0 0 0 0 0 0 0 0 0 0 0 0 23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: def
HASH: 452B5D971BCF17F15C7FDE4562BD0A78
   A B C D E F G H J K L M N
13 X . X O . . . . O X X X .
12 . X X O . . . O . O O X O
11 . X O . O O O O O O O X O
10 . X O . O X X O X X O X O
 9 X X O O O X X X X O O X .
 8 X O . . O O X . X O O X X
 7 O O O O O X . . . X X O X
 6 . . . O X X . . . . X O O
 5 . . O X X . . . . X X O O
 4 . . O X . . . . . X O O .
 3 . . O X . X . . . X O O O
 2 . . O O X . . . . X O . X
 1 . . . O O X . . . X O X .


Initial pla Black
Encore phase 0
Turns this phase 112
Approx valid turns this phase 112
Approx consec valid turns this game 112
Rules koSIMPLEscoreTERRITORYtaxNONEsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves K4 L10 M11 L11 M10 L9 M12 L12 M13 N12 M9 L8 M8 M7 L7 M6 K7 N11 N8 N10 K5 L3 K3 L4 K2 L2 L5 M5 J9 H10 J10 H11 H9 J11 G9 F11 F3 E9 F6 D7 D3 C5 C12 C11 B11 C10 B10 D12 B12 C9 A13 C3 D4 C4 L13 K12 K13 J13 N7 N6 L6 M3 B9 B8 C8 B7 C13 D13 D11 E11 D10 D9 E5 F8 N2 N3 M1 L1 N4 M4 K1 D2 E2 C2 G8 N5 F7 E7 F9 E8 F10 E10 A8 A7 A9 K8 J8 E1 F1 D1 D5 D6 G10 G11 K10 K11 E6 K9 D8 C7 J12 H12 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF801080834BFA5253862633E04203201901880E60418200A0730490048DAC79414C11A18418608205101090450000800400000000000000000000000000000000000000807000C006003001000C006003001801800E1050430010008000000D8078014000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000080000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0.05 0 0 0 1 0 0 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 24 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0.5 0.5 0 0 0.50882 0.49118 0 0.279774 0.525914 0.474086 0 0.821997 0.573209 0.426791 0 2.32219 0.771165 0.228835 0 8.60138 0 0 0.294122 0 0 1 1 1 0 0 0.250589 1.35658 0.459693 1 1 0 1 1 1 1 1 794402 2.00896e+06 549276 3.26873e+06 1.02836e+06 505939 1 0 0 0 111 1 111 0 0 0 0.907386 28.3782 1.35658 30 -1 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 0 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 0 -1 1 1 1 1 1 -1 -1 -1 -1 -1 1 -1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 0 -120 -120 -120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 -120 -120 120 -120 -120 120 -120 120 -120 -120 120 120 120 -120 -120 -120 -120 120 120 -120 0 -120 120 120 120 120 120 -120 -120 -120 120 120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 0 -120 120 120 120 120 120 -120 -120 -120 -120 -120 120 -120 -120 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 20991 0 0 0 0 0 0 0 0 0 0 0 0 16004 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 634 0 0 0 0 0 0 0 0 0 0 0 0 438 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 24 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: def
HASH: 452B5D971BCF17F15C7FDE4562BD0A78
   A B C D E F G H J K L M N
13 X . X O . . . . O X X X .
12 . X X O . . . O . O O X O
11 . X O . O O O O O O O X O
10 . X O . O X X O X X O X O
 9 X X O O O X X X X O O X .
 8 X O . . O O X . X O O X X
 7 O O O O O X . . . X X O X
 6 . . . O X X . . . . X O O
 5 . . O X X . . . . X X O O
 4 . . O X . . . . . X O O .
 3 . . O X . X . . . X O O O
 2 . . O O X . . . . X O . X
 1 . . . O O X . . . X O X .


Initial pla Black
Encore phase 0
Turns this phase 112
Approx valid turns this phase 112
Approx consec valid turns this game 112
Rules koSIMPLEscoreAREAtaxSEKIsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves K4 L10 M11 L11 M10 L9 M12 L12 M13 N12 M9 L8 M8 M7 L7 M6 K7 N11 N8 N10 K5 L3 K3 L4 K2 L2 L5 M5 J9 H10 J10 H11 H9 J11 G9 F11 F3 E9 F6 D7 D3 C5 C12 C11 B11 C10 B10 D12 B12 C9 A13 C3 D4 C4 L13 K12 K13 J13 N7 N6 L6 M3 B9 B8 C8 B7 C13 D13 D11 E11 D10 D9 E5 F8 N2 N3 M1 L1 N4 M4 K1 D2 E2 C2 G8 N5 F7 E7 F9 E8 F10 E10 A8 A7 A9 K8 J8 E1 F1 D1 D5 D6 G10 G11 K10 K11 E6 K9 D8 C7 J12 H12 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF801080834BFA5253862633E04203201901880E60418200A0730490048DAC79414C11A18418608205101090450000800400000000000000000000000000000000000000807000C006003001000C006003001801800E1050430010008000000D807801400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000008000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001800000000000000000000000000000000000000000000000000000000000000000000001080834BFA5253862633E04203201901880E60418200E07704B0058DAC7941CC1FA1FC1FE0FE07F01F907D000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0.5 0.5 0 0 0.506306 0.493694 0 0.207597 0.518526 0.481474 0 0.609935 0.552338 0.447662 0 1.7231 0.693859 0.306141 0 6.38236 0 0 0.150326 0 0 1 1 1 0 0 0.296945 1.65148 0.46922 1 1 0 1 1 1 1 1 794402 2.00896e+06 549276 3.26873e+06 1.02836e+06 505939 0 1 0 0 111 1 111 0 0 0 0.42747 7.64275 1.65148 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 0 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 0 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 0 -1 1 1 1 1 1 -1 -1 -1 -1 -1 1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 0 -120 -120 -120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 -120 -120 120 -120 -120 120 -120 120 -120 -120 120 120 120 -120 -120 -120 -120 120 120 -120 0 -120 120 120 120 120 120 -120 -120 -120 120 120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 0 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 0 -120 120 120 120 120 120 -120 -120 -120 -120 -120 120 -120 0 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 14195 0 0 0 0 0 0 0 0 0 0 0 0 11857 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 404 0 0 0 0 0 0 0 0 0 0 0 0 374 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 6 0 0 0 0 0 0 0 0 0 0 0 0 23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: def
HASH: 452B5D971BCF17F15C7FDE4562BD0A78
   A B C D E F G H J K L M N
13 X . X O . . . . O X X X .
12 . X X O . . . O . O O X O
11 . X O . O O O O O O O X O
10 . X O . O X X O X X O X O
 9 X X O O O X X X X O O X .
 8 X O . . O O X . X O O X X
 7 O O O O O X . . . X X O X
 6 . . . O X X . . . . X O O
 5 . . O X X . . . . X X O O
 4 . . O X . . . . . X O O .
 3 . . O X . X . . . X O O O
 2 . . O O X . . . . X O . X
 1 . . . O O X . . . X O X .


Initial pla Black
Encore phase 0
Turns this phase 112
Approx valid turns this phase 112
Approx consec valid turns this game 112
Rules koSIMPLEscoreTERRITORYtaxSEKIsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves K4 L10 M11 L11 M10 L9 M12 L12 M13 N12 M9 L8 M8 M7 L7 M6 K7 N11 N8 N10 K5 L3 K3 L4 K2 L2 L5 M5 J9 H10 J10 H11 H9 J11 G9 F11 F3 E9 F6 D7 D3 C5 C12 C11 B11 C10 B10 D12 B12 C9 A13 C3 D4 C4 L13 K12 K13 J13 N7 N6 L6 M3 B9 B8 C8 B7 C13 D13 D11 E11 D10 D9 E5 F8 N2 N3 M1 L1 N4 M4 K1 D2 E2 C2 G8 N5 F7 E7 F9 E8 F10 E10 A8 A7 A9 K8 J8 E1 F1 D1 D5 D6 G10 G11 K10 K11 E6 K9 D8 C7 J12 H12 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF801080834BFA5253862633E04203201901880E60418200A0730490048DAC79414C11A18418608205101090450000800400000000000000000000000000000000000000807000C006003001000C006003001801800E1050430010008000000D8078014000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000080000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0.05 0 0 0 1 1 0 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 24 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
0.5 0.5 0 0 0.50882 0.49118 0 0.279774 0.525914 0.474086 0 0.821997 0.573209 0.426791 0 2.32219 0.771165 0.228835 0 8.60138 0 0 0.294122 0 0 1 1 1 0 0 0.250589 1.35658 0.459693 1 1 0 1 1 1 1 1 794402 2.00896e+06 549276 3.26873e+06 1.02836e+06 505939 1 0 0 0 111 1 111 0 0 0 0.907386 28.3782 1.35658 30 -1 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 0 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 0 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 0 -1 1 1 1 1 1 -1 -1 -1 -1 -1 1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -120 -120 -120 120 120 120 120 120 120 -120 -120 -120 0 -120 -120 -120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 120 120 120 120 120 120 -120 120 -120 -120 120 120 120 -120 -120 120 -120 -120 120 -120 120 -120 -120 120 120 120 -120 -120 -120 -120 120 120 -120 0 -120 120 120 120 120 120 -120 -120 -120 120 120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 -120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 0 120 120 120 -120 -120 -120 -120 -120 -120 -120 120 120 120 120 120 120 120 -120 -120 -120 -120 -120 -120 120 0 -120 120 120 120 120 120 -120 -120 -120 -120 -120 120 -120 0 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 20991 0 0 0 0 0 0 0 0 0 0 0 0 16004 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 634 0 0 0 0 0 0 0 0 0 0 0 0 438 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 24 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: def
HASH: 452B5D971BCF17F15C7FDE4562BD0A78
   A B C D E F G H J K L M N
13 X . X O . . . . O X X X .
12 . X X O . . . O . O O X O
11 . X O . O O O O O O O X O
10 . X O . O X X O X X O X O
 9 X X O O O X X X X O O X .
 8 X O . . O O X . X O O X X
 7 O O O O O X . . . X X O X
 6 . . . O X X . . . . X O O
 5 . . O X X . . . . X X O O
 4 . . O X . . . . . X O O .
 3 . . O X . X . . . X O O O
 2 . . O O X . . . . X O . X
 1 . . . O O X . . . X O X .


Initial pla Black
Encore phase 0
Turns this phase 112
Approx valid turns this phase 112
Approx consec valid turns this game 112
Rules koSIMPLEscoreAREAtaxALLsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves K4 L10 M11 L11 M10 L9 M12 L12 M13 N12 M9 L8 M8 M7 L7 M6 K7 N11 N8 N10 K5 L3 K3 L4 K2 L2 L5 M5 J9 H10 J10 H11 H9 J11 G9 F11 F3 E9 F6 D7 D3 C5 C12 C11 B11 C10 B10 D12 B12 C9 A13 C3 D4 C4 L13 K12 K13 J13 N7 N6 L6 M3 B9 B8 C8 B7 C13 D13 D11 E11 D10 D9 E5 F8 N2 N3 M1 L1 N4 M4 K1 D2 E2 C2 G8 N5 F7 E7 F9 E8 F10 E10 A8 A7 A9 K8 J8 E1 F1 D1 D5 D6 G10 G11 K10 K11 E6 K9 D8 C7 J12 H12 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF801080834BFA5253862633E04203201901880E60418200A0730490048DAC79414C11A18418608205101090450000800400000000000000000000000000000000000000807000C006003001000C006003001801800E1050430010008000000D807801400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000008000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001800000000000000000000000000000000000000000000000000000000000000000000001080834BFA5253862633E04203201901880E60418200E07704B0058DAC7941CC1FA1FC1FE0FE07F01F907D000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
1 0 0 2 0.990042 0.00995773 0 2.14254 0.970743 0.0292566 0 2.4188 0.917349 0.0826513 0 3.18314 0.693859 0.306141 0 6.38236 2 0 0.374888 0 0 1 1 1 0 0 0.296945 1.65148 0.46922 1 1 0 1 1 1 1 1 794402 2.00896e+06 549276 3.26873e+06 1.02836e+06 505939 0 1 0 0 111 1 111 0 0 0 0.42747 7.64275 1.65148 30 0 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 0 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 0 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 0 -1 1 1 1 1 1 -1 -1 -1 -1 -1 1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -120 -60 -120 120 111 111 110 111 120 -120 -120 -120 0 -60 -120 -120 120 111 111 111 120 111 120 120 -120 120 -60 -120 120 110 120 120 120 120 120 120 120 -120 120 -60 -120 120 111 120 -120 -120 120 -120 -120 120 -120 120 -120 -120 120 120 120 -120 -120 -120 -120 120 120 -120 0 -120 120 111 111 120 120 -120 -112 -120 120 120 -120 -120 120 120 120 120 120 -120 -112 -112 -111 -120 -120 120 -120 111 111 111 120 -120 -120 -111 -112 -111 -111 -120 120 120 111 111 120 -120 -120 -112 -112 -112 -111 -120 -120 120 120 111 111 120 -120 -112 -111 -112 -111 -111 -120 120 120 0 111 111 120 -120 -111 -120 -112 -111 -112 -120 120 120 120 111 111 120 120 -120 -112 -112 -111 -111 -120 120 0 -120 111 111 111 120 120 -120 -111 -112 -111 -120 120 -120 0 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 14196 0 0 0 0 0 0 0 0 0 0 0 0 11857 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 404 0 0 0 0 0 0 0 0 0 0 0 0 374 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 6 0 0 0 0 0 0 0 0 0 0 0 0 23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 


seedBase: def
HASH: 452B5D971BCF17F15C7FDE4562BD0A78
   A B C D E F G H J K L M N
13 X . X O . . . . O X X X .
12 . X X O . . . O . O O X O
11 . X O . O O O O O O O X O
10 . X O . O X X O X X O X O
 9 X X O O O X X X X O O X .
 8 X O . . O O X . X O O X X
 7 O O O O O X . . . X X O X
 6 . . . O X X . . . . X O O
 5 . . O X X . . . . X X O O
 4 . . O X . . . . . X O O .
 3 . . O X . X . . . X O O O
 2 . . O O X . . . . X O . X
 1 . . . O O X . . . X O X .


Initial pla Black
Encore phase 0
Turns this phase 112
Approx valid turns this phase 112
Approx consec valid turns this game 112
Rules koSIMPLEscoreTERRITORYtaxALLsui0komi0
Ko recap block hash 00000000000000000000000000000000
White bonus score 0
White handicap bonus score 0
Has button 0
Presumed next pla Black
Past normal phase end 0
Game result 0 Empty 0 0 0 0
Last moves K4 L10 M11 L11 M10 L9 M12 L12 M13 N12 M9 L8 M8 M7 L7 M6 K7 N11 N8 N10 K5 L3 K3 L4 K2 L2 L5 M5 J9 H10 J10 H11 H9 J11 G9 F11 F3 E9 F6 D7 D3 C5 C12 C11 B11 C10 B10 D12 B12 C9 A13 C3 D4 C4 L13 K12 K13 J13 N7 N6 L6 M3 B9 B8 C8 B7 C13 D13 D11 E11 D10 D9 E5 F8 N2 N3 M1 L1 N4 M4 K1 D2 E2 C2 G8 N5 F7 E7 F9 E8 F10 E10 A8 A7 A9 K8 J8 E1 F1 D1 D5 D6 G10 G11 K10 K11 E6 K9 D8 C7 J12 H12 
binaryInputNCHWPacked
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|u1','fortran_order':False,'shape':(1,22,22)}                                                               
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF801080834BFA5253862633E04203201901880E60418200A0730490048DAC79414C11A18418608205101090450000800400000000000000000000000000000000000000807000C006003001000C006003001801800E1050430010008000000D8078014000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000080000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000

globalInputNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,16)}                                                                  
0 0 0 0 0 0.05 0 0 0 1 1 1 0 0 0 0 

policyTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,2,170)}                                                               
0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 24 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 

globalTargetsNC
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<f4','fortran_order':False,'shape':(1,64)}                                                                  
1 0 0 2 0.992557 0.00744324 0 2.21472 0.978131 0.0218688 0 2.63087 0.938219 0.0617805 0 3.78223 0.771165 0.228835 0 8.60138 2 0 0.209462 0 0 1 1 1 0 0 0.250589 1.35658 0.459693 1 1 0 1 1 1 1 1 794402 2.00896e+06 549276 3.26873e+06 1.02836e+06 505939 1 0 0 0 111 1 111 0 0 0 0.907386 28.3782 1.35658 30 -1 0 2 

scoreDistrN
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,458)}                                                                 
0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 50 50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 

valueTargetsNCHW
-109 78 85 77 80 89 1 0 -10 0 {'descr':'|i1','fortran_order':False,'shape':(1,5,13,13)}                                                             
-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 0 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 0 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 0 -1 1 1 1 1 1 -1 -1 -1 -1 -1 1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -1 0 -1 1 0 0 0 0 1 -1 -1 -1 0 0 -1 -1 1 0 0 0 1 0 1 1 -1 1 0 -1 1 0 1 1 1 1 1 1 1 -1 1 0 -1 1 0 1 -1 -1 1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 0 -1 1 0 0 1 1 -1 0 -1 1 1 -1 -1 1 1 1 1 1 -1 0 0 0 -1 -1 1 -1 0 0 0 1 -1 -1 0 0 0 0 -1 1 1 0 0 1 -1 -1 0 0 0 0 -1 -1 1 1 0 0 1 -1 0 0 0 0 0 -1 1 1 0 0 0 1 -1 0 -1 0 0 0 -1 1 1 1 0 0 1 1 -1 0 0 0 0 -1 1 0 -1 0 0 0 1 1 -1 0 0 0 -1 1 -1 0 -120 -60 -120 120 111 111 110 111 120 -120 -120 -120 0 -60 -120 -120 120 111 111 111 120 111 120 120 -120 120 -60 -120 120 110 120 120 120 120 120 120 120 -120 120 -60 -120 120 111 120 -120 -120 120 -120 -120 120 -120 120 -120 -120 120 120 120 -120 -120 -120 -120 120 120 -120 0 -120 120 111 111 120 120 -120 -112 -120 120 120 -120 -120 120 120 120 120 120 -120 -112 -112 -111 -120 -120 120 -120 111 111 111 120 -120 -120 -111 -112 -111 -111 -120 120 120 111 111 120 -120 -120 -112 -112 -112 -111 -120 -120 120 120 111 111 120 -120 -112 -111 -112 -111 -111 -120 120 120 0 111 111 120 -120 -111 -120 -112 -111 -112 -120 120 120 120 111 111 120 120 -120 -112 -112 -111 -111 -120 120 0 -120 111 111 111 120 120 -120 -111 -112 -111 -120 120 -120 0 

qValueTargetsNCMove
-109 78 85 77 80 89 1 0 -10 0 {'descr':'<i2','fortran_order':False,'shape':(1,3,170)}                                                               
0 0 0 0 0 0 0 20992 0 0 0 0 0 0 0 0 0 0 0 0 16004 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 633 0 0 0 0 0 0 0 0 0 0 0 0 438 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 24 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 


==============================================================
Also testing status logic inference!
Search assumes Black first
Rules koPOSITIONALscoreAREAtaxNONEsui1komi7.5
HASH: EE4728F0A3AC6E5DDDBDE8D7C1E1DBE4
   A B C D E F G H J
 9 . . . . . . . . .
 8 . O . . . O . . .
 7 . . X . . . . . .
 6 O O O O O O O O .
 5 X X X X X X X X .
 4 O X . . . . . . .
 3 . O X . X . . X .
 2 O O X . X . . . .
 1 . O . . . . . . .


.........
.a...a...
..d......
aaaaaaaa.
aaaaaaaa.
da.......
.da.a..a.
dda.a....
.d.......

    W    W    W    W    W    W    W    W  +98
    W    W    W    W    W    W    W    W  +98
    W    W  +98    W    W    W    W    W  +98
    W    W    W    W    W    W    W    W  -98
    B    B    B    B    B    B    B    B    B
  -97    B    B    B    B    B    B    B    B
  -98  -97    B    B    B    B    B    B    B
  -97  -97    B    B    B    B    B    B    B
  -98  -96    B    B    B    B    B    B    B

Search assumes White first
Rules koPOSITIONALscoreAREAtaxNONEsui1komi7.5
HASH: EE4728F0A3AC6E5DDDBDE8D7C1E1DBE4
   A B C D E F G H J
 9 . . . . . . . . .
 8 . O . . . O . . .
 7 . . X . . . . . .
 6 O O O O O O O O .
 5 X X X X X X X X .
 4 O X . . . . . . .
 3 . O X . X . . X .
 2 O O X . X . . . .
 1 . O . . . . . . .


.........
.a...a...
..d......
aaaaaaaa.
aaaaaaaa.
da.......
.da.a..a.
dda.a....
.d.......

    W    W    W    W    W    W    W    W    W
    W    W    W    W    W    W    W    W    W
    W    W    W    W    W    W    W    W    W
    W    W    W    W    W    W    W    W    W
    B    B    B    B    B    B    B    B  +96
  -98    B    B    B    B    B    B    B    B
    B  -98    B    B    B    B    B    B    B
  -98  -97    B    B    B    B    B    B    B
  -98  -96    B    B    B    B    B    B    B

Search assumes White first
Rules koPOSITIONALscoreAREAtaxNONEsui1komi7.5
HASH: 711C691648207A3B188626417281C9ED
   A B C D E F G H J
 9 O . O . X X O . X
 8 O O O O O X X X X
 7 X X X X X O O O O
 6 . . . . X . . . .
 5 X X X . . O . O .
 4 O O X X X . O . .
 3 . O O O X . O . .
 2 X O . O X . X O O
 1 . X X O X . X X .


a.a.aad.a
aaaaaaaaa
aaaaaaaaa
....a....
aaa..a.a.
aaaaa.a..
.aaaa.a..
aa.aa.aaa
.aaaa.aa.

   +5   -5  +40  -11  -70  -73  -42  -72  -47
  +21  +43  +55  +75  +85  -65  -71  -71  -70
    B    B    B    B  -97    W    W    W    W
  -97  -97  -95  -95    B  +97  +98  +98  +97
    B    B    B  -94  +92    W  +98    W  +97
  +66  +68  -98    B    B  +76    W  +97  +96
   -5  +62  +73  +66    B  +83    W  +95  +95
  -29  +58  +24  +68  -98  -82  -98  +95  +96
  -16  -36  -24  +70  -98  -94  -98  -97  -58

Search assumes White first
Rules koSIMPLEscoreTERRITORYtaxSEKIsui0komi6.5
HASH: 711C691648207A3B188626417281C9ED
   A B C D E F G H J
 9 O . O . X X O . X
 8 O O O O O X X X X
 7 X X X X X O O O O
 6 . . . . X . . . .
 5 X X X . . O . O .
 4 O O X X X . O . .
 3 . O O O X . O . .
 2 X O . O X . X O O
 1 . X X O X . X X .


d.d.aad.a
dddddaaaa
aaaaaaaaa
....a....
aaa..a.a.
ddaaa.a..
.ddda.a..
ad.da.aaa
.aada.aa.

  -30  -46  -15  -47  -78  -78  -69  -63  -55
  -28   -7   -8  +16  +33  -78  -80  -90  -69
    B    B    B    B    B    W    W    W    W
  -97  -96  -94  -89  -98  +44  +92  +95  +95
    B    B    B  -83   +4    W  +94    W  +95
   -7   +9    B    B  -98  +24    W  +95  +94
  -44   -9   +9  +20  -98  +54    W  +97  +94
  -54  -16  -29   +4  -96  -48  -94  +97  +98
  -43  -68  -65  +11  -97  -85  -94  -92  +20

==============================================================
: GPU 0 finishing, processed 775 rows 775 batches
Done
