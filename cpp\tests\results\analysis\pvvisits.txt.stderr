: Running with following config:
cudaUseFP16 = false
cudaUseNHWC = false
forDeterministicTesting = true
logAllRequests = true
logAllResponses = true
logFile = tests/results/analysis/pvvisits.txt.log
logSearchInfo = true
logTimeStamp = false
maxPlayouts = 10000
maxVisits = 100
nnCacheSizePowerOfTwo = 23
nnMaxBatchSize = 64
nnMutexPoolSizePowerOfTwo = 17
nnRandSeed = analysisTest
nnRandomize = false
numAnalysisThreads = 1
numSearchThreads = 1
openclUseFP16 = false
reportAnalysisWinratesAs = BLACK
rootSymmetryPruning = false
trtUseFP16 = false

: Analysis Engine starting...
: KataGo v1.16.2
: nnRandSeed0 = analysisTest
: After dedups: nnModelFile0 = tests/models/g170-b6c96-s175395328-d26788732.bin.gz useFP16 false useNHWC false
: Initializing neural net buffer to be size 19 * 19 allowing smaller boards
: Cuda backend thread 0: Found GPU NVIDIA RTX A5000 memory 25425608704 compute capability major 8 minor 6
: Cuda backend thread 0: Model version 8 useFP16 = false useNHWC = false
: Cuda backend thread 0: Model name: g170-b6c96-s175395328-d26788732
: Loaded config configs/analysis_example.cfg and/or command-line and query overrides
: Loaded model tests/models/g170-b6c96-s175395328-d26788732.bin.gz
: Config override: cudaUseFP16 = false
: Config override: cudaUseNHWC = false
: Config override: forDeterministicTesting = true
: Config override: logAllRequests = true
: Config override: logAllResponses = true
: Config override: logDir = 
: Config override: logFile = tests/results/analysis/pvvisits.txt.log
: Config override: logSearchInfo = true
: Config override: logTimeStamp = false
: Config override: maxPlayouts = 10000
: Config override: maxVisits = 100
: Config override: nnRandSeed = analysisTest
: Config override: nnRandomize = false
: Config override: numAnalysisThreads = 1
: Config override: numSearchThreadsPerAnalysisThread = 1
: Config override: openclUseFP16 = false
: Config override: rootSymmetryPruning = false
: Config override: trtUseFP16 = false
: Analyzing up to 1 positions at a time in parallel
: Started, ready to begin handling requests
: Request: {"id":"pvvisittest","initialStones":[],"moves":[["B","D4"],["W","D5"],["B","C5"],["W","E4"],["B","D3"],["W","C6"],["B","E5"],["W","D6"]],"rules":"tromp-taylor","komi":9,"boardXSize":7,"boardYSize":7,"overrideSettings":{"maxVisits":1000},"includePVVisits":true}
: Request: {"id":"pvvisittest","initialStones":[],"moves":[["B","D4"],["W","D5"],["B","C5"],["W","E4"],["B","D3"],["W","E5"],["B","C6"],["W","E3"],["B","E2"],["W","F2"],["B","D2"],["W","C4"],["B","D6"],["W","B4"]],"rules":"chinese","komi":9,"boardXSize":7,"boardYSize":7,"overrideSettings":{"maxVisits":1000},"includePVVisits":true}
: MoveNum: 8 HASH: 68923BD112145D32565C85CE0EF9CB35
   A B C D E F G
 7 . . . . . . .
 6 . . O1O3. . .
 5 . . X O X2. .
 4 . . . X O . .
 3 . . . X . . .
 2 . . . . . . .
 1 . . . . . . .

koPOSITIONALscoreAREAtaxNONEsui1komi9
Root visits: 1000
New playouts: 1000
NN rows: 873
NN batches: 873
NN avg batch size: 1
PV: E3 F4 F5 F6 F3 C4 B5 B4 B6 B7 B3 A5 C3 A6 E6
Tree:
: T   5.70c W  13.38c S  -4.34c ( +0.4 L  +0.4) N    1000  --  E3 F4 F5 F6 F3 C4 B5
---Black(^)---
E3  : T  18.43c W  23.65c S  -4.12c ( +0.9 L  +0.9) LCB    6.59c P  2.45% WF 497.3 PSV     445 N     453  --  E3 F4 F5 F6 F3 C4 B5 B4
F4  : T -11.01c W  -2.43c S  -5.72c ( -0.2 L  -0.2) LCB  -32.00c P 40.25% WF 146.6 PSV      83 N     169  --  F4 C4 C3 B5 B4 F5 E3 E6
E6  : T -22.73c W -11.31c S  -4.94c ( -0.9 L  -1.0) LCB  -44.68c P 38.59% WF  71.5 PSV      57 N      90  --  E6 E3 E2 F2 B5 B6 D2 F5
F5  : T  -1.03c W   9.68c S  -4.19c ( +0.2 L  +0.2) LCB  -18.49c P 17.70% WF 272.4 PSV      55 N     286  --  F5 B5 C4 B4 B3 E3 D2 E2
B5  : T -85.41c W -71.58c S -13.83c ( -4.9 L  -4.3) LCB -435.41c P  0.79% WF   0.8 PSV       0 N       1  --  B5

: Response: {"id":"pvvisittest","isDuringSearch":false,"moveInfos":[{"edgeVisits":453,"edgeWeight":444.566206,"lcb":0.575969984,"move":"E3","order":0,"playSelectionValue":444.566206,"prior":0.024539493,"pv":["E3","F4","F5","F6","F3","C4","B5","B4","B6","B7","B3","A5","C3","A6","E6"],"pvEdgeVisits":[453,268,260,212,194,98,74,61,44,15,9,6,4,3,1],"pvVisits":[453,268,260,212,194,98,74,61,44,15,9,6,4,3,1],"scoreLead":0.876836711,"scoreMean":0.876836711,"scoreSelfplay":0.930208339,"scoreStdev":7.02856264,"utility":0.184278803,"utilityLcb":0.0659439857,"visits":453,"weight":444.566206,"winrate":0.618232418},{"edgeVisits":169,"edgeWeight":168.587573,"lcb":0.412862051,"move":"F4","order":1,"playSelectionValue":83.0,"prior":0.402488917,"pv":["F4","C4","C3","B5","B4","F5","E3","E6","C5","E4","B6","C4","E5"],"pvEdgeVisits":[169,131,110,105,77,34,20,17,6,5,3,2,1],"pvVisits":[169,131,110,105,77,34,20,17,6,5,3,2,1],"scoreLead":-0.238332115,"scoreMean":-0.238332115,"scoreSelfplay":-0.226421316,"scoreStdev":9.51598137,"utility":-0.110119867,"utilityLcb":-0.320022992,"visits":169,"weight":168.587573,"winrate":0.487827452},{"edgeVisits":90,"edgeWeight":89.9123304,"lcb":0.365034897,"move":"E6","order":2,"playSelectionValue":57.0,"prior":0.385945201,"pv":["E6","E3","E2","F2","B5","B6","D2","F5","F6"],"pvEdgeVisits":[90,59,31,28,5,3,3,2,1],"pvVisits":[90,59,31,28,5,6,3,2,1],"scoreLead":-1.03280841,"scoreMean":-1.03280841,"scoreSelfplay":-0.860583148,"scoreStdev":15.790381,"utility":-0.227315483,"utilityLcb":-0.446827254,"visits":90,"weight":89.9123304,"winrate":0.443431958},{"edgeVisits":286,"edgeWeight":284.474866,"lcb":0.486057287,"move":"F5","order":3,"playSelectionValue":55.0,"prior":0.176997334,"pv":["F5","B5","C4","B4","B3","E3","D2","E2","F4","B2","A4","A5","B6"],"pvEdgeVisits":[286,255,156,116,91,80,70,46,21,17,6,4,1],"pvVisits":[286,255,156,116,91,80,70,46,21,17,6,4,1],"scoreLead":0.173774678,"scoreMean":0.173774678,"scoreSelfplay":0.23475126,"scoreStdev":11.6782578,"utility":-0.0103324739,"utilityLcb":-0.184937398,"visits":286,"weight":284.474866,"winrate":0.548416189},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-1.10789931,"move":"B5","order":4,"playSelectionValue":0.0,"prior":0.00789487734,"pv":["B5"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-4.31511497,"scoreMean":-4.31511497,"scoreSelfplay":-4.92428255,"scoreStdev":10.3026649,"utility":-0.854056469,"utilityLcb":-4.35405649,"visits":1,"weight":1.0,"winrate":0.142100699}],"rootInfo":{"currentPlayer":"B","rawLead":2.50616407,"rawNoResultProb":0.0,"rawScoreSelfplay":3.91906309,"rawScoreSelfplayStdev":9.32987957,"rawStScoreError":-1.0,"rawStWrError":-0.5,"rawVarTimeLeft":-1.0,"rawWinrate":0.*********,"scoreLead":0.*********,"scoreSelfplay":0.*********,"scoreStdev":8.96842384,"symHash":"10155F672C0B5682E08EB339027AD849","thisHash":"E2B811FEE059E65F93697CB1DE35B543","utility":0.0934868382,"visits":1000,"weight":989.5409758592832,"winrate":0.*********},"turnNumber":8}
: MoveNum: 14 HASH: B56076090A467FAA5A715507E32E1149
   A B C D E F G
 7 . . . . . . .
 6 . . X X2. . .
 5 . . X O O . .
 4 . O3O1X O . .
 3 . . . X O . .
 2 . . . X X O .
 1 . . . . . . .

koSIMPLEscoreAREAtaxNONEsui0whbNfpok1komi9
Root visits: 1000
New playouts: 1000
NN rows: 1762
NN batches: 1762
NN avg batch size: 1
PV: C3 E6 F1 B5 B6 D7 A6 F3 C7 E7 A4 G2 E1 G1 B3 pass A5 pass pass
Tree:
: T  29.02c W  30.19c S  -2.13c ( +1.1 L  +1.0) N    1000  --  C3 E6 F1 B5 B6 D7 A6
---Black(^)---
C3  : T  35.46c W  35.72c S  -1.68c ( +1.4 L  +1.3) LCB   28.08c P  9.60% WF 833.7 PSV     787 N     793  --  C3 E6 F1 B5 B6 D7 A6 F3
F1  : T   5.53c W   9.00c S  -2.06c ( +0.7 L  +0.7) LCB  -15.97c P 26.24% WF  82.4 PSV      46 N     102  --  F1 E6 B5 B2 C2 B3 F3 D7
B2  : T   3.05c W   7.37c S  -4.21c ( +0.1 L  +0.1) LCB  -36.56c P 19.58% WF  57.2 PSV      31 N      71  --  B2 E6 B5 D7 C7 E7 F1 A6
E6  : T -67.71c W -48.43c S -12.11c ( -6.6 L  -6.9) LCB -115.09c P 20.50% WF   8.4 PSV      10 N      15  --  E6 C3 C2 B2 F1 F3 B1
B3  : T -88.35c W -51.03c S -16.58c (-10.7 L  -9.1) LCB -146.48c P 12.47% WF   4.4 PSV       5 N       8  --  B3 C3 C2 B2 F1
B5  : T -64.39c W -52.08c S -11.61c ( -6.4 L  -6.2) LCB  -88.19c P 10.33% WF   4.4 PSV       5 N       7  --  B5 B2 C2 B3
F3  : T -57.34c W -46.99c S -10.35c ( -4.7 L  -4.4) LCB -407.34c P  0.94% WF   0.8 PSV       0 N       1  --  F3
G2  : T -68.88c W -56.05c S -12.83c ( -6.5 L  -5.7) LCB -418.88c P  0.10% WF   0.7 PSV       0 N       1  --  G2
F6  : T -99.48c W -79.79c S -19.69c (-13.9 L -12.0) LCB -449.48c P  0.08% WF   0.7 PSV       0 N       1  --  F6

: Response: {"id":"pvvisittest","isDuringSearch":false,"moveInfos":[{"edgeVisits":793,"edgeWeight":786.673008,"lcb":0.652237374,"move":"C3","order":0,"playSelectionValue":786.673008,"prior":0.0960063189,"pv":["C3","E6","F1","B5","B6","D7","A6","F3","C7","E7","A4","G2","E1","G1","B3","pass"],"pvEdgeVisits":[793,509,488,376,374,125,90,74,68,57,18,13,10,8,6,5],"pvVisits":[793,509,488,376,374,125,90,74,68,57,18,13,10,8,6,5],"scoreLead":1.29828199,"scoreMean":1.29828199,"scoreSelfplay":1.3950855,"scoreStdev":9.89407037,"utility":0.354617979,"utilityLcb":0.280810929,"visits":793,"weight":786.673008,"winrate":0.678597035},{"edgeVisits":102,"edgeWeight":101.869682,"lcb":0.468191835,"move":"F1","order":1,"playSelectionValue":46.0,"prior":0.26238814,"pv":["F1","E6","B5","B2","C2","B3","F3","D7","C7","E7"],"pvEdgeVisits":[102,61,56,50,22,15,11,4,2,1],"pvVisits":[102,61,56,50,22,15,11,4,2,1],"scoreLead":0.682430466,"scoreMean":0.682430466,"scoreSelfplay":0.663772015,"scoreStdev":17.3692951,"utility":0.0553045711,"utilityLcb":-0.159746591,"visits":102,"weight":101.869682,"winrate":0.544995821},{"edgeVisits":71,"edgeWeight":71.0,"lcb":0.395418891,"move":"B2","order":2,"playSelectionValue":31.0,"prior":0.195804924,"pv":["B2","E6","B5","D7","C7","E7","F1","A6","A4","A5"],"pvEdgeVisits":[71,61,56,32,27,18,12,5,2,1],"pvVisits":[71,61,56,32,27,18,12,5,2,1],"scoreLead":0.12790975500000001,"scoreMean":0.12790975500000001,"scoreSelfplay":0.0761589862,"scoreStdev":10.2067119,"utility":0.0304630889,"utilityLcb":-0.365556632,"visits":71,"weight":71.0,"winrate":0.536854505},{"edgeVisits":15,"edgeWeight":15.0,"lcb":0.0886770621,"move":"E6","order":3,"playSelectionValue":10.0,"prior":0.205015883,"pv":["E6","C3","C2","B2","F1","F3","B1"],"pvEdgeVisits":[15,11,6,5,3,2,1],"pvVisits":[15,11,6,5,3,2,1],"scoreLead":-6.94677813,"scoreMean":-6.94677813,"scoreSelfplay":-6.64989997,"scoreStdev":16.4329229,"utility":-0.677121418,"utilityLcb":-1.15085452,"visits":15,"weight":15.0,"winrate":0.257867456},{"edgeVisits":8,"edgeWeight":8.0,"lcb":0.037224531,"move":"B3","order":4,"playSelectionValue":5.0,"prior":0.12468569,"pv":["B3","C3","C2","B2","F1"],"pvEdgeVisits":[8,7,3,2,1],"pvVisits":[8,7,3,2,1],"scoreLead":-9.08254817,"scoreMean":-9.08254817,"scoreSelfplay":-10.6984885,"scoreStdev":17.1132663,"utility":-0.883480214,"utilityLcb":-1.46484868,"visits":8,"weight":8.0,"winrate":0.244856125},{"edgeVisits":7,"edgeWeight":7.0,"lcb":0.154619932,"move":"B5","order":5,"playSelectionValue":5.0,"prior":0.103275739,"pv":["B5","B2","C2","B3"],"pvEdgeVisits":[7,6,2,1],"pvVisits":[7,6,2,1],"scoreLead":-6.20094748,"scoreMean":-6.20094748,"scoreSelfplay":-6.38497322,"scoreStdev":16.7719949,"utility":-0.643863802,"utilityLcb":-0.881865074,"visits":7,"weight":7.0,"winrate":0.239620386},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-0.98493681,"move":"F3","order":6,"playSelectionValue":0.0,"prior":0.0093550859,"pv":["F3"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-4.40834856,"scoreMean":-4.40834856,"scoreSelfplay":-4.69527817,"scoreStdev":14.6450335,"utility":-0.573367821,"utilityLcb":-4.07336784,"visits":1,"weight":1.0,"winrate":0.265063196},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-1.03027137,"move":"G2","order":7,"playSelectionValue":0.0,"prior":0.000978292897,"pv":["G2"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-5.69961214,"scoreMean":-5.69961214,"scoreSelfplay":-6.53693628,"scoreStdev":14.8002952,"utility":-0.688812329,"utilityLcb":-4.18881235,"visits":1,"weight":1.0,"winrate":0.219728634},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-1.14895124,"move":"F6","order":8,"playSelectionValue":0.0,"prior":0.000811504433,"pv":["F6"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-12.0221157,"scoreMean":-12.0221157,"scoreSelfplay":-13.8977699,"scoreStdev":17.5401848,"utility":-0.994838871,"utilityLcb":-4.49483889,"visits":1,"weight":1.0,"winrate":0.101048771}],"rootInfo":{"currentPlayer":"B","rawLead":1.90460968,"rawNoResultProb":8.25451571e-05,"rawScoreSelfplay":3.37504053,"rawScoreSelfplayStdev":12.2505929,"rawStScoreError":-1.0,"rawStWrError":-0.5,"rawVarTimeLeft":-1.0,"rawWinrate":0.689557821,"scoreLead":1.03168034,"scoreSelfplay":1.10982168,"scoreStdev":10.7128548,"symHash":"0CAA03496D1088226717248B65E75E44","thisHash":"3F4A5C26F80BC4C79F44AC7833E26F3F","utility":0.303443451,"visits":1000,"weight":993.5426907577031,"winrate":0.657007593},"turnNumber":14}
: tests/models/g170-b6c96-s175395328-d26788732.bin.gz
: NN rows: 1762
: NN batches: 1762
: NN avg batch size: 1
: GPU -1 finishing, processed 1762 rows 1762 batches
: All cleaned up, quitting
