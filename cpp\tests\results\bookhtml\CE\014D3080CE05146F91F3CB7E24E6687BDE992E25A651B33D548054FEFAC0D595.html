
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 2;
const board = [0,0,0,0,0,0,0,0,0,0,0,1,0,2,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,3],],'p':0.0604,'wl':-0.1243,'ssM':0.63,'wlRad':0.0132,'sRad':0.55,'v':103,'av':103,},{'xy':[[0,1],],'p':0.3600,'wl':0.2035,'ssM':0.38,'wlRad':0.0875,'sRad':0.84,'v':75,'av':75,},{'xy':[[0,0],],'p':0.7470,'wl':0.3667,'ssM':-0.79,'wlRad':0.0607,'sRad':1.54,'v':17,'av':17,},{'xy':[[0,3],],'p':0.6138,'wl':0.3748,'ssM':0.21,'wlRad':0.0165,'sRad':0.63,'v':67,'av':67,},{'xy':[[3,1],],'p':0.1229,'wl':0.3579,'ssM':0.67,'wlRad':0.2210,'sRad':1.26,'v':73,'av':73,},{'xy':[[2,0],],'p':0.4438,'wl':0.8799,'ssM':0.42,'wlRad':0.0756,'sRad':0.46,'v':31,'av':31,},{'move':'other','p':0.1255,'wl':0.7530,'ssM':-0.14,'wlRad':0.0099,'sRad':1.59,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
