
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../01/087A91D601B7AE323571DE219ADFC452E15F44577C11C1F33DD932B7DC3CAC06.html';
const pSym = 4;
const board = [1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[0,1],],'p':0.4060,'wl':-0.6817,'ssM':0.13,'wlRad':0.0402,'sRad':0.86,'v':16,'av':16,},{'xy':[[3,0],],'p':0.6184,'wl':-0.4600,'ssM':-0.11,'wlRad':0.1877,'sRad':0.54,'v':20,'av':20,},{'xy':[[2,0],],'p':0.9414,'wl':-0.2761,'ssM':0.25,'wlRad':0.1172,'sRad':0.44,'v':11,'av':11,},{'xy':[[1,1],],'p':0.9341,'wl':-0.0873,'ssM':0.44,'wlRad':0.0046,'sRad':0.30,'v':13,'av':13,},{'xy':[[2,3],],'p':0.7149,'wl':0.4869,'ssM':-0.59,'wlRad':0.0241,'sRad':0.97,'v':31,'av':31,},{'xy':[[1,3],],'p':0.9344,'wl':0.4710,'ssM':-0.02,'wlRad':0.0740,'sRad':0.81,'v':13,'av':13,},{'xy':[[3,3],],'p':0.2613,'wl':0.8193,'ssM':1.30,'wlRad':0.0709,'sRad':1.21,'v':88,'av':88,},{'xy':[[2,1],],'p':0.6252,'wl':0.8363,'ssM':1.48,'wlRad':0.2474,'sRad':0.80,'v':57,'av':57,},{'move':'other','p':0.1076,'wl':-0.4888,'ssM':0.79,'wlRad':0.0870,'sRad':0.11,'v':10,'av':10,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
