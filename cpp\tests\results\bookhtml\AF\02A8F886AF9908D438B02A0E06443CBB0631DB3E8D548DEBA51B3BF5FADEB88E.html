
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 2;
const pLink = '../A0/23A3B394A056CC98CAA720A4EFE26605E43170679500026C119E60ACC0A5E25D.html';
const pSym = 6;
const board = [0,0,0,0,0,0,0,0,0,0,1,1,0,0,2,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,1],],'p':0.3940,'wl':0.4949,'ssM':-0.10,'wlRad':0.0126,'sRad':1.76,'v':88,'av':88,},{'xy':[[2,1],],'p':0.4679,'wl':-0.0365,'ssM':0.40,'wlRad':0.0751,'sRad':0.64,'v':69,'av':69,},{'xy':[[3,0],],'p':0.7087,'wl':-0.1630,'ssM':-0.57,'wlRad':0.0688,'sRad':1.00,'v':113,'av':113,},{'move':'other','p':0.0278,'wl':0.5448,'ssM':-0.70,'wlRad':0.0135,'sRad':0.56,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
