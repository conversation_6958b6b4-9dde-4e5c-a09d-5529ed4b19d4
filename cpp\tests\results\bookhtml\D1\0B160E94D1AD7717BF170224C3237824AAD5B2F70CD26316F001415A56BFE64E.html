
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 2;
const board = [0,0,0,0,0,0,0,0,0,0,0,1,2,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,1],],'p':0.4421,'wl':-0.2282,'ssM':0.16,'wlRad':0.1917,'sRad':0.88,'v':43,'av':43,},{'xy':[[2,2],],'p':0.9373,'wl':0.3524,'ssM':0.26,'wlRad':0.0091,'sRad':0.95,'v':149,'av':149,},{'xy':[[3,3],],'p':0.5447,'wl':0.4837,'ssM':0.77,'wlRad':0.0744,'sRad':1.50,'v':175,'av':175,},{'xy':[[0,1],],'p':0.1042,'wl':0.5005,'ssM':1.26,'wlRad':0.1764,'sRad':1.21,'v':89,'av':89,},{'xy':[[0,0],],'p':0.1259,'wl':0.7871,'ssM':-0.02,'wlRad':0.0803,'sRad':0.77,'v':91,'av':91,},{'xy':[[1,3],],'p':0.8743,'wl':0.8412,'ssM':0.51,'wlRad':0.1470,'sRad':0.91,'v':102,'av':102,},{'move':'other','p':0.1039,'wl':0.1650,'ssM':0.09,'wlRad':0.2547,'sRad':0.65,'v':15,'av':15,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
