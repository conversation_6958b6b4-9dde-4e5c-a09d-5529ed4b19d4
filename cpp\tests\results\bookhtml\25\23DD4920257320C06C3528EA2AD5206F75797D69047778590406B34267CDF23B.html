
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../31/F17EA86A31F03431F3D61EC3DFDB2CEDB673834A312D577285070E44F6822F41.html';
const pSym = 0;
const board = [0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,3],[0,1],],'p':0.1563,'wl':-0.3632,'ssM':0.04,'wlRad':0.0981,'sRad':0.66,'v':53,'av':53,},{'xy':[[3,0],],'p':0.7507,'wl':0.2990,'ssM':1.06,'wlRad':0.0787,'sRad':1.23,'v':62,'av':62,},{'xy':[[1,3],[0,2],],'p':0.5487,'wl':0.9056,'ssM':0.18,'wlRad':0.0857,'sRad':1.49,'v':18,'av':18,},{'xy':[[2,2],[1,1],],'p':0.6100,'wl':0.9552,'ssM':0.37,'wlRad':0.0337,'sRad':0.82,'v':110,'av':110,},{'move':'other','p':0.0941,'wl':0.0666,'ssM':0.46,'wlRad':0.0805,'sRad':0.97,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
