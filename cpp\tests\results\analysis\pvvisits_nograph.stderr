: Running with following config:
cudaUseFP16 = false
cudaUseNHWC = false
forDeterministicTesting = true
logAllRequests = true
logAllResponses = true
logFile = tests/results/analysis/pvvisits_nograph.txt.log
logSearchInfo = true
logTimeStamp = false
maxVisits = 500
nnCacheSizePowerOfTwo = 23
nnMaxBatchSize = 64
nnMutexPoolSizePowerOfTwo = 17
nnRandSeed = analysisTest
nnRandomize = false
numAnalysisThreads = 1
numSearchThreads = 1
openclUseFP16 = false
reportAnalysisWinratesAs = BLACK
rootSymmetryPruning = false
trtUseFP16 = false
useGraphSearch = false

: Analysis Engine starting...
: KataGo v1.16.2
: nnRandSeed0 = analysisTest
: After dedups: nnModelFile0 = tests/models/g170-b6c96-s175395328-d26788732.bin.gz useFP16 false useNHWC false
: Initializing neural net buffer to be size 19 * 19 allowing smaller boards
: Cuda backend thread 0: Found GPU NVIDIA RTX A5000 memory 25425608704 compute capability major 8 minor 6
: Cuda backend thread 0: Model version 8 useFP16 = false useNHWC = false
: Cuda backend thread 0: Model name: g170-b6c96-s175395328-d26788732
: Loaded config configs/analysis_example.cfg and/or command-line and query overrides
: Loaded model tests/models/g170-b6c96-s175395328-d26788732.bin.gz
: Config override: cudaUseFP16 = false
: Config override: cudaUseNHWC = false
: Config override: forDeterministicTesting = true
: Config override: logAllRequests = true
: Config override: logAllResponses = true
: Config override: logDir = 
: Config override: logFile = tests/results/analysis/pvvisits_nograph.txt.log
: Config override: logSearchInfo = true
: Config override: logTimeStamp = false
: Config override: nnRandSeed = analysisTest
: Config override: nnRandomize = false
: Config override: numAnalysisThreads = 1
: Config override: numSearchThreadsPerAnalysisThread = 1
: Config override: openclUseFP16 = false
: Config override: rootSymmetryPruning = false
: Config override: trtUseFP16 = false
: Config override: useGraphSearch = false
: Analyzing up to 1 positions at a time in parallel
: Started, ready to begin handling requests
: Request: {"id":"pvvisittest","initialStones":[],"moves":[["B","D4"],["W","D5"],["B","C5"],["W","E4"],["B","D3"],["W","C6"],["B","E5"],["W","D6"]],"rules":"tromp-taylor","komi":9,"boardXSize":7,"boardYSize":7,"overrideSettings":{"maxVisits":1000},"includePVVisits":true}
: Request: {"id":"pvvisittest","initialStones":[],"moves":[["B","D4"],["W","D5"],["B","C5"],["W","E4"],["B","D3"],["W","E5"],["B","C6"],["W","E3"],["B","E2"],["W","F2"],["B","D2"],["W","C4"],["B","D6"],["W","B4"]],"rules":"chinese","komi":9,"boardXSize":7,"boardYSize":7,"overrideSettings":{"maxVisits":1000},"includePVVisits":true}
: MoveNum: 8 HASH: 68923BD112145D32565C85CE0EF9CB35
   A B C D E F G
 7 . . . . . . .
 6 . . O1O3. . .
 5 . . X O X2. .
 4 . . . X O . .
 3 . . . X . . .
 2 . . . . . . .
 1 . . . . . . .

koPOSITIONALscoreAREAtaxNONEsui1komi9
Root visits: 1000
New playouts: 1000
NN rows: 762
NN batches: 762
NN avg batch size: 1
PV: F5 B5 C4 B4 B3 E3 D2 E2 F4 B2 E1
Tree:
: T  17.68c W  21.45c S  -2.51c ( +1.3 L  +1.2) N    1000  --  F5 B5 C4 B4 B3 E3 D2
---Black(^)---
F5  : T  23.66c W  26.61c S  -1.42c ( +1.9 L  +1.7) LCB   13.10c P 17.70% WF 597.7 PSV     561 N     585  --  F5 B5 C4 B4 B3 E3 D2 E2
F4  : T  -0.93c W   4.98c S  -4.79c ( +0.2 L  +0.2) LCB  -21.49c P 40.25% WF 132.0 PSV      85 N     157  --  F4 C4 C3 B5 B4 F5 E3 E6
E6  : T -15.68c W  -9.27c S  -4.40c ( -0.5 L  -0.7) LCB  -41.76c P 38.59% WF  56.7 PSV      52 N      75  --  E6 F4 F5 C4 B5 C3 D2 B6
E3  : T  22.30c W  26.34c S  -4.19c ( +0.9 L  +0.9) LCB    6.59c P  2.45% WF 186.9 PSV      43 N     180  --  E3 F4 F5 F6 F3 E6 B5 G5
B5  : T -85.41c W -71.58c S -13.83c ( -4.9 L  -4.3) LCB -435.41c P  0.79% WF   0.7 PSV       0 N       1  --  B5
B6  : T -83.20c W -70.77c S -12.42c ( -4.9 L  -3.1) LCB -433.20c P  0.04% WF   0.6 PSV       0 N       1  --  B6

: Response: {"id":"pvvisittest","isDuringSearch":false,"moveInfos":[{"edgeVisits":585,"edgeWeight":561.358881,"lcb":0.59534161,"move":"F5","order":0,"playSelectionValue":561.358881,"prior":0.176997334,"pv":["F5","B5","C4","B4","B3","E3","D2","E2","F4","B2","E1"],"pvEdgeVisits":[585,163,111,52,49,41,34,10,6,3,1],"pvVisits":[585,163,111,52,49,41,34,10,6,3,1],"scoreLead":1.66101904,"scoreMean":1.66101904,"scoreSelfplay":1.86381632,"scoreStdev":10.2500802,"utility":0.236614376,"utilityLcb":0.131009054,"visits":585,"weight":561.358881,"winrate":0.633057796},{"edgeVisits":157,"edgeWeight":156.949859,"lcb":0.45147042,"move":"F4","order":1,"playSelectionValue":85.0,"prior":0.402488917,"pv":["F4","C4","C3","B5","B4","F5","E3","E6","C5","E4","B6","C4"],"pvEdgeVisits":[157,118,106,101,78,27,16,13,5,4,2,1],"pvVisits":[157,118,106,101,78,27,16,13,5,4,2,1],"scoreLead":0.241261019,"scoreMean":0.241261019,"scoreSelfplay":0.247204823,"scoreStdev":9.49897677,"utility":-0.0092889543,"utilityLcb":-0.214942477,"visits":157,"weight":156.949859,"winrate":0.524918107},{"edgeVisits":75,"edgeWeight":74.9559717,"lcb":0.360513255,"move":"E6","order":2,"playSelectionValue":52.0,"prior":0.385945201,"pv":["E6","F4","F5","C4","B5","C3","D2","B6","C2"],"pvEdgeVisits":[75,32,21,13,9,7,3,2,1],"pvVisits":[75,32,21,13,9,7,3,2,1],"scoreLead":-0.713662814,"scoreMean":-0.713662814,"scoreSelfplay":-0.494140835,"scoreStdev":15.8468123,"utility":-0.156797045,"utilityLcb":-0.417601883,"visits":75,"weight":74.9559717,"winrate":0.45365784},{"edgeVisits":180,"edgeWeight":179.594992,"lcb":0.575572652,"move":"E3","order":3,"playSelectionValue":43.0,"prior":0.024539493,"pv":["E3","F4","F5","F6","F3","E6","B5","G5","B6","G3","F2","G2","B7","F1","E1"],"pvEdgeVisits":[180,110,105,75,59,43,28,27,25,21,15,7,3,2,1],"pvVisits":[180,110,105,75,59,43,28,27,25,21,15,7,3,2,1],"scoreLead":0.900497247,"scoreMean":0.900497247,"scoreSelfplay":0.94718168,"scoreStdev":6.72172226,"utility":0.222978612,"utilityLcb":0.0658610238,"visits":180,"weight":179.594992,"winrate":0.631686076},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-1.10789931,"move":"B5","order":4,"playSelectionValue":0.0,"prior":0.00789487734,"pv":["B5"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-4.31511497,"scoreMean":-4.31511497,"scoreSelfplay":-4.92428255,"scoreStdev":10.3026649,"utility":-0.854056469,"utilityLcb":-4.35405649,"visits":1,"weight":1.0,"winrate":0.142100699},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-1.10387475,"move":"B6","order":5,"playSelectionValue":0.0,"prior":0.000405064406,"pv":["B6"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-3.12851071,"scoreMean":-3.12851071,"scoreSelfplay":-4.92141008,"scoreStdev":12.3884691,"utility":-0.831956677,"utilityLcb":-4.33195669,"visits":1,"weight":1.0,"winrate":0.146125257}],"rootInfo":{"currentPlayer":"B","rawLead":2.50616407,"rawNoResultProb":0.0,"rawScoreSelfplay":3.91906309,"rawScoreSelfplayStdev":9.32987957,"rawStScoreError":-1.0,"rawStWrError":-0.5,"rawVarTimeLeft":-1.0,"rawWinrate":0.*********,"scoreLead":1.28920402,"scoreSelfplay":1.4632206,"scoreStdev":10.5207545,"symHash":"10155F672C0B5682E08EB339027AD849","thisHash":"E2B811FEE059E65F93697CB1DE35B543","utility":0.*********,"visits":1000,"weight":975.6506190028175,"winrate":0.*********},"turnNumber":8}
: MoveNum: 14 HASH: B56076090A467FAA5A715507E32E1149
   A B C D E F G
 7 . . . . . . .
 6 . . X X2. . .
 5 . . X O O . .
 4 . O3O1X O . .
 3 . . . X O . .
 2 . . . X X O .
 1 . . . . . . .

koSIMPLEscoreAREAtaxNONEsui0whbNfpok1komi9
Root visits: 1000
New playouts: 1000
NN rows: 1653
NN batches: 1653
NN avg batch size: 1
PV: C3 E6 F1 B5 B6 D7 A6 F3 C7 E7 A4 G2 E1 G1 B3 pass A5 pass pass
Tree:
: T  29.01c W  29.95c S  -2.36c ( +1.0 L  +0.9) N    1000  --  C3 E6 F1 B5 B6 D7 A6
---Black(^)---
C3  : T  35.36c W  35.42c S  -1.97c ( +1.3 L  +1.2) LCB   27.80c P  9.60% WF 833.8 PSV     788 N     793  --  C3 E6 F1 B5 B6 D7 A6 F3
F1  : T   6.63c W   9.78c S  -1.98c ( +0.7 L  +0.7) LCB  -14.77c P 26.24% WF  82.5 PSV      48 N     101  --  F1 E6 B5 B2 C2 B3 F3 D7
B2  : T   3.14c W   7.35c S  -4.21c ( +0.1 L  +0.1) LCB  -36.41c P 19.58% WF  57.2 PSV      32 N      71  --  B2 E6 B5 D7 C7 E7 F1 A6
E6  : T -64.23c W -46.28c S -11.46c ( -6.1 L  -6.3) LCB -116.75c P 20.50% WF   8.0 PSV      11 N      14  --  E6 C3 C2 B2 F1
B5  : T -59.03c W -50.26c S -10.85c ( -6.1 L  -5.9) LCB  -84.21c P 10.33% WF   5.6 PSV       5 N       9  --  B5 B2 C2 B3 F1 F3
B3  : T -88.64c W -51.03c S -16.58c (-10.7 L  -9.1) LCB -146.28c P 12.47% WF   4.4 PSV       5 N       8  --  B3 C3 C2 B2 F1
F3  : T -57.34c W -46.99c S -10.35c ( -4.7 L  -4.4) LCB -407.34c P  0.94% WF   0.8 PSV       0 N       1  --  F3
G2  : T -68.88c W -56.05c S -12.83c ( -6.5 L  -5.7) LCB -418.88c P  0.10% WF   0.7 PSV       0 N       1  --  G2
F6  : T -99.48c W -79.79c S -19.69c (-13.9 L -12.0) LCB -449.48c P  0.08% WF   0.7 PSV       0 N       1  --  F6

: Response: {"id":"pvvisittest","isDuringSearch":false,"moveInfos":[{"edgeVisits":793,"edgeWeight":787.780181,"lcb":0.650074502,"move":"C3","order":0,"playSelectionValue":787.780181,"prior":0.0960063189,"pv":["C3","E6","F1","B5","B6","D7","A6","F3","C7","E7","A4","G2","E1","G1","B3","pass"],"pvEdgeVisits":[793,528,507,395,393,145,103,85,79,66,26,20,17,14,7,6],"pvVisits":[793,528,507,395,393,145,103,85,79,66,26,20,17,14,7,6],"scoreLead":1.18812451,"scoreMean":1.18812451,"scoreSelfplay":1.27715313,"scoreStdev":9.49262464,"utility":0.353611377,"utilityLcb":0.277996695,"visits":793,"weight":787.780181,"winrate":0.677079746},{"edgeVisits":101,"edgeWeight":100.869682,"lcb":0.472471036,"move":"F1","order":1,"playSelectionValue":48.0,"prior":0.26238814,"pv":["F1","E6","B5","B2","C2","B3","F3","D7","C7","E7"],"pvEdgeVisits":[101,60,55,49,21,14,10,3,2,1],"pvVisits":[101,60,55,49,21,14,10,3,2,1],"scoreLead":0.734135563,"scoreMean":0.734135563,"scoreSelfplay":0.731684014,"scoreStdev":17.2273674,"utility":0.0662598528,"utilityLcb":-0.147689564,"visits":101,"weight":100.869682,"winrate":0.5488815419999999},{"edgeVisits":71,"edgeWeight":71.0,"lcb":0.395502028,"move":"B2","order":2,"playSelectionValue":32.0,"prior":0.195804924,"pv":["B2","E6","B5","D7","C7","E7","F1","A6","A4","A5"],"pvEdgeVisits":[71,61,56,32,27,18,12,5,2,1],"pvVisits":[71,61,56,32,27,18,12,5,2,1],"scoreLead":0.124981029,"scoreMean":0.124981029,"scoreSelfplay":0.0732972982,"scoreStdev":10.2096562,"utility":0.0313885445,"utilityLcb":-0.364053393,"visits":71,"weight":71.0,"winrate":0.536731292},{"edgeVisits":14,"edgeWeight":14.0,"lcb":0.0810459812,"move":"E6","order":3,"playSelectionValue":11.0,"prior":0.205015883,"pv":["E6","C3","C2","B2","F1"],"pvEdgeVisits":[14,10,4,3,1],"pvVisits":[14,10,4,3,1],"scoreLead":-6.33317387,"scoreMean":-6.33317387,"scoreSelfplay":-6.0924605,"scoreStdev":16.2884315,"utility":-0.642295254,"utilityLcb":-1.1674807,"visits":14,"weight":14.0,"winrate":0.268612211},{"edgeVisits":9,"edgeWeight":9.0,"lcb":0.158805102,"move":"B5","order":4,"playSelectionValue":5.0,"prior":0.103275739,"pv":["B5","B2","C2","B3","F1","F3"],"pvEdgeVisits":[9,8,4,3,2,1],"pvVisits":[9,8,4,3,2,1],"scoreLead":-5.8938767,"scoreMean":-5.8938767,"scoreSelfplay":-6.11950803,"scoreStdev":17.7054049,"utility":-0.590348791,"utilityLcb":-0.842123874,"visits":9,"weight":9.0,"winrate":0.248724774},{"edgeVisits":8,"edgeWeight":8.0,"lcb":0.0389938711,"move":"B3","order":5,"playSelectionValue":5.0,"prior":0.12468569,"pv":["B3","C3","C2","B2","F1"],"pvEdgeVisits":[8,7,3,2,1],"pvVisits":[8,7,3,2,1],"scoreLead":-9.08254817,"scoreMean":-9.08254817,"scoreSelfplay":-10.6984885,"scoreStdev":17.1132663,"utility":-0.886363714,"utilityLcb":-1.46277803,"visits":8,"weight":8.0,"winrate":0.244856125},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-0.98493681,"move":"F3","order":6,"playSelectionValue":0.0,"prior":0.0093550859,"pv":["F3"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-4.40834856,"scoreMean":-4.40834856,"scoreSelfplay":-4.69527817,"scoreStdev":14.6450335,"utility":-0.573367821,"utilityLcb":-4.07336784,"visits":1,"weight":1.0,"winrate":0.265063196},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-1.03027137,"move":"G2","order":7,"playSelectionValue":0.0,"prior":0.000978292897,"pv":["G2"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-5.69961214,"scoreMean":-5.69961214,"scoreSelfplay":-6.53693628,"scoreStdev":14.8002952,"utility":-0.688812329,"utilityLcb":-4.18881235,"visits":1,"weight":1.0,"winrate":0.219728634},{"edgeVisits":1,"edgeWeight":1.0,"lcb":-1.14895124,"move":"F6","order":8,"playSelectionValue":0.0,"prior":0.000811504433,"pv":["F6"],"pvEdgeVisits":[1],"pvVisits":[1],"scoreLead":-12.0221157,"scoreMean":-12.0221157,"scoreSelfplay":-13.8977699,"scoreStdev":17.5401848,"utility":-0.994838871,"utilityLcb":-4.49483889,"visits":1,"weight":1.0,"winrate":0.101048771}],"rootInfo":{"currentPlayer":"B","rawLead":1.90460968,"rawNoResultProb":8.25451571e-05,"rawScoreSelfplay":3.37504053,"rawScoreSelfplayStdev":12.2505929,"rawStScoreError":-1.0,"rawStWrError":-0.5,"rawVarTimeLeft":-1.0,"rawWinrate":0.689557821,"scoreLead":0.935710229,"scoreSelfplay":1.006822,"scoreStdev":10.400954,"symHash":"0CAA03496D1088226717248B65E75E44","thisHash":"3F4A5C26F80BC4C79F44AC7833E26F3F","utility":0.301988675,"visits":1000,"weight":994.6498639362214,"winrate":0.65524489},"turnNumber":14}
: tests/models/g170-b6c96-s175395328-d26788732.bin.gz
: NN rows: 1653
: NN batches: 1653
: NN avg batch size: 1
: GPU -1 finishing, processed 1653 rows 1653 batches
: All cleaned up, quitting
