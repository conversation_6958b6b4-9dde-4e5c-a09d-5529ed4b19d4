= analysisWideRootNoise analysisIgnorePreRootHistory genmoveAntiMirror antiMirror humanSLProfile allowResignation ponderingEnabled delayMoveScale delayMoveMax chosenMovePrune chosenMoveSubtract chosenMoveTemperature chosenMoveTemperatureEarly chosenMoveTemperatureHalflife chosenMoveTemperatureOnlyBelowProb conservativePass cpuctExploration cpuctExplorationBase cpuctExplorationLog cpuctUtilityStdevPrior cpuctUtilityStdevPriorWeight cpuctUtilityStdevScale drawEquivalentWinsForWhite dynamicScoreCenterScale dynamicScoreCenterZeroWeight dynamicScoreUtilityFactor enableMorePassingHacks enablePassingHacks endgameTurnTimeDecay fillDameBeforePass fpuLossProp fpuParentWeight fpuParentWeightByVisitedPolicy fpuParentWeightByVisitedPolicyPow fpuReductionMax futileVisitsThreshold graphSearchCatchUpLeakProb graphSearchRepBound humanSLChosenMoveIgnorePass humanSLChosenMovePiklLambda humanSLChosenMoveProp humanSLCpuctExploration humanSLCpuctPermanent humanSLOppExploreProbWeightful humanSLOppExploreProbWeightless humanSLPlaExploreProbWeightful humanSLPlaExploreProbWeightless humanSLRootExploreProbWeightful humanSLRootExploreProbWeightless ignoreAllHistory ignorePreRootHistory lagBuffer lcbStdevs maxPlayouts maxPlayoutsPondering maxTime maxTimePondering maxVisits maxVisitsPondering midgameTimeFactor midgameTurnPeakTime minPlayoutsPerThread minVisitPropForLCB nnPolicyTemperature noResultUtilityForWhite noisePruneUtilityScale noisePruningCap numSearchThreads numVirtualLossesPerThread obviousMovesPolicyEntropyTolerance obviousMovesPolicySurpriseTolerance obviousMovesTimeFactor overallocateTimeFactor playoutDoublingAdvantage playoutDoublingAdvantagePla policyOptimism rootDesiredPerChildVisitsCoeff rootDirichletNoiseTotalConcentration rootDirichletNoiseWeight rootEndingBonusPoints rootFpuLossProp rootFpuReductionMax rootNoiseEnabled rootNumSymmetriesToSample rootPolicyOptimism rootPolicyTemperature rootPolicyTemperatureEarly rootPruneUselessMoves rootSymmetryPruning searchFactorAfterOnePass searchFactorAfterTwoPass staticScoreUtilityFactor subtreeValueBiasFactor subtreeValueBiasFreeProp subtreeValueBiasTableNumShards subtreeValueBiasWeightExponent treeReuseCarryOverTimeFactor uncertaintyCoeff uncertaintyExponent uncertaintyMaxWeight useGraphSearch useLcbForSelection useNoisePruning useNonBuggyLcb useUncertainty valueWeightExponent wideRootNoise winLossUtilityFactor

= 

= 50.5

= 

= -23

= 1.16.2+g170-b6c96-s175M

= [{"internalName":"g170-b6c96-s175395328-d26788732","maxBatchSize":8,"name":"tests/models/g170-b6c96-s175395328-d26788732.bin.gz","usesHumanSLProfile":false,"usingFP16":"false","version":8}]

? Could not set params: WARNING: humanSLProfile is specified as config param but model(s) don't use it: tests/models/g170-b6c96-s175395328-d26788732.bin.gz


= true

= false

= 1e+06

= 100

= 

= 

= false

= false

= 1234

= 100

= 

= 

= false

= false

= 12345

= 4321

