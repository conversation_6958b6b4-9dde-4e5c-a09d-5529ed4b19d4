
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 4;
const board = [0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,2,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,1],],'p':0.3849,'wl':-0.5004,'ssM':0.96,'wlRad':0.0682,'sRad':0.55,'v':74,'av':74,},{'xy':[[3,2],],'p':0.2538,'wl':-0.3982,'ssM':-0.24,'wlRad':0.1267,'sRad':0.80,'v':92,'av':92,},{'xy':[[0,1],],'p':0.9414,'wl':-0.3998,'ssM':0.79,'wlRad':0.0125,'sRad':0.91,'v':93,'av':93,},{'xy':[[1,1],],'p':0.7046,'wl':-0.2907,'ssM':-0.09,'wlRad':0.1880,'sRad':0.38,'v':54,'av':54,},{'xy':[[0,0],],'p':0.7148,'wl':0.0223,'ssM':-0.76,'wlRad':0.0180,'sRad':0.78,'v':46,'av':46,},{'xy':[[2,1],],'p':0.0178,'wl':0.0191,'ssM':0.38,'wlRad':0.0377,'sRad':0.64,'v':108,'av':108,},{'xy':[[2,3],],'p':0.2546,'wl':0.4095,'ssM':0.66,'wlRad':0.1714,'sRad':0.58,'v':64,'av':64,},{'xy':[[0,2],],'p':0.1078,'wl':0.4837,'ssM':0.77,'wlRad':0.0744,'sRad':1.50,'v':175,'av':175,},{'xy':[[2,2],],'p':0.5089,'wl':0.7639,'ssM':0.25,'wlRad':0.0823,'sRad':1.07,'v':33,'av':33,},{'xy':[[2,0],],'p':0.1967,'wl':0.7871,'ssM':-0.02,'wlRad':0.0803,'sRad':0.77,'v':91,'av':91,},{'move':'pass','p':0.3626,'wl':0.9536,'ssM':-0.52,'wlRad':0.0770,'sRad':0.55,'v':95,'av':95,},{'xy':[[1,3],],'p':0.0084,'wl':0.9322,'ssM':0.25,'wlRad':0.0842,'sRad':1.06,'v':83,'av':83,},{'move':'other','p':0.0701,'wl':-0.3215,'ssM':-1.70,'wlRad':0.0209,'sRad':2.24,'v':14,'av':14,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
