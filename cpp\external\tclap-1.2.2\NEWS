
4/3/03 -  Checked in a good sized update that move support of the library
closer to that of the POSIX/GNU standards.  Switches can now be combined into
single arguments, -- is supported and MultiArgs now allow for multiple labeled
args.  I've also changed things a bit by subclassing MultiArg and ValueArg
to get unlabeled versions of these classes.  I think this is a bit cleaner
design, despite two new classes.

1/7/04 - ... and with great trepidation, I release 0.9.6.  Loads of changes.  
The big change is that you can now define the delimiter used to separate 
argument flags and argument values. So if you prefer arguments of the style 
"-s=asdf"  instead of "-s asdf", you can do so.  I've also fixed a number of 
warnings generated and fixed a few pathologic bugs related to combined
switches.  That said, I suspect that there may be a few significant bugs
in this release that I haven't uncovered yet.  Please let me know ASAP if
you find any.

2/6/04 - Another big release: 0.9.7.  First is a bugfix submitted by 
<PERSON> that specializes the _extractValue method in a couple of
places that allows strings with spaces to be correctly read by tclap.  A
second bug found by <PERSON> has been fixed so that exceptions are thrown 
if more than one value is parsed from a single arg or if the second value 
parsed is invalid.   A big new feature has been added that allows args to
be xor'd.  This means that two (or more) args can be specified such that
one and only one of the args is required.  If a second arg is found an
exception is thrown.  See the manual for details.  As always, let me know
if you run into any problems.

2/10/04 - A minor release: 0.9.8.  A couple of bug fixes for 0.9.7 are
included and a feature has been added that allows Args to be specified 
without short options, meaning the user is forced to use only long options.
This is useful for programs with more options than map sensibly to single
chars.

7/3/04 - Added a new constructor and handling to the various value args
that allows the user to provide a list of values that the input arg values 
should be restricted to.  

8/9/04 - Created a function to print the output nicely, meaning line wraps
are handled somewhat sensibly now.  Also changed error handling slightly. 
Instead of printing the entire usage, I just print a short usage.  If
someone really hates this, its easy to change back.  Let me know if this 
causes problems.  I think this equals release 0.9.9!

10/19/04 - A number of changes that should substantially improve the library.
The most important being that we've moved the implementation of the library
entirely into the header files.  This means there is no longer a library to
complile against, you simply have to #include <tclap/CmdLine.h>.  New
constructors have been added to the various Arg classes that allow them to 
be constructed with a CmdLine reference so that you no longer need to call
the add method if you prefer it that way.  The output generated by the library
has been confined to a few methods in the CmdLine class.  This means to 
generate different output you can extend CmdLine and override the offending
methods.  A number of style changes have been made in the code base to 
conform better to C++ best practices.   A thoughtful user has contributed
project files for the building the examples Microsoft Visual Studio.  See
the README file in the msc directory for more details

And so we have release 1.0!

10/30/04 - A few bugfixes.  Now checking for include.h before including it. 
This will help Windows users who don't have it.  Also changed test1 so that 
it doesn't use toupper, which apparently causes problem for non-ASCII 
character sets.

10/31/04 - A few more tweaks, none of which should be noticeable to people
who are already using the lib without trouble. Maybe I shouldn't release 
things early in the morning!  Also note that manual.html is now generated 
from manual.xml.  If you have your own docbook xsl style that you prefer, 
then have at it.

12/3/04 - Some minor bug fixes including the removal of the two stage name
lookup ifdefs which means that the software should work out of the box 
for gcc 3.4+.  Isolated output in a separate class that should make
customization of output easier.  I also included a rudimentary output class
that generated a (bad) Docbook command summary when used. 

1/4/05 - Several bug fixes, but no new features. Fixed a bug when mandatory 
long args and unlabeled args were used together and weren't working properly.
Now they can be used together. Fixed another bug in spacePrint where long 
program names caused an infinite loop.  Finally, fixed a small memory leak.

1/6/05 - Fixed a bug where setting the output object for a CmdLine didn't
register for version or usage generation. Doh!  Created a Constraint interface
that should facilitate the creation of different constraints on Args.
This has involved changing the constructor interface, so if you've been using
allowed lists, you'll need to make a small modification to your existing code.
See examples/test6.cpp for details.

9/26/09 - Whoa, long break.  Primarily a bug-fix release, but we did switch 
to using traits, which necessitates the minor version bump. Take a look 
at test11.cpp and test12.cpp for examples on using ArgTraits for extending 
tclap for different types.

4/16/11 - Another long break!  Several minor bug and memory leak fixes.

12/26/17 - v1.2 bug fix release