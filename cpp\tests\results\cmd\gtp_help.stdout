
DESCRIPTION: 

   Run KataGo main GTP engine for playing games or casual analysis.

USAGE: 

   gtp  [-config <FILE>] ...  [-model <FILE>] [-human-model <FILE>]
        [...other flags...]


Where: 

   -config <FILE>  (accepted multiple times)
     Config file(s) to use, can be one or multiple files (see
     gtp_example.cfg or configs/gtp_example.cfg). Defaults to: (dir
     containing katago.exe, or else ~/.katago)/default_gtp.cfg

   -model <FILE>
     Neural net model file. Defaults to: (dir containing katago.exe, or
     else ~/.katago)/default_model.bin.gz

   -human-model <FILE>
     Human SL neural net model file

   -override-config <KEYVALUEPAIRS>  (accepted multiple times)
     Override config parameters. Format: "key=value, key=value,..."

   -override-version <VERSION>
     Force KataGo to say a certain value in response to gtp version command

   --,  -ignore_rest
     Ignores the rest of the labeled arguments following this flag.

   -version
     Displays version information and exits.

   -h,  -help
     Displays usage information and exits.



