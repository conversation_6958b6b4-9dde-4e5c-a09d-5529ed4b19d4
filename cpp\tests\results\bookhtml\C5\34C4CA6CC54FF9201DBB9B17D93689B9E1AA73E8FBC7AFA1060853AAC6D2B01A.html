
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 7;
const board = [0,0,2,1,0,0,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,2],],'p':0.5553,'wl':-0.1605,'ssM':0.10,'wlRad':0.0810,'sRad':0.81,'v':105,'av':105,},{'xy':[[1,1],],'p':0.4481,'wl':0.1080,'ssM':0.53,'wlRad':0.0013,'sRad':0.42,'v':187,'av':187,},{'xy':[[2,3],],'p':0.3806,'wl':0.2711,'ssM':1.55,'wlRad':0.1106,'sRad':0.77,'v':63,'av':63,},{'xy':[[3,3],],'p':0.9665,'wl':0.5298,'ssM':-0.11,'wlRad':0.0076,'sRad':0.78,'v':40,'av':40,},{'move':'pass','p':0.0022,'wl':0.6887,'ssM':0.35,'wlRad':0.1679,'sRad':1.36,'v':25,'av':25,},{'xy':[[0,2],],'p':0.7384,'wl':0.7574,'ssM':0.41,'wlRad':0.1584,'sRad':1.00,'v':135,'av':135,},{'move':'other','p':0.0713,'wl':0.7108,'ssM':-0.35,'wlRad':0.1758,'sRad':1.40,'v':13,'av':13,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
