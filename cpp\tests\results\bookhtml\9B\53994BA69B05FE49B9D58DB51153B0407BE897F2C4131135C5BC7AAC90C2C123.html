
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 2;
const board = [1,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,1],],'p':0.0748,'wl':-0.1944,'ssM':0.50,'wlRad':0.0098,'sRad':0.99,'v':63,'av':63,},{'xy':[[0,3],],'p':0.2864,'wl':0.1647,'ssM':0.20,'wlRad':0.0971,'sRad':0.52,'v':112,'av':112,},{'xy':[[2,3],],'p':0.5587,'wl':0.3462,'ssM':0.56,'wlRad':0.0912,'sRad':0.57,'v':148,'av':148,},{'xy':[[1,0],],'p':0.1215,'wl':0.7984,'ssM':0.92,'wlRad':0.0668,'sRad':0.94,'v':105,'av':105,},{'xy':[[1,1],],'p':0.3847,'wl':0.9315,'ssM':0.07,'wlRad':0.0530,'sRad':1.06,'v':46,'av':46,},{'xy':[[3,0],],'p':0.4223,'wl':0.9990,'ssM':-0.40,'wlRad':0.0369,'sRad':1.18,'v':56,'av':56,},{'move':'other','p':0.2119,'wl':0.0683,'ssM':0.30,'wlRad':0.0191,'sRad':0.97,'v':13,'av':13,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
