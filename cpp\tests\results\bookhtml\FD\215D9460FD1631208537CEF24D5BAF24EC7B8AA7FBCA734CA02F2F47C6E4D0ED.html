
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../C3/1274A7ACC3E0122274760B9AD42F975B532B2A27B2D70E0C8656897329704B81.html';
const pSym = 6;
const board = [0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,2],],'p':0.0647,'wl':-0.3100,'ssM':-0.19,'wlRad':0.0924,'sRad':0.87,'v':65,'av':65,},{'xy':[[1,1],],'p':0.5737,'wl':-0.0182,'ssM':0.20,'wlRad':0.0141,'sRad':0.61,'v':83,'av':83,},{'xy':[[0,3],],'p':0.9407,'wl':0.2516,'ssM':0.64,'wlRad':0.1537,'sRad':0.82,'v':40,'av':40,},{'xy':[[3,0],],'p':0.4307,'wl':0.2711,'ssM':1.55,'wlRad':0.1106,'sRad':0.77,'v':63,'av':63,},{'xy':[[2,1],],'p':0.6074,'wl':0.7322,'ssM':0.51,'wlRad':0.1816,'sRad':1.07,'v':174,'av':174,},{'xy':[[1,3],],'p':0.8937,'wl':0.7678,'ssM':0.77,'wlRad':0.0071,'sRad':0.81,'v':123,'av':123,},{'move':'other','p':0.1818,'wl':0.6216,'ssM':0.16,'wlRad':0.0809,'sRad':1.00,'v':16,'av':16,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
