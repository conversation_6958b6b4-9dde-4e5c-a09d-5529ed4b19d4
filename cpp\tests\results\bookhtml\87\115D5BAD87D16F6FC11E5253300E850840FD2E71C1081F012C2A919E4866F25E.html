
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../01/087A91D601B7AE323571DE219ADFC452E15F44577C11C1F33DD932B7DC3CAC06.html';
const pSym = 7;
const board = [0,0,0,0,0,0,0,0,0,0,0,0,0,2,0,1,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,0],],'p':0.7093,'wl':-0.3592,'ssM':1.02,'wlRad':0.0082,'sRad':0.85,'v':16,'av':16,},{'xy':[[1,1],],'p':0.8596,'wl':0.2811,'ssM':0.33,'wlRad':0.0172,'sRad':0.08,'v':18,'av':18,},{'xy':[[3,0],],'p':0.2750,'wl':0.2846,'ssM':0.50,'wlRad':0.0140,'sRad':0.69,'v':43,'av':43,},{'move':'pass','p':0.6732,'wl':0.8323,'ssM':-0.01,'wlRad':0.0254,'sRad':0.58,'v':15,'av':15,},{'xy':[[3,2],],'p':0.4689,'wl':0.7452,'ssM':1.54,'wlRad':0.0221,'sRad':0.97,'v':86,'av':86,},{'xy':[[1,2],],'p':0.8680,'wl':0.8827,'ssM':0.70,'wlRad':0.1663,'sRad':1.85,'v':23,'av':23,},{'move':'other','p':0.1109,'wl':-0.2748,'ssM':-0.37,'wlRad':0.0784,'sRad':0.77,'v':15,'av':15,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
