
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 0;
const board = [2,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,1],[1,3],],'p':0.1103,'wl':-0.5593,'ssM':0.17,'wlRad':0.1044,'sRad':0.91,'v':93,'av':93,},{'xy':[[2,1],[1,2],],'p':0.4487,'wl':-0.3958,'ssM':0.01,'wlRad':0.0443,'sRad':0.67,'v':59,'av':59,},{'xy':[[3,2],[2,3],],'p':0.2871,'wl':0.5340,'ssM':0.59,'wlRad':0.0246,'sRad':0.93,'v':32,'av':32,},{'xy':[[3,0],[0,3],],'p':0.4524,'wl':0.7639,'ssM':0.25,'wlRad':0.0823,'sRad':1.07,'v':33,'av':33,},{'move':'other','p':0.1406,'wl':0.5354,'ssM':1.00,'wlRad':0.4395,'sRad':1.30,'v':11,'av':11,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
