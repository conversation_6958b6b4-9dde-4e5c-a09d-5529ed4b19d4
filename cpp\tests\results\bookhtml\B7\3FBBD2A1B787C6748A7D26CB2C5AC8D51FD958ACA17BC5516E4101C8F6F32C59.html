
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 2;
const board = [0,0,0,0,0,0,1,0,0,2,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,2],[1,1],],'p':0.1418,'wl':-0.8025,'ssM':1.08,'wlRad':0.2796,'sRad':1.25,'v':145,'av':145,},{'xy':[[3,2],[1,0],],'p':0.3420,'wl':0.0612,'ssM':-0.80,'wlRad':0.0939,'sRad':0.62,'v':85,'av':85,},{'xy':[[0,3],],'p':0.2511,'wl':0.2400,'ssM':-0.10,'wlRad':0.1806,'sRad':1.88,'v':35,'av':35,},{'move':'other','p':0.0286,'wl':-0.6618,'ssM':0.20,'wlRad':0.6382,'sRad':0.66,'v':18,'av':18,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
