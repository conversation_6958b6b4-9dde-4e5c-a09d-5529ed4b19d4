
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 2;
const pLink = '../1C/0DD58B411C0829D8036A6E048FE73BB7A9CC0CF82C071821A9F7D88E9D459E20.html';
const pSym = 6;
const board = [0,0,0,0,0,1,0,0,2,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,0],],'p':0.1061,'wl':0.0699,'ssM':-0.94,'wlRad':0.0101,'sRad':0.89,'v':18,'av':18,},{'xy':[[2,3],],'p':0.1734,'wl':-0.4813,'ssM':-0.81,'wlRad':0.0594,'sRad':1.25,'v':18,'av':18,},{'xy':[[1,2],],'p':0.6698,'wl':-0.6517,'ssM':0.34,'wlRad':0.3772,'sRad':1.39,'v':14,'av':14,},{'xy':[[2,1],],'p':0.6836,'wl':-0.6190,'ssM':-1.51,'wlRad':0.1898,'sRad':1.98,'v':157,'av':157,},{'move':'pass','p':0.0278,'wl':-0.9373,'ssM':-0.70,'wlRad':0.0715,'sRad':1.27,'v':18,'av':18,},{'move':'other','p':0.1618,'wl':0.9279,'ssM':1.21,'wlRad':0.0225,'sRad':0.99,'v':15,'av':15,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
