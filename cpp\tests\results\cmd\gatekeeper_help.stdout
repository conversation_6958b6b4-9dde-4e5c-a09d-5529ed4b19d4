
DESCRIPTION: 

   Test neural nets to see if they should be accepted for self-play
   training data generation.

USAGE: 

   gatekeeper  -config <FILE> ...  [-override-config <KEYVALUEPAIRS>] ... 
               -test-models-dir <DIR> -sgf-output-dir <DIR>
               -accepted-models-dir <DIR> -rejected-models-dir <DIR>
               [-selfplay-dir <DIR>] [-required-candidate-win-prop <PROP>]
               [...other flags...]


Where: 

   -config <FILE>  (accepted multiple times)
     (required)  Config file(s) to use, can be one or multiple files.

   -override-config <KEYVALUEPAIRS>  (accepted multiple times)
     Override config parameters. Format: "key=value, key=value,..."

   -test-models-dir <DIR>
     (required)  Dir to poll and load models from

   -sgf-output-dir <DIR>
     (required)  Dir to output sgf files

   -accepted-models-dir <DIR>
     (required)  Dir to write good models to

   -rejected-models-dir <DIR>
     (required)  Dir to write bad models to

   -selfplay-dir <DIR>
     Dir where selfplay data will be produced if a model passes

   -required-candidate-win-prop <PROP>
     Required win prop to accept

   -no-autoreject-old-models
     Test older models than the latest accepted model

   -quit-if-no-nets-to-test
     Terminate instead of waiting for a new net to test

   --,  -ignore_rest
     Ignores the rest of the labeled arguments following this flag.

   -version
     Displays version information and exits.

   -h,  -help
     Displays usage information and exits.



