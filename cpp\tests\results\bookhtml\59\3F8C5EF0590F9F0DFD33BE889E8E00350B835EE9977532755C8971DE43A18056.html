
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 2;
const board = [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,0],[0,1],],'p':0.4273,'wl':-0.6313,'ssM':-0.23,'wlRad':0.0335,'sRad':1.41,'v':99,'av':99,},{'xy':[[2,1],[1,2],],'p':0.9666,'wl':0.1990,'ssM':0.59,'wlRad':0.0118,'sRad':0.34,'v':93,'av':93,},{'xy':[[3,2],[2,3],],'p':0.9280,'wl':0.6122,'ssM':0.28,'wlRad':0.0542,'sRad':0.99,'v':71,'av':71,},{'xy':[[3,1],[1,3],],'p':0.0006,'wl':0.6964,'ssM':0.78,'wlRad':0.0200,'sRad':0.85,'v':166,'av':166,},{'move':'other','p':0.0660,'wl':0.1469,'ssM':-0.90,'wlRad':0.0130,'sRad':2.08,'v':18,'av':18,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
