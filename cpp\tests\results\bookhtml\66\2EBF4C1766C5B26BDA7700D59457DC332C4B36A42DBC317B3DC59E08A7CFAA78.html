
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 5;
const board = [0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,1,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[2,0],],'p':0.2951,'wl':-0.5062,'ssM':0.78,'wlRad':0.0604,'sRad':1.06,'v':33,'av':33,},{'xy':[[2,1],],'p':0.0568,'wl':0.0701,'ssM':0.39,'wlRad':0.0417,'sRad':0.60,'v':153,'av':153,},{'xy':[[3,0],],'p':0.7789,'wl':0.5724,'ssM':0.11,'wlRad':0.2453,'sRad':0.63,'v':48,'av':48,},{'xy':[[1,3],],'p':0.3390,'wl':0.9578,'ssM':0.11,'wlRad':0.1422,'sRad':0.49,'v':60,'av':60,},{'move':'other','p':0.1694,'wl':0.0122,'ssM':-0.20,'wlRad':0.0642,'sRad':0.59,'v':20,'av':20,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
