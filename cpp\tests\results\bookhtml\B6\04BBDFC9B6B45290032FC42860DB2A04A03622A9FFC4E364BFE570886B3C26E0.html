
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 7;
const board = [0,0,2,0,0,0,0,0,0,0,1,0,0,0,0,0,];
const links = {13:'../0A/0ECCECCB0A81BDB622B142764A9BB9EAB5995B73517F04D18DA0EB8F444FF807.html',};
const linkSyms = {13:3,};
const moves = [{'xy':[[0,0],],'p':0.5817,'wl':-0.7809,'ssM':0.47,'wlRad':0.1402,'sRad':2.03,'v':70,'av':70,},{'xy':[[3,3],],'p':0.1020,'wl':0.3226,'ssM':0.64,'wlRad':0.0654,'sRad':0.55,'v':113,'av':113,},{'xy':[[1,1],],'p':0.1848,'wl':0.5469,'ssM':-0.27,'wlRad':0.0173,'sRad':1.07,'v':93,'av':93,},{'xy':[[2,1],],'p':0.7460,'wl':0.6195,'ssM':-0.09,'wlRad':0.0378,'sRad':1.62,'v':30,'av':30,},{'xy':[[3,0],],'p':0.4776,'wl':0.7718,'ssM':0.24,'wlRad':0.0144,'sRad':1.06,'v':163,'av':163,},{'xy':[[1,3],],'p':0.9240,'wl':0.9102,'ssM':-0.27,'wlRad':0.2452,'sRad':1.00,'v':233,'av':233,},{'move':'other','p':0.0162,'wl':-0.6642,'ssM':-0.35,'wlRad':0.0181,'sRad':0.59,'v':19,'av':19,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
