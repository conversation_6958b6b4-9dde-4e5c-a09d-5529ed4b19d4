
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../CC/2A4B2282CC19AA3054FB86CA301A87405FD80ACF0E31F0F9989F10B18154627A.html';
const pSym = 1;
const board = [2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[3,2],[2,3],],'p':0.1664,'wl':-0.1229,'ssM':-0.02,'wlRad':0.2538,'sRad':0.44,'v':68,'av':68,},{'move':'pass','p':0.0871,'wl':0.0698,'ssM':0.06,'wlRad':0.0045,'sRad':0.63,'v':102,'av':102,},{'xy':[[1,0],[0,1],],'p':0.6217,'wl':0.1597,'ssM':0.41,'wlRad':0.1024,'sRad':0.64,'v':91,'av':91,},{'xy':[[2,0],[0,2],],'p':0.6305,'wl':0.8346,'ssM':0.32,'wlRad':0.0380,'sRad':0.52,'v':119,'av':119,},{'move':'other','p':0.1805,'wl':-0.9641,'ssM':-0.53,'wlRad':0.2278,'sRad':0.74,'v':12,'av':12,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
