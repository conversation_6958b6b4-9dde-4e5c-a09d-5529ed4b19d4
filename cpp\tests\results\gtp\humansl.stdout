= Q16

= D16

= R4

= D4

= F17

= F16

= G16

= E17

= F18

= F15

= symmetry 0
whiteWin 0.499233
whiteLoss 0.500747
noResult 0.000020
whiteScore 1.098
whiteScoreSq 1848.614
shorttermWinlossError 0.236
shorttermScoreError 2.793
policy
0.000001 0.000001 0.002207 0.001344 0.000117 0.000027 0.000003 0.000001 0.000001 0.000001 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000001 
0.000000 0.000003 0.002995 0.038484 0.082295     NAN 0.000025 0.000473 0.000127 0.000084 0.000018 0.000007 0.000004 0.000002 0.000005 0.000006 0.000005 0.000001 0.000000 
0.000000 0.000094 0.028777 0.000477     NAN     NAN 0.002693 0.049972 0.030025 0.054604 0.018682 0.004235 0.002596 0.001591 0.000032 0.000027 0.000154 0.000003 0.000000 
0.000000 0.001099 0.000747     NAN 0.000036     NAN     NAN 0.037254 0.020958 0.057100 0.012004 0.003399 0.000802 0.000157 0.000003     NAN 0.000010 0.000001 0.000000 
0.000000 0.000488 0.006365 0.000456 0.000558     NAN 0.434555 0.002988 0.001429 0.000802 0.000110 0.000043 0.000019 0.000007 0.000004 0.000002 0.000009 0.000001 0.000000 
0.000000 0.000065 0.023436 0.004075 0.000207 0.000111 0.000611 0.000734 0.000040 0.000016 0.000005 0.000004 0.000003 0.000002 0.000004 0.000040 0.000296 0.000001 0.000000 
0.000000 0.000008 0.004667 0.002383 0.000060 0.000052 0.000035 0.000015 0.000003 0.000003 0.000001 0.000001 0.000001 0.000001 0.000004 0.000078 0.000203 0.000000 0.000000 
0.000000 0.000004 0.001455 0.000794 0.000013 0.000008 0.000003 0.000002 0.000001 0.000002 0.000001 0.000001 0.000001 0.000001 0.000002 0.000025 0.000049 0.000000 0.000000 
0.000000 0.000003 0.001850 0.000537 0.000005 0.000001 0.000001 0.000001 0.000001 0.000003 0.000001 0.000001 0.000001 0.000001 0.000003 0.000062 0.000120 0.000000 0.000000 
0.000000 0.000006 0.007724 0.008910 0.000027 0.000002 0.000001 0.000002 0.000003 0.000164 0.000003 0.000001 0.000001 0.000002 0.000018 0.003110 0.001190 0.000001 0.000000 
0.000000 0.000002 0.000778 0.000176 0.000003 0.000001 0.000000 0.000001 0.000001 0.000004 0.000001 0.000000 0.000000 0.000001 0.000002 0.000073 0.000081 0.000000 0.000000 
0.000000 0.000001 0.000106 0.000027 0.000001 0.000001 0.000001 0.000001 0.000000 0.000001 0.000000 0.000000 0.000000 0.000001 0.000001 0.000024 0.000023 0.000001 0.000000 
0.000000 0.000001 0.000461 0.000123 0.000003 0.000001 0.000001 0.000000 0.000000 0.000001 0.000000 0.000000 0.000001 0.000001 0.000002 0.000034 0.000056 0.000001 0.000000 
0.000000 0.000006 0.010174 0.000908 0.000012 0.000003 0.000001 0.000000 0.000000 0.000001 0.000000 0.000000 0.000001 0.000001 0.000003 0.000023 0.000004 0.000001 0.000000 
0.000000 0.000011 0.000302 0.000030 0.000027 0.000014 0.000002 0.000001 0.000001 0.000003 0.000001 0.000001 0.000002 0.000009 0.000020 0.000003 0.000002 0.000000 0.000000 
0.000000 0.000074 0.000093     NAN 0.000019 0.000642 0.000082 0.000015 0.000029 0.000796 0.000052 0.000021 0.000077 0.001143 0.000508 0.000002     NAN 0.000000 0.000000 
0.000000 0.000003 0.001182 0.000046 0.000191 0.006948 0.000201 0.000024 0.000061 0.000396 0.000111 0.000048 0.000128 0.002391 0.004162 0.000020 0.000001 0.000000 0.000000 
0.000000 0.000001 0.000002 0.000036 0.000004 0.000005 0.000001 0.000001 0.000000 0.000000 0.000000 0.000000 0.000001 0.000002 0.000004 0.000002 0.000001 0.000001 0.000000 
0.000001 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000 0.000001 
policyPass 0.000000 
whiteOwnership
0.4154044 0.3694948 0.2812435 0.1898008 -0.1756298 -0.4097241 -0.6210949 -0.6416556 -0.5651127 -0.4386993 -0.2546560 -0.1314511 -0.1093722 -0.1669770 -0.2366379 -0.2253147 -0.2601000 -0.2643444 -0.2715763 
0.4472943 0.4812244 0.4946894 0.4773015 0.2735972 -0.8360478 -0.6757309 -0.6735216 -0.5478646 -0.4317169 -0.2613530 -0.1439215 -0.1216405 -0.1536681 -0.3084047 -0.2974414 -0.2937996 -0.2631847 -0.2507630 
0.4664535 0.5206537 0.6184344 0.6400264 0.9091853 -0.7802985 -0.7280337 -0.6373661 -0.5469217 -0.4521292 -0.2843708 -0.1844527 -0.1331074 -0.1995777 -0.4390736 -0.5046462 -0.3726046 -0.2855491 -0.2405170 
0.4396652 0.5528440 0.6410068 0.9414414 0.8917020 0.9158655 -0.8063740 -0.5960585 -0.4443561 -0.3622707 -0.2166971 -0.1362303 -0.1075881 -0.2168387 -0.3865197 -0.8979436 -0.4942848 -0.2930181 -0.2306217 
0.3718322 0.4344789 0.5746042 0.6522347 0.7244678 0.8720480 -0.2740866 -0.2623417 -0.2849178 -0.2204053 -0.1353328 -0.0979690 -0.0786562 -0.1346910 -0.2840072 -0.3879333 -0.4823289 -0.3031400 -0.2463853 
0.2615246 0.3100799 0.3715608 0.5001083 0.5527735 0.4248485 0.1549190 -0.1270198 -0.1374665 -0.0821598 -0.0433455 -0.0096848 -0.0013435 -0.0519039 -0.1457208 -0.2407871 -0.2126787 -0.1930908 -0.2083794 
0.1366894 0.1666445 0.2693611 0.3519797 0.3708759 0.2966305 0.1224809 -0.0235076 -0.0479258 -0.0123230 0.0011029 0.0011471 -0.0119247 -0.0207526 -0.0774073 -0.1207190 -0.1652384 -0.1102725 -0.1103510 
0.0609354 0.0949199 0.1669336 0.2313694 0.2649744 0.2347273 0.1271402 0.0236768 0.0044397 0.0084857 0.0027682 -0.0060114 -0.0210175 -0.0079695 -0.0547877 -0.0757897 -0.1015076 -0.0554129 0.0018054 
0.0102672 0.0583127 0.0854024 0.1146389 0.1457039 0.1294756 0.0782008 0.0267221 0.0171307 0.0096103 0.0012237 -0.0042471 -0.0130205 0.0166370 0.0093652 -0.0151487 -0.0348684 -0.0005879 0.0450298 
-0.0185550 0.0272129 0.0170386 0.1056751 0.0577644 0.0567897 0.0324729 -0.0037335 0.0058966 0.0186472 0.0038752 -0.0077271 -0.0096830 0.0130964 -0.0116370 -0.0641726 -0.0386943 -0.0464016 0.0112212 
-0.0248896 0.0305773 0.0371879 0.0400484 0.0578063 0.0360200 0.0152892 -0.0069570 -0.0085588 -0.0025685 -0.0201249 -0.0093651 -0.0167195 0.0057151 -0.0047913 -0.0570493 -0.0740789 -0.0690217 -0.0155121 
-0.0206780 0.0146335 0.0901055 0.0775824 0.0624284 0.0340135 0.0121247 -0.0124814 -0.0258417 -0.0146263 -0.0217515 -0.0213425 -0.0277634 -0.0067872 -0.0295070 -0.1117018 -0.1489333 -0.1486602 -0.0959490 
0.0218371 0.0317125 0.1346233 0.1445650 0.1152644 0.0635806 0.0303846 0.0064527 -0.0130354 0.0003808 -0.0190558 -0.0288904 -0.0337709 -0.0019033 -0.0173022 -0.1456053 -0.2144511 -0.2198835 -0.2099686 
0.0988753 0.0758992 0.1469400 0.2484623 0.1840528 0.0969943 0.0585671 0.0298768 0.0063907 0.0105817 -0.0056172 -0.0115039 -0.0052323 0.0228598 -0.0142201 -0.2065626 -0.2886380 -0.3258153 -0.3169296 
0.1277209 0.1732938 0.3224241 0.3672411 0.2724277 0.1745587 0.1108634 0.0653825 0.0338341 0.0228483 0.0256290 0.0392826 0.0345262 0.0186060 -0.0698942 -0.2510438 -0.4063794 -0.5141323 -0.4527270 
0.1409923 0.2046795 0.4246517 0.8886530 0.3617839 0.1954509 0.1442430 0.0666453 0.0219268 0.0161960 0.0172130 0.0455542 0.0319075 0.0189829 -0.0864766 -0.3330906 -0.9059920 -0.6710891 -0.5559928 
0.1363705 0.1927463 0.2793121 0.4063359 0.3629241 0.1134730 0.1608306 0.0875443 0.0204488 -0.0076503 0.0107437 0.0654909 0.0175373 -0.0629755 -0.1776553 -0.4049745 -0.5225939 -0.6060084 -0.5736755 
0.1769083 0.1553447 0.2164454 0.2194278 0.1888992 0.0994612 0.0712061 0.0341133 0.0123989 -0.0080449 -0.0024268 0.0299050 0.0140132 -0.0804372 -0.1732652 -0.3272057 -0.4675705 -0.5340660 -0.5650671 
0.2079775 0.1772159 0.1668886 0.1635177 0.1686190 0.1211975 0.0785596 0.0311163 -0.0007647 -0.0073765 0.0140151 0.0324351 0.0129053 -0.0684741 -0.1544852 -0.3148718 -0.4033215 -0.4870633 -0.5389509

= {"allowResignation":"true","analysisIgnorePreRootHistory":"false","analysisWideRootNoise":"0.04","antiMirror":"false","chosenMovePrune":0.0,"chosenMoveSubtract":0.0,"chosenMoveTemperature":0.7,"chosenMoveTemperatureEarly":0.85,"chosenMoveTemperatureHalflife":80.0,"chosenMoveTemperatureOnlyBelowProb":0.01,"conservativePass":true,"cpuctExploration":1.0,"cpuctExplorationBase":500.0,"cpuctExplorationLog":0.45,"cpuctUtilityStdevPrior":0.4,"cpuctUtilityStdevPriorWeight":2.0,"cpuctUtilityStdevScale":0.85,"delayMoveMax":"10","delayMoveScale":"2","drawEquivalentWinsForWhite":0.5,"dynamicScoreCenterScale":0.75,"dynamicScoreCenterZeroWeight":0.2,"dynamicScoreUtilityFactor":0.0,"enableMorePassingHacks":true,"enablePassingHacks":true,"endgameTurnTimeDecay":100.0,"fillDameBeforePass":true,"fpuLossProp":0.0,"fpuParentWeight":0.0,"fpuParentWeightByVisitedPolicy":true,"fpuParentWeightByVisitedPolicyPow":2.0,"fpuReductionMax":0.2,"futileVisitsThreshold":0.0,"genmoveAntiMirror":"true","graphSearchCatchUpLeakProb":0.0,"graphSearchRepBound":11,"humanSLChosenMoveIgnorePass":true,"humanSLChosenMovePiklLambda":100000000.0,"humanSLChosenMoveProp":1.0,"humanSLCpuctExploration":0.5,"humanSLCpuctPermanent":0.2,"humanSLOppExploreProbWeightful":0.0,"humanSLOppExploreProbWeightless":0.0,"humanSLPlaExploreProbWeightful":0.0,"humanSLPlaExploreProbWeightless":0.0,"humanSLProfile":"preaz_12k","humanSLRootExploreProbWeightful":0.0,"humanSLRootExploreProbWeightless":0.0,"ignoreAllHistory":false,"ignorePreRootHistory":false,"lagBuffer":1.0,"lcbStdevs":5.0,"maxPlayouts":1125899906842624,"maxPlayoutsPondering":1125899906842624,"maxTime":1e+20,"maxTimePondering":1e+20,"maxVisits":40,"maxVisitsPondering":1125899906842624,"midgameTimeFactor":1.0,"midgameTurnPeakTime":130.0,"minPlayoutsPerThread":8.0,"minVisitPropForLCB":0.15,"nnPolicyTemperature":1.0,"noResultUtilityForWhite":0.0,"noisePruneUtilityScale":0.15,"noisePruningCap":1e+50,"numSearchThreads":1,"numVirtualLossesPerThread":1.0,"obviousMovesPolicyEntropyTolerance":0.3,"obviousMovesPolicySurpriseTolerance":0.15,"obviousMovesTimeFactor":1.0,"overallocateTimeFactor":1.0,"playoutDoublingAdvantage":0.0,"playoutDoublingAdvantagePla":"E","policyOptimism":1.0,"ponderingEnabled":"false","rootDesiredPerChildVisitsCoeff":0.0,"rootDirichletNoiseTotalConcentration":10.83,"rootDirichletNoiseWeight":0.25,"rootEndingBonusPoints":0.5,"rootFpuLossProp":0.0,"rootFpuReductionMax":0.1,"rootNoiseEnabled":false,"rootNumSymmetriesToSample":2,"rootPolicyOptimism":0.2,"rootPolicyTemperature":1.0,"rootPolicyTemperatureEarly":1.0,"rootPruneUselessMoves":true,"rootSymmetryPruning":true,"searchFactorAfterOnePass":1.0,"searchFactorAfterTwoPass":1.0,"staticScoreUtilityFactor":0.3,"subtreeValueBiasFactor":0.0,"subtreeValueBiasFreeProp":0.8,"subtreeValueBiasTableNumShards":65536,"subtreeValueBiasWeightExponent":0.85,"treeReuseCarryOverTimeFactor":0.0,"uncertaintyCoeff":0.25,"uncertaintyExponent":1.0,"uncertaintyMaxWeight":8.0,"useGraphSearch":true,"useLcbForSelection":false,"useNoisePruning":false,"useNonBuggyLcb":true,"useUncertainty":false,"valueWeightExponent":0.25,"wideRootNoise":0.0,"winLossUtilityFactor":1.0}

= preaz_12k

= 

= E18

= C17

= G15

= G14

= H14

= H13

= J13

= G13

= preaz_5d

