: Running with following config:
allowResignation = true
friendlyPassOk = false
hasButton = false
koRule = POSITIONAL
lagBuffer = 1.0
logAllGTPCommunication = true
logDir = gtp_logs
logSearchInfo = true
logSearchInfoForChosenMove = false
logToStderr = false
maxPlayouts = 500
maxTime = 1
maxTimePondering = 1
maxVisits = 500
multiStoneSuicideLegal = true
nnCacheSizePowerOfTwo = 12
nnMutexPoolSizePowerOfTwo = 10
numSearchThreads = 3
ponderingEnabled = true
resignConsecTurns = 3
resignThreshold = -0.90
scoringRule = AREA
searchFactorAfterOnePass = 0.50
searchFactorAfterTwoPass = 0.25
searchFactorWhenWinning = 0.40
searchFactorWhenWinningThreshold = 0.95
taxRule = NONE
whiteHandicapBonus = 0

: Running tiny net to sanity-check that GPU is working
: After dedups: nnModelFile0 = tests/scratch/tmpTinyModel.bin.gz useFP16 auto useNHWC auto
: Initializing neural net buffer to be size 19 * 19 allowing smaller boards
: Cuda backend thread 0: Found GPU NVIDIA RTX A5000 memory 25425608704 compute capability major 8 minor 6
: Cuda backend thread 0: Model version 9 useFP16 = true useNHWC = true
: Cuda backend thread 0: Model name: rect15-b2c16-s13679744-d94886722
: After dedups: nnModelFile0 = tests/scratch/tmpTinyMishModel.bin.gz useFP16 auto useNHWC auto
: Initializing neural net buffer to be size 19 * 19 allowing smaller boards
: Cuda backend thread 0: Found GPU NVIDIA RTX A5000 memory 25425608704 compute capability major 8 minor 6
: Cuda backend thread 0: Model version 11 useFP16 = true useNHWC = true
: Cuda backend thread 0: Model name: b1c6nbt
: After dedups: nnModelFile0 = tests/scratch/tmpTinyMishModel.bin.gz useFP16 auto useNHWC auto
: Initializing neural net buffer to be size 19 * 19 allowing smaller boards
: Cuda backend thread 0: Found GPU NVIDIA RTX A5000 memory 25425608704 compute capability major 8 minor 6
: Cuda backend thread 0: Model version 11 useFP16 = true useNHWC = true
: Cuda backend thread 0: Model name: b1c6nbt
: Tiny net sanity check complete
