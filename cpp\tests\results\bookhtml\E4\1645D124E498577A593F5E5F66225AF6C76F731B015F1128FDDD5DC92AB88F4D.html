
<html>
<header>
<link rel="stylesheet" href="../book.css">
<script>
const nextPla = 1;
const pLink = '../96/0971EF5E9671A796679C44F5D33A7F2091A1B80BE1AC64D34EDF70D6F57F797E.html';
const pSym = 6;
const board = [0,0,0,0,0,2,0,0,0,1,0,0,0,0,0,0,];
const links = {};
const linkSyms = {};
const moves = [{'xy':[[1,3],],'p':0.1254,'wl':0.0274,'ssM':0.27,'wlRad':0.0733,'sRad':0.37,'v':102,'av':102,},{'xy':[[2,0],],'p':0.7580,'wl':0.0472,'ssM':0.38,'wlRad':0.0860,'sRad':0.68,'v':192,'av':192,},{'xy':[[3,1],],'p':0.1537,'wl':0.4305,'ssM':-0.21,'wlRad':0.0494,'sRad':1.32,'v':94,'av':94,},{'move':'other','p':0.1288,'wl':-0.9201,'ssM':-0.53,'wlRad':0.0464,'sRad':1.17,'v':10,'av':10,},];

</script>
<script type="text/javascript" src="../book.js"></script>
</header>
<body>
</body>
</html>
